# Everchess Landing Page Integration Guide

## Overview

This guide provides step-by-step instructions for implementing the exact UI-UX Foundation Reference landing page design in React Native + Expo. The landing page serves as the entry point for new users and showcases all key features of Everchess.

## Design Analysis

### Key Visual Elements
- **Dark theme** with gradient accents and rainbow animations
- **Hero section** with animated logo and call-to-action buttons
- **Feature sections** highlighting 3D gameplay, battlepass, and Web3 integration
- **Interactive cards** with hover effects and visual previews
- **Responsive layout** that adapts to different screen sizes

### Color Palette
```typescript
// theme/landingColors.ts
export const landingColors = {
  // Background Colors
  background: '#0b0b0e',
  backgroundCard: '#141414',
  backgroundElevated: '#1a1a1a',
  
  // Primary Colors
  primary: '#5856D6',
  red: '#dc2626',
  cyan: '#00b6ff',
  yellow: '#ffd432',
  
  // Text Colors
  textPrimary: '#ffffff',
  textSecondary: '#b3b3b3',
  textMuted: '#888888',
  
  // Rainbow Gradient
  rainbow: [
    '#ff5e00', '#ffbb00', '#00ff95', 
    '#00b8ff', '#8c00ff', '#ff00c8', '#ff0055'
  ],
};
```

## Implementation Strategy

### 1. Screen Structure
```typescript
// screens/LandingScreen.tsx
import React from 'react';
import { ScrollView, SafeAreaView } from 'react-native';
import { HeroSection } from '../components/landing/HeroSection';
import { FeaturesSection } from '../components/landing/FeaturesSection';
import { ChessSetsSection } from '../components/landing/ChessSetsSection';
import { BattlepassSection } from '../components/landing/BattlepassSection';
import { CommunitySection } from '../components/landing/CommunitySection';
import { WinToEarnSection } from '../components/landing/WinToEarnSection';
import { Web3Section } from '../components/landing/Web3Section';
import { ReadyToPlaySection } from '../components/landing/ReadyToPlaySection';
import { LandingHeader } from '../components/landing/LandingHeader';

export const LandingScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <LandingHeader />
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <HeroSection />
        <FeaturesSection />
        <ChessSetsSection />
        <BattlepassSection />
        <CommunitySection />
        <WinToEarnSection />
        <Web3Section />
        <ReadyToPlaySection />
      </ScrollView>
    </SafeAreaView>
  );
};
```

### 2. Animated Logo Component
```typescript
// components/landing/AnimatedLogo.tsx
import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';

interface AnimatedLogoProps {
  size: number;
  showRainbow?: boolean;
}

export const AnimatedLogo: React.FC<AnimatedLogoProps> = ({ 
  size, 
  showRainbow = true 
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (showRainbow) {
      const rotation = Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 8000,
          useNativeDriver: true,
        })
      );
      rotation.start();
      return () => rotation.stop();
    }
  }, [showRainbow]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {showRainbow && (
        <Animated.View 
          style={[
            styles.rainbowBorder, 
            { transform: [{ rotate }] }
          ]}
        >
          <LinearGradient
            colors={[
              '#ff5e00', '#ffbb00', '#00ff95', 
              '#00b8ff', '#8c00ff', '#ff00c8', '#ff0055'
            ]}
            style={StyleSheet.absoluteFill}
          />
        </Animated.View>
      )}
      <View style={[styles.logoContainer, { margin: showRainbow ? 4 : 0 }]}>
        <Image
          source={require('../../assets/logo.png')}
          style={[styles.logo, { width: size - 8, height: size - 8 }]}
          contentFit="contain"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
  },
  rainbowBorder: {
    position: 'absolute',
    inset: -20,
    borderRadius: 12,
  },
  logoContainer: {
    backgroundColor: '#000000',
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    zIndex: 10,
  },
});
```

### 3. Hero Section Implementation
```typescript
// components/landing/HeroSection.tsx
import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Button } from '../ui/Button';
import { AnimatedLogo } from './AnimatedLogo';
import { ChevronRight, Gamepad2 } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');
const isTablet = width >= 768;

export const HeroSection: React.FC = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.textSection}>
          <Text style={styles.title}>ANCIENT MADE MODERN</Text>
          <Text style={styles.subtitle}>A Gamified Chess Experience.</Text>
          <Text style={styles.description}>
            Experience chess like never before. Everchess is a next-gen gamified 
            chess app that combines traditional gameplay with modern features. 
            Available soon on Web, iOS, and Android.
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Get Started"
              variant="outline"
              onPress={() => navigation.navigate('Auth')}
              style={styles.getStartedButton}
              icon={<ChevronRight size={16} color="#ffffff" />}
            />
            <Button
              title="Play Now"
              variant="primary"
              onPress={() => navigation.navigate('Auth')}
              style={styles.playButton}
              icon={<Gamepad2 size={20} color="#ffffff" />}
            />
          </View>
        </View>
        
        <View style={styles.logoSection}>
          <AnimatedLogo size={isTablet ? 320 : 280} showRainbow />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: isTablet ? 64 : 32,
    backgroundColor: '#0b0b0e',
  },
  content: {
    flexDirection: isTablet ? 'row' : 'column',
    alignItems: 'center',
    gap: isTablet ? 48 : 32,
  },
  textSection: {
    flex: isTablet ? 1 : undefined,
    alignItems: isTablet ? 'flex-start' : 'center',
  },
  title: {
    fontSize: isTablet ? 48 : 32,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: isTablet ? 'left' : 'center',
    marginBottom: 8,
    fontFamily: 'Sora_700Bold',
  },
  subtitle: {
    fontSize: isTablet ? 36 : 24,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: isTablet ? 'left' : 'center',
    marginBottom: 16,
    fontFamily: 'Sora_700Bold',
  },
  description: {
    fontSize: 16,
    color: '#b3b3b3',
    textAlign: isTablet ? 'left' : 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 600,
    fontFamily: 'Lora_400Regular',
  },
  buttonContainer: {
    flexDirection: isTablet ? 'row' : 'column',
    gap: 12,
    width: isTablet ? undefined : '100%',
  },
  getStartedButton: {
    borderColor: '#ffd432',
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  playButton: {
    backgroundColor: '#dc2626',
  },
  logoSection: {
    flex: isTablet ? 1 : undefined,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
```

### 4. Feature Cards Component
```typescript
// components/landing/FeatureCard.tsx
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  accentColor: string;
  onPress?: () => void;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  accentColor,
  onPress,
}) => {
  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        <View style={[styles.iconContainer, { backgroundColor: `${accentColor}20` }]}>
          {icon}
        </View>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: 200,
    margin: 8,
    borderRadius: 12,
    backgroundColor: '#141414',
    borderWidth: 1,
    borderColor: 'rgba(75, 85, 99, 0.3)',
  },
  content: {
    padding: 20,
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Sora_600SemiBold',
  },
  description: {
    fontSize: 14,
    color: '#b3b3b3',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: 'Lora_400Regular',
  },
});
```

### 5. Interactive Preview Cards
```typescript
// components/landing/PreviewCard.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Image } from 'expo-image';

interface PreviewCardProps {
  title: string;
  children: React.ReactNode;
  height?: number;
  onPress?: () => void;
}

export const PreviewCard: React.FC<PreviewCardProps> = ({
  title,
  children,
  height = 400,
  onPress,
}) => {
  const [scaleAnim] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      <Animated.View 
        style={[
          styles.container, 
          { height, transform: [{ scale: scaleAnim }] }
        ]}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.content}>
          {children}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#141414',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(75, 85, 99, 0.3)',
    overflow: 'hidden',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(75, 85, 99, 0.2)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_600SemiBold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
});
```

### 6. Responsive Layout System
```typescript
// utils/responsive.ts
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const responsive = {
  // Screen dimensions
  screenWidth: width,
  screenHeight: height,
  
  // Breakpoints
  isSmall: width < 576,
  isMedium: width >= 576 && width < 768,
  isLarge: width >= 768 && width < 992,
  isXLarge: width >= 992,
  
  // Helper functions
  wp: (percentage: number) => (width * percentage) / 100,
  hp: (percentage: number) => (height * percentage) / 100,
  
  // Responsive values
  padding: width >= 768 ? 24 : 16,
  margin: width >= 768 ? 20 : 12,
  fontSize: (base: number) => width >= 768 ? base * 1.2 : base,
  
  // Grid columns
  columns: width >= 992 ? 3 : width >= 768 ? 2 : 1,
};

// Responsive style helper
export const createResponsiveStyle = (styles: any) => {
  return StyleSheet.create(styles);
};
```

### 7. Animation Utilities
```typescript
// utils/animations.ts
import { Animated, Easing } from 'react-native';

export const landingAnimations = {
  // Fade in animation
  fadeIn: (value: Animated.Value, duration = 600) => {
    return Animated.timing(value, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    });
  },

  // Scale in animation
  scaleIn: (value: Animated.Value, duration = 400) => {
    return Animated.timing(value, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.back(1.7)),
      useNativeDriver: true,
    });
  },

  // Slide up animation
  slideUp: (value: Animated.Value, duration = 500) => {
    return Animated.timing(value, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    });
  },

  // Rainbow rotation
  rainbowRotation: (value: Animated.Value) => {
    return Animated.loop(
      Animated.timing(value, {
        toValue: 1,
        duration: 8000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
  },

  // Stagger animation for multiple elements
  staggerAnimation: (
    animations: Animated.CompositeAnimation[],
    staggerDelay = 100
  ) => {
    return Animated.stagger(staggerDelay, animations);
  },
};
```

## Implementation Checklist

### Phase 1: Basic Structure
- [ ] Create LandingScreen component with ScrollView
- [ ] Implement responsive layout system
- [ ] Set up basic navigation between sections
- [ ] Add SafeAreaView for proper spacing

### Phase 2: Core Components
- [ ] Build AnimatedLogo with rainbow border
- [ ] Create HeroSection with responsive text
- [ ] Implement FeatureCard component
- [ ] Add PreviewCard with hover effects

### Phase 3: Content Sections
- [ ] FeaturesSection with 3 feature cards
- [ ] ChessSetsSection with collection preview
- [ ] BattlepassSection with progress visualization
- [ ] CommunitySection with community features

### Phase 4: Advanced Features
- [ ] WinToEarnSection with economy preview
- [ ] Web3Section with Solana branding
- [ ] ReadyToPlaySection with CTA buttons
- [ ] LandingHeader with navigation menu

### Phase 5: Polish & Optimization
- [ ] Add smooth scroll animations
- [ ] Implement intersection observer for section animations
- [ ] Optimize images and assets
- [ ] Test responsive behavior across devices

## Assets Required

### Images
- `logo.png` - Main Everchess logo
- `chess-pawn-blue.png` - Blue chess pawn icon
- `chess-pawn-yellow.png` - Yellow chess pawn icon  
- `chess-pawn-red.png` - Red chess pawn icon
- `solana-new-logo.png` - Solana logo

### Fonts
- **Sora** (Primary): 400, 600, 700 weights
- **Lora** (Secondary): 400, 500 weights

### Icons
- Lucide React Native icons for UI elements
- Custom SVG icons for specific features

## Performance Considerations

### Optimization Strategies
1. **Image Optimization**: Use Expo Image with proper caching
2. **Animation Performance**: Use native driver for all animations
3. **Lazy Loading**: Implement lazy loading for heavy sections
4. **Memory Management**: Properly cleanup animations and listeners

### Bundle Size Management
1. **Tree Shaking**: Import only needed components
2. **Asset Optimization**: Compress images and use WebP format
3. **Code Splitting**: Split landing page from main app bundle

This integration guide provides a comprehensive foundation for implementing the exact UI-UX Foundation Reference landing page in React Native + Expo while maintaining the visual fidelity and interactive elements of the original design.
