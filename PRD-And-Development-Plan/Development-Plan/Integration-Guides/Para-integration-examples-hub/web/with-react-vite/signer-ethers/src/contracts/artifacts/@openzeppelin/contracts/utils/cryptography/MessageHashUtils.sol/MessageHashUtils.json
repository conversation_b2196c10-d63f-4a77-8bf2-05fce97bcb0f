{"_format": "hh-sol-artifact-1", "contractName": "MessageHashUtils", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "abi": [], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220229dad1c21e0c8510b1531cf54c3713bb7b4a8f5f9dc4cd339a76e8551dbcc8864736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220229dad1c21e0c8510b1531cf54c3713bb7b4a8f5f9dc4cd339a76e8551dbcc8864736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}