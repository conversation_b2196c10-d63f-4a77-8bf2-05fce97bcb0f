{"_format": "hh-sol-artifact-1", "contractName": "ECDSA", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "abi": [{"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220ea78b0f46ee7f082d029812a528a5e0e5f3a1c646f4731a18cadc8ee8ad0f14364736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220ea78b0f46ee7f082d029812a528a5e0e5f3a1c646f4731a18cadc8ee8ad0f14364736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}