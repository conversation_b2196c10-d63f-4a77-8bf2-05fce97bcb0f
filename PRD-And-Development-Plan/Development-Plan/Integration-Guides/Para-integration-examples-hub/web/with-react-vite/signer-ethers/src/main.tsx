import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router-dom";
import "./index.css";
import App from "./App";
import { ParaProvider } from "./components/ParaProvider";
import Header from "./components/Header";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ParaProvider>
      <BrowserRouter>
        <Header />
        <main>
          <App />
        </main>
      </BrowserRouter>
    </ParaProvider>
  </StrictMode>
);
