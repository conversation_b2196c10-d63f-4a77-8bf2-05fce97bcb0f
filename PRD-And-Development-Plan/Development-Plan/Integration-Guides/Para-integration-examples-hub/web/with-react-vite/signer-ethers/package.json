{"name": "signer-ethers", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "compile": "hardhat compile", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@getpara/ethers-v6-integration": "1.16.0", "@getpara/react-sdk": "1.16.0", "ethers": "^6.13.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@openzeppelin/contracts": "^5.2.0", "@tailwindcss/vite": "4.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "hardhat": "^2.22.18", "tailwindcss": "4.0.6", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-node-polyfills": "0.23.0"}}