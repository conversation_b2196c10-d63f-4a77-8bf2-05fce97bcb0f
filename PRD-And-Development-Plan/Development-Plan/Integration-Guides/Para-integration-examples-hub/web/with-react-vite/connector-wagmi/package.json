{"name": "connector-wagmi", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@getpara/react-sdk": "1.16.0", "@getpara/wagmi-v2-integration": "1.16.0", "@tanstack/react-query": "5.64.2", "@wagmi/core": "^2.17.0", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.28.0", "wagmi": "^2.15.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/vite": "4.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "tailwindcss": "4.0.6", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-node-polyfills": "0.23.0"}}