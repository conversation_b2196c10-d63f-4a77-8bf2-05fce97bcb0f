{"name": "para-modal-cosmos", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && vite build", "dev": "vite", "postinstall": "yarn graz --generate", "preview": "vite preview"}, "dependencies": {"@cosmjs/cosmwasm-stargate": "0.33.1", "@cosmjs/launchpad": "0.27.1", "@cosmjs/proto-signing": "0.33.1", "@cosmjs/stargate": "0.33.1", "@cosmjs/tendermint-rpc": "0.33.1", "@getpara/cosmos-wallet-connectors": "1.16.0", "@getpara/graz": "0.1.0", "@getpara/react-sdk": "1.16.0", "@leapwallet/cosmos-social-login-capsule-provider": "0.0.44", "long": "5.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "starknet": "6.11.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/vite": "4.0.6", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "tailwindcss": "4.0.6", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-node-polyfills": "0.23.0"}, "resolutions": {"@getpara/react-sdk": "1.16.0"}}