{"name": "para-modal-svelte", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.app.json && tsc -p tsconfig.node.json"}, "dependencies": {"@getpara/react-sdk": "1.16.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.3", "@tsconfig/svelte": "^5.0.4", "svelte": "^5.19.6", "svelte-preprocess-react": "2.1.0", "svelte-check": "^4.1.4", "typescript": "~5.7.2", "vite": "^6.1.0", "vite-plugin-node-polyfills": "0.23.0", "tailwindcss": "4.0.6", "@tailwindcss/vite": "4.0.6"}}