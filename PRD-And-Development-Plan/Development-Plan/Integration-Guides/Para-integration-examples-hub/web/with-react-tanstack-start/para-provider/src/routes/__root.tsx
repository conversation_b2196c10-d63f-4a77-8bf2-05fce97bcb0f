import React from "react";
import { createRoot<PERSON>oute, HeadContent, Outlet, Scrip<PERSON> } from "@tanstack/react-router";
import Providers from "~/components/Providers";
import appCss from "~/styles/app.css?url";

export const Route = createRootRoute({
  head: () => ({
    meta: [{ charSet: "utf-8" }, { name: "viewport", content: "width=device-width, initial-scale=1" }],
    links: [
      { rel: "stylesheet", href: appCss },
      { rel: "icon", href: "/para.svg", type: "image/svg+xml" },
    ],
  }),
  component: RootComponent,
});

function RootComponent() {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <Providers>
          <main>
            <Outlet />
          </main>
        </Providers>
        <Scripts />
      </body>
    </html>
  );
}
