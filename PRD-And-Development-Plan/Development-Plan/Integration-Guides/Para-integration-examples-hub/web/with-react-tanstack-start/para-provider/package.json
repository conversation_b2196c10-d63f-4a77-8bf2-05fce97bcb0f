{"name": "para-provider-tanstack", "version": "1.0.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}, "dependencies": {"@getpara/react-sdk": "1.16.0", "@tanstack/react-query": "5.76.0", "@tanstack/react-router": "^1.120.3", "@tanstack/react-router-devtools": "^1.120.3", "@tanstack/react-start": "^1.120.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "vinxi": "0.5.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "postcss": "^8.5.1", "tailwindcss": "^4.1.6", "typescript": "^5.7.2", "vite-plugin-node-polyfills": "0.23.0", "vite-tsconfig-paths": "^5.1.4"}}