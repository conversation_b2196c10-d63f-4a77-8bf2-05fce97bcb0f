{"_format": "hh-sol-artifact-1", "contractName": "Errors", "sourceName": "@openzeppelin/contracts/utils/Errors.sol", "abi": [{"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "FailedDeployment", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "MissingPrecompile", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212206c21a610320bdb81a15d880442d7ab580488ad70e36c06bd53bf2fb1a64c53df64736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212206c21a610320bdb81a15d880442d7ab580488ad70e36c06bd53bf2fb1a64c53df64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}