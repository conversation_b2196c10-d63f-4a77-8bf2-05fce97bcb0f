{"_format": "hh-sol-artifact-1", "contractName": "ShortStrings", "sourceName": "@openzeppelin/contracts/utils/ShortStrings.sol", "abi": [{"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212209d7b477aa38de15cb42e11ec24c18d89ed6c781a66d5973788c6a9e812bb105064736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212209d7b477aa38de15cb42e11ec24c18d89ed6c781a66d5973788c6a9e812bb105064736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}