{"name": "para-modal-solana-nextjs", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@solana-mobile/wallet-adapter-mobile": "2.1.5", "@solana/wallet-adapter-base": "0.9.24", "@solana/wallet-adapter-react": "0.15.36", "@solana/wallet-adapter-walletconnect": "0.1.17", "@solana/web3.js": "1.98.0", "@tanstack/react-query": "5.71.5", "@getpara/react-sdk": "1.16.0", "@getpara/solana-wallet-connectors": "1.16.0", "next": "15.1.5", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}