{"name": "signer-so<PERSON>-anchor", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "anchor:build": "anchor build", "anchor:deploy": "anchor deploy --program-name transfer_tokens --program-keypair target/deploy/transfer_tokens-keypair.json", "anchor:build-deploy": "npm run anchor:build && npm run anchor:deploy", "anchor:verify": "anchor verify"}, "dependencies": {"@coral-xyz/anchor": "0.31.0", "@getpara/react-sdk": "1.16.0", "@getpara/solana-web3.js-v1-integration": "1.16.0", "@solana/kit": "2.1.0", "@solana/spl-token": "0.4.13", "@solana/web3.js": "1.69.0", "next": "15.1.5", "react": "19.0.0", "react-dom": "19.0.0", "tweetnacl": "1.0.3", "tweetnacl-util": "0.15.1"}, "devDependencies": {"@types/node": "22.10.10", "@types/react": "19", "@types/react-dom": "19", "postcss": "8", "tailwindcss": "3.4.1", "ts-node": "10.9.2", "typescript": "5.7.3"}}