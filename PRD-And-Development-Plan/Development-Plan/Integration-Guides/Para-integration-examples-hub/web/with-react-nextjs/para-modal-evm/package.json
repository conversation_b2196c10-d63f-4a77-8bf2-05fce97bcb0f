{"name": "para-modal-evm-nextjs", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@tanstack/react-query": "^5.64.2", "@getpara/evm-wallet-connectors": "1.16.0", "@getpara/react-sdk": "1.16.0", "next": "15.1.5", "react": "19.0.0", "react-dom": "19.0.0", "wagmi": "^2.14.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "pino-pretty": "13.0.0"}}