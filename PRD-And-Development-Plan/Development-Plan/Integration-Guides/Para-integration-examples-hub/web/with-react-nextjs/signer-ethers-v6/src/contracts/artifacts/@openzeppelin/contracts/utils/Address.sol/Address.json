{"_format": "hh-sol-artifact-1", "contractName": "Address", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "abi": [{"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220a359dadc1532b6361dd3691be316fa4391a52ba3428a26473a83511d2cc3771d64736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220a359dadc1532b6361dd3691be316fa4391a52ba3428a26473a83511d2cc3771d64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}