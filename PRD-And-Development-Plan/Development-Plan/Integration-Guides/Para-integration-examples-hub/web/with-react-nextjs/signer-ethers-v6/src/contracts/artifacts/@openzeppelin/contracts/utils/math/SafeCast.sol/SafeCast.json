{"_format": "hh-sol-artifact-1", "contractName": "SafeCast", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "abi": [{"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "int256", "name": "value", "type": "int256"}], "name": "SafeCastOverflowedIntDowncast", "type": "error"}, {"inputs": [{"internalType": "int256", "name": "value", "type": "int256"}], "name": "SafeCastOverflowedIntToUint", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintToInt", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220316339f60b3fd1858de7f836dab11b96a56ba64a0fc65a8a88172d93a655ae7464736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220316339f60b3fd1858de7f836dab11b96a56ba64a0fc65a8a88172d93a655ae7464736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}