{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220324c39d5297afb136187e0a02fa806c282a2d4d9d4ba345dc57f45522daa24fb64736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220324c39d5297afb136187e0a02fa806c282a2d4d9d4ba345dc57f45522daa24fb64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}