{"_format": "hh-sol-cache-2", "files": {"/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/access/Ownable.sol": {"lastModificationDate": 1737731459595, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"lastModificationDate": 1737731460168, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol": {"lastModificationDate": 1737731460168, "contentHash": "b1a8fc63b83ce00408e0c9ed1230b717", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20Permit.sol", "../ERC20.sol", "../../../utils/cryptography/ECDSA.sol", "../../../utils/cryptography/EIP712.sol", "../../../utils/Nonces.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Permit"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/ERC20.sol": {"lastModificationDate": 1737731460006, "contentHash": "227a6eb2225701c12d9c959b758b6333", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/extensions/ERC20Capped.sol": {"lastModificationDate": 1737731460168, "contentHash": "599b6100008da0cc7875f57044251679", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Capped.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Capped"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/Context.sol": {"lastModificationDate": 1737731459606, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"lastModificationDate": 1737731459606, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/IERC20.sol": {"lastModificationDate": 1737731460006, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"lastModificationDate": 1737731460169, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/Nonces.sol": {"lastModificationDate": 1737731459606, "contentHash": "c32d108058718efb9061b88e83a83f79", "sourceName": "@openzeppelin/contracts/utils/Nonces.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Nonces"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/cryptography/ECDSA.sol": {"lastModificationDate": 1737731460006, "contentHash": "81de029d56aa803972be03c5d277cb6c", "sourceName": "@openzeppelin/contracts/utils/cryptography/ECDSA.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["ECDSA"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/cryptography/EIP712.sol": {"lastModificationDate": 1737731460006, "contentHash": "8dbb261c55f358342798c4d1803d4f8e", "sourceName": "@openzeppelin/contracts/utils/cryptography/EIP712.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./MessageHashUtils.sol", "../ShortStrings.sol", "../../interfaces/IERC5267.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["EIP712"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol": {"lastModificationDate": 1737731460169, "contentHash": "94ec15baf0d5df863f45b8f351937ec7", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Permit"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/interfaces/IERC5267.sol": {"lastModificationDate": 1737731459604, "contentHash": "94364524cb1a39dcbc3d3afff6d8e53e", "sourceName": "@openzeppelin/contracts/interfaces/IERC5267.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC5267"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/ShortStrings.sol": {"lastModificationDate": 1737731459607, "contentHash": "94e7feaf138d08fb736e43ca0be9bf26", "sourceName": "@openzeppelin/contracts/utils/ShortStrings.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./StorageSlot.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ShortStrings"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol": {"lastModificationDate": 1737731460006, "contentHash": "86fd93657e4e27ff76c38699e9b9fcef", "sourceName": "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Strings.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["MessageHashUtils"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/StorageSlot.sol": {"lastModificationDate": 1737731459607, "contentHash": "e656d64c4ce918f3d13030b91c935134", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["StorageSlot"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/Strings.sol": {"lastModificationDate": 1737731459607, "contentHash": "a55fef2557b35bac18a1880d3c2e6740", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./math/Math.sol", "./math/SafeCast.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Strings"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/math/Math.sol": {"lastModificationDate": 1737731460006, "contentHash": "2b2665ae9bdb1af440658741a77fe213", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/math/SafeCast.sol": {"lastModificationDate": 1737731460006, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/math/SignedMath.sol": {"lastModificationDate": 1737731460006, "contentHash": "ae3528afb8bdb0a7dcfba5b115ee8074", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["SignedMath"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/node_modules/@openzeppelin/contracts/utils/Panic.sol": {"lastModificationDate": 1737731459607, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "/Users/<USER>/Documents/GitHub/new-examples-hub/web/react-nextjs/signer-ethers/src/contracts/CapsuleTestToken.sol": {"lastModificationDate": 1737732649518, "contentHash": "fb156a3d347a0e61c4188dfb2132760b", "sourceName": "src/contracts/CapsuleTestToken.sol", "solcConfig": {"version": "0.8.20", "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Capped.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["CapsuleTestToken"]}}}