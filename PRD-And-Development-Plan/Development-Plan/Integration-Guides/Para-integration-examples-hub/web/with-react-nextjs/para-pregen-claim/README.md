# Para Pregen Wallet Example

This project is a simple example of using the Para SDK for creating pregen wallets on the server while claiming them on
the client post user authentication. The example randomly generates a UUID as the identifier for the pregen wallet which
gets used on the server for creating a new wallet. This user share is submitted back to the client where its added tot
he para client so it can be claimed.

## Prerequisites

- **Para API Key**: Obtain your API key from the Para developer portal. Create a `.env.local` file in the project root
  (you can copy `.env.example`) and add your key, prefixing with `NEXT_PUBLIC_` to expose it to the browser:
  ```env
  NEXT_PUBLIC_PARA_API_KEY=your_api_key_here
  ```

## Installation

1.  Install project dependencies using your preferred package manager:
    ```bash
    npm install
    # or
    yarn install
    ```

## Running the Example

1.  Start the Next.js development server:
    ```bash
    npm run dev
    # or
    yarn dev
    ```
2.  Open [http://localhost:3000](https://www.google.com/search?q=http://localhost:3000) (or the port specified) with
    your browser to see the result.

## Learn More

For comprehensive guidance on using the Para SDK, setup details, and advanced features, please refer to the official
documentation:

[Para SDK documentation](https://docs.usepara.com/welcome)
