{"_format": "hh-sol-artifact-1", "contractName": "StorageSlot", "sourceName": "@openzeppelin/contracts/utils/StorageSlot.sol", "abi": [], "bytecode": "0x60566050600b82828239805160001a6073146043577f4e487b7100000000000000000000000000000000000000000000000000000000600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220e720a9c2a57f6209f7f2521024ac618e6a0af2a0d030646748b683fc73f60af064736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220e720a9c2a57f6209f7f2521024ac618e6a0af2a0d030646748b683fc73f60af064736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}