{"contracts": {"contracts/Example.sol:Example": {"abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "", "type": "uint256"}], "name": "XChanged", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_x", "type": "uint256"}], "name": "changeX", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "x", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bin": "6080604052348015600e575f80fd5b5060e48061001b5f395ff3fe6080604052348015600e575f80fd5b50600436106030575f3560e01c80630c55699c1460345780639435337e14604d575b5f80fd5b603b5f5481565b60405190815260200160405180910390f35b605c60583660046098565b605e565b005b5f8190556040518181527fceeee1fb76599ca46aff82635ae1181018ed6c2b3feb4299610c4baddca1a9e49060200160405180910390a150565b5f6020828403121560a7575f80fd5b503591905056fea2646970667358221220990267f8440e70c773c42df74f127d9f69ae292b13c6a3eda54492216a49752c64736f6c63430008190033"}}, "version": "0.8.25+commit.b61c2a91.Darwin.appleclang"}