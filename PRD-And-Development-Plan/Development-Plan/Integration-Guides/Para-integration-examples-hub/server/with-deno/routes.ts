import { type Route } from "@std/http";
import { createWallet } from "./handlers/createWallet.js";
import { signWithEthers } from "./handlers/signWithEthers.js";
import { signWithViem } from "./handlers/signWithViem.js";
import { signWithCosmJS } from "./handlers/signWithCosmJS.js";
import { signWithSolanaWeb3 } from "./handlers/signWithSolanaWeb3.js";
import { signWithAlchemy } from "./handlers/signWithAlchemy.js";

export const routes: Route[] = [
  { pattern: new URLPattern({ pathname: "/examples/wallets/pregen/create" }), method: "POST", handler: createWallet },
  { pattern: new URLPattern({ pathname: "/examples/ethers/pregen" }), method: "POST", handler: signWithEthers },
  { pattern: new URLPattern({ pathname: "/examples/viem/pregen" }), method: "POST", handler: signWithViem },
  { pattern: new URLPattern({ pathname: "/examples/cosmjs/pregen" }), method: "POST", handler: signWithCosmJS },
  {
    pattern: new URLPattern({ pathname: "/examples/solana-web3/pregen" }),
    method: "POST",
    handler: signWithSolanaWeb3,
  },
  { pattern: new URLPattern({ pathname: "/examples/alchemy/pregen" }), method: "POST", handler: signWithAlchemy },
];
