{"name": "with-node", "type": "module", "scripts": {"install:all": "yarn install && cd test-ui && yarn install", "build:ui": "cd test-ui && yarn build", "copy:ui": "rm -rf public/* && cp -r test-ui/dist/* public/", "build:dev": "yarn build:ui && yarn copy:ui", "build": "yarn build:dev && tsc --project tsconfig.json", "dev:server": "tsx watch server.ts", "dev": "yarn build:dev && yarn dev:server"}, "dependencies": {"@aa-sdk/core": "^4.35.1", "@account-kit/infra": "^4.35.1", "@account-kit/smart-contracts": "^4.35.1", "@cosmjs/stargate": "0.33.1", "@ethereumjs/rlp": "5.0.2", "@getpara/cosmjs-v0-integration": "1.16.0", "@getpara/ethers-v6-integration": "1.16.0", "@getpara/server-sdk": "1.16.0", "@getpara/solana-web3.js-v1-integration": "1.16.0", "@getpara/viem-v2-integration": "1.16.0", "@solana/web3.js": "1.98.0", "@zerodev/ecdsa-validator": "5.4.9", "@zerodev/sdk": "5.4.36", "cosmjs-types": "^0.9.0", "dotenv": "16.5.0", "ethers": "6.14.3", "express": "4.21.2", "sqlite": "5.1.1", "sqlite3": "5.1.7", "viem": "2.30.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/jest": "29.5.14", "@types/node": "^20.0.0", "@types/sqlite3": "^3.1.11", "@types/supertest": "6.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^9.1.2", "eslint": "^8.0.0", "jest": "29.7.0", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-node": "^10.9.1", "tsx": "^4.19.1", "typescript": "^5.0.0"}, "resolutions": {"@solana/web3.js": "1.98.0"}}