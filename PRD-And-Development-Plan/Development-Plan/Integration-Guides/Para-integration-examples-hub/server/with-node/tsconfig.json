{"compilerOptions": {"lib": ["ES2022"], "target": "ES2022", "module": "Node16", "moduleDetection": "force", "allowJs": true, "moduleResolution": "Node16", "allowImportingTsExtensions": false, "noEmit": false, "outDir": "./dist", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "isolatedModules": true, "verbatimModuleSyntax": false}, "ts-node": {"esm": true, "transpileOnly": true, "skipIgnore": true, "experimentalSpecifierResolution": "node"}, "include": ["*.ts", "db/**/*.ts", "examples/**/*.ts", "utils/**/*.ts"], "exclude": ["node_modules", "dist"]}