# Para Wallet SDK Integration Guide for Everchess

## Overview

This guide provides comprehensive instructions for integrating Para Wallet SDK into the Everchess cross-platform application with the complete dashboard system. Para Wallet SDK offers unified authentication supporting email, social logins, and Web3 wallet connections, making it the perfect solution for Ever<PERSON>'s diverse user base across all dashboard functionality including tournaments, battlepass, missions, and chess sets management.

## Para Wallet SDK Benefits

### Unified Authentication
- **Email/Password**: Traditional authentication for Web2 users
- **Social Logins**: Google, Apple, Discord integration
- **Web3 Wallets**: Seamless wallet connection for crypto users
- **Progressive Enhancement**: Users can start with Web2 and upgrade to Web3

### Developer Experience
- **Single SDK**: One integration for all authentication methods
- **Cross-Platform**: Works on React Native, iOS, Android, and Web
- **Type Safety**: Full TypeScript support
- **Comprehensive Documentation**: Well-documented API and examples

### User Experience
- **Seamless Onboarding**: No complex wallet setup required initially
- **Familiar Flows**: Traditional login flows for non-crypto users
- **Wallet Integration**: Easy upgrade to Web3 features when ready
- **Secure**: Industry-standard security practices

## Installation

### React Native Installation

```bash
# Install Para Wallet SDK for React Native
npm install @para-wallet/react-native-sdk

# Install required peer dependencies
npm install react-native-keychain react-native-secure-storage
npm install @react-native-async-storage/async-storage

# For iOS, run pod install
cd ios && pod install
```

### Environment Configuration

```bash
# .env file
EXPO_PUBLIC_PARA_PROJECT_ID=your_para_project_id
EXPO_PUBLIC_PARA_API_KEY=your_para_api_key
EXPO_PUBLIC_PARA_ENVIRONMENT=development # or production
```

## Core Implementation

### Para Wallet Service

```typescript
// services/auth/paraWalletService.ts
import { Platform } from 'react-native';
import { 
  ParaWallet, 
  ParaWalletConfig, 
  AuthResult, 
  User,
  WalletInfo 
} from '@para-wallet/react-native-sdk';

export interface ParaAuthResult {
  user: User;
  accessToken: string;
  refreshToken: string;
  wallet?: WalletInfo;
}

export class ParaWalletService {
  private static instance: ParaWalletService;
  private paraWallet: ParaWallet;
  private isInitialized = false;

  private constructor() {
    this.initializeParaWallet();
  }

  public static getInstance(): ParaWalletService {
    if (!ParaWalletService.instance) {
      ParaWalletService.instance = new ParaWalletService();
    }
    return ParaWalletService.instance;
  }

  private initializeParaWallet(): void {
    const config: ParaWalletConfig = {
      projectId: process.env.EXPO_PUBLIC_PARA_PROJECT_ID!,
      apiKey: process.env.EXPO_PUBLIC_PARA_API_KEY!,
      environment: process.env.EXPO_PUBLIC_PARA_ENVIRONMENT as 'development' | 'production',
      
      // App Configuration
      appName: 'Everchess',
      appDescription: 'Ancient Made Modern - Immersive 3D Chess Platform',
      appIcon: 'https://everchess.app/icon.png',
      appUrl: 'https://everchess.app',
      
      // Supported Blockchain Networks
      chains: ['solana:mainnet', 'solana:devnet'],
      
      // Authentication Methods
      auth: {
        email: true,
        google: true,
        apple: Platform.OS === 'ios',
        discord: true,
        twitter: false,
        github: false,
      },
      
      // Wallet Features
      wallet: {
        showBalance: true,
        showActivity: true,
        enableSend: true,
        enableReceive: true,
        enableSwap: false, // Not needed for chess game
      },
      
      // UI Theme
      theme: {
        colorMode: 'dark',
        primaryColor: '#5856D6',
        backgroundColor: '#000000',
        textColor: '#FFFFFF',
        borderRadius: 8,
      },
      
      // Security Settings
      security: {
        requireBiometric: false, // Optional biometric authentication
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      },
    };

    try {
      this.paraWallet = new ParaWallet(config);
      this.isInitialized = true;
      console.log('Para Wallet SDK initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Para Wallet SDK:', error);
      throw new Error('Para Wallet SDK initialization failed');
    }
  }

  // Email/Password Authentication
  public async signUpWithEmail(email: string, password: string): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.auth.signUpWithEmail({
        email,
        password,
      });
      
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Email signup failed:', error);
      throw new Error('Failed to create account with email');
    }
  }

  public async signInWithEmail(email: string, password: string): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.auth.signInWithEmail({
        email,
        password,
      });
      
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Email signin failed:', error);
      throw new Error('Failed to sign in with email');
    }
  }

  // Social Authentication
  public async signInWithGoogle(): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.auth.signInWithGoogle();
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Google signin failed:', error);
      throw new Error('Failed to sign in with Google');
    }
  }

  public async signInWithApple(): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    if (Platform.OS !== 'ios') {
      throw new Error('Apple Sign In is only available on iOS');
    }
    
    try {
      const result = await this.paraWallet.auth.signInWithApple();
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Apple signin failed:', error);
      throw new Error('Failed to sign in with Apple');
    }
  }

  public async signInWithDiscord(): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.auth.signInWithDiscord();
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Discord signin failed:', error);
      throw new Error('Failed to sign in with Discord');
    }
  }

  // Wallet Authentication
  public async connectWallet(): Promise<ParaAuthResult> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.wallet.connect();
      return this.formatAuthResult(result);
    } catch (error) {
      console.error('Wallet connection failed:', error);
      throw new Error('Failed to connect wallet');
    }
  }

  // Session Management
  public async getCurrentUser(): Promise<User | null> {
    this.ensureInitialized();
    
    try {
      return await this.paraWallet.auth.getCurrentUser();
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  public async refreshToken(): Promise<string> {
    this.ensureInitialized();
    
    try {
      const result = await this.paraWallet.auth.refreshToken();
      return result.accessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error('Failed to refresh authentication token');
    }
  }

  public async signOut(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.paraWallet.auth.signOut();
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error('Failed to sign out');
    }
  }

  // Wallet Operations
  public async getWalletInfo(): Promise<WalletInfo | null> {
    this.ensureInitialized();
    
    try {
      return await this.paraWallet.wallet.getInfo();
    } catch (error) {
      console.error('Failed to get wallet info:', error);
      return null;
    }
  }

  public async getWalletBalance(): Promise<number> {
    this.ensureInitialized();
    
    try {
      const balance = await this.paraWallet.wallet.getBalance();
      return balance.sol || 0;
    } catch (error) {
      console.error('Failed to get wallet balance:', error);
      return 0;
    }
  }

  // Event Listeners
  public onAuthStateChanged(callback: (user: User | null) => void): () => void {
    this.ensureInitialized();
    
    return this.paraWallet.auth.onAuthStateChanged(callback);
  }

  public onWalletStateChanged(callback: (wallet: WalletInfo | null) => void): () => void {
    this.ensureInitialized();
    
    return this.paraWallet.wallet.onStateChanged(callback);
  }

  // Utility Methods
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Para Wallet SDK not initialized');
    }
  }

  private formatAuthResult(result: AuthResult): ParaAuthResult {
    return {
      user: result.user,
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      wallet: result.wallet,
    };
  }

  // Password Reset
  public async resetPassword(email: string): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.paraWallet.auth.resetPassword({ email });
      console.log('Password reset email sent');
    } catch (error) {
      console.error('Password reset failed:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  // Email Verification
  public async sendEmailVerification(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.paraWallet.auth.sendEmailVerification();
      console.log('Email verification sent');
    } catch (error) {
      console.error('Email verification failed:', error);
      throw new Error('Failed to send email verification');
    }
  }

  // Account Linking
  public async linkWallet(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.paraWallet.auth.linkWallet();
      console.log('Wallet linked successfully');
    } catch (error) {
      console.error('Wallet linking failed:', error);
      throw new Error('Failed to link wallet to account');
    }
  }

  public async unlinkWallet(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.paraWallet.auth.unlinkWallet();
      console.log('Wallet unlinked successfully');
    } catch (error) {
      console.error('Wallet unlinking failed:', error);
      throw new Error('Failed to unlink wallet from account');
    }
  }
}

// Export singleton instance
export const paraWalletService = ParaWalletService.getInstance();
```

## React Context Integration

### Auth Context with Para Wallet

```typescript
// contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { paraWalletService, ParaAuthResult } from '../services/auth/paraWalletService';
import { User, WalletInfo } from '@para-wallet/react-native-sdk';

interface AuthContextType {
  // User State
  user: User | null;
  wallet: WalletInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Authentication Methods
  signUpWithEmail: (email: string, password: string) => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  signInWithDiscord: () => Promise<void>;
  connectWallet: () => Promise<void>;
  signOut: () => Promise<void>;
  
  // Wallet Methods
  linkWallet: () => Promise<void>;
  unlinkWallet: () => Promise<void>;
  getWalletBalance: () => Promise<number>;
  
  // Utility Methods
  refreshToken: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  sendEmailVerification: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [wallet, setWallet] = useState<WalletInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    // Set up auth state listener
    const unsubscribeAuth = paraWalletService.onAuthStateChanged((user) => {
      setUser(user);
      setIsLoading(false);
    });

    // Set up wallet state listener
    const unsubscribeWallet = paraWalletService.onWalletStateChanged((wallet) => {
      setWallet(wallet);
    });

    // Initialize current user
    const initializeAuth = async () => {
      try {
        const currentUser = await paraWalletService.getCurrentUser();
        setUser(currentUser);
        
        if (currentUser) {
          const walletInfo = await paraWalletService.getWalletInfo();
          setWallet(walletInfo);
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Cleanup listeners
    return () => {
      unsubscribeAuth();
      unsubscribeWallet();
    };
  }, []);

  const handleAuthResult = (result: ParaAuthResult) => {
    setUser(result.user);
    if (result.wallet) {
      setWallet(result.wallet);
    }
  };

  const signUpWithEmail = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.signUpWithEmail(email, password);
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.signInWithEmail(email, password);
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.signInWithGoogle();
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithApple = async () => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.signInWithApple();
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithDiscord = async () => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.signInWithDiscord();
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const connectWallet = async () => {
    setIsLoading(true);
    try {
      const result = await paraWalletService.connectWallet();
      handleAuthResult(result);
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    try {
      await paraWalletService.signOut();
      setUser(null);
      setWallet(null);
    } finally {
      setIsLoading(false);
    }
  };

  const linkWallet = async () => {
    await paraWalletService.linkWallet();
    const walletInfo = await paraWalletService.getWalletInfo();
    setWallet(walletInfo);
  };

  const unlinkWallet = async () => {
    await paraWalletService.unlinkWallet();
    setWallet(null);
  };

  const getWalletBalance = async (): Promise<number> => {
    return await paraWalletService.getWalletBalance();
  };

  const refreshToken = async () => {
    await paraWalletService.refreshToken();
  };

  const resetPassword = async (email: string) => {
    await paraWalletService.resetPassword(email);
  };

  const sendEmailVerification = async () => {
    await paraWalletService.sendEmailVerification();
  };

  const value: AuthContextType = {
    user,
    wallet,
    isAuthenticated,
    isLoading,
    signUpWithEmail,
    signInWithEmail,
    signInWithGoogle,
    signInWithApple,
    signInWithDiscord,
    connectWallet,
    signOut,
    linkWallet,
    unlinkWallet,
    getWalletBalance,
    refreshToken,
    resetPassword,
    sendEmailVerification,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## Authentication Screens

### Login Screen Component

```typescript
// screens/auth/LoginScreen.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert, Platform } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { Button } from '../../components/ui/Button';
import { TextInput } from '../../components/ui/TextInput';

export const LoginScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { 
    signInWithEmail, 
    signInWithGoogle, 
    signInWithApple, 
    signInWithDiscord,
    connectWallet 
  } = useAuth();

  const handleEmailLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      await signInWithEmail(email, password);
    } catch (error) {
      Alert.alert('Login Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'apple' | 'discord') => {
    setIsLoading(true);
    try {
      switch (provider) {
        case 'google':
          await signInWithGoogle();
          break;
        case 'apple':
          await signInWithApple();
          break;
        case 'discord':
          await signInWithDiscord();
          break;
      }
    } catch (error) {
      Alert.alert('Login Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWalletConnect = async () => {
    setIsLoading(true);
    try {
      await connectWallet();
    } catch (error) {
      Alert.alert('Wallet Connection Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome to Everchess</Text>
      <Text style={styles.subtitle}>Ancient Made Modern</Text>

      {/* Email/Password Login */}
      <View style={styles.section}>
        <TextInput
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        <TextInput
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
        />
        <Button
          title="Sign In with Email"
          onPress={handleEmailLogin}
          loading={isLoading}
          style={styles.button}
        />
      </View>

      {/* Social Login */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Or continue with</Text>
        
        <Button
          title="Continue with Google"
          onPress={() => handleSocialLogin('google')}
          loading={isLoading}
          variant="outline"
          style={styles.button}
        />
        
        {Platform.OS === 'ios' && (
          <Button
            title="Continue with Apple"
            onPress={() => handleSocialLogin('apple')}
            loading={isLoading}
            variant="outline"
            style={styles.button}
          />
        )}
        
        <Button
          title="Continue with Discord"
          onPress={() => handleSocialLogin('discord')}
          loading={isLoading}
          variant="outline"
          style={styles.button}
        />
      </View>

      {/* Web3 Login */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Web3 Users</Text>
        <Button
          title="Connect Wallet"
          onPress={handleWalletConnect}
          loading={isLoading}
          variant="primary"
          style={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: '#000000',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#888888',
    textAlign: 'center',
    marginBottom: 40,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  button: {
    marginBottom: 12,
  },
});
```

## Error Handling

### Para Wallet Error Types

```typescript
// types/auth.ts
export enum ParaWalletErrorCode {
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  WALLET_CONNECTION_FAILED = 'WALLET_CONNECTION_FAILED',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  USER_CANCELLED = 'USER_CANCELLED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
}

export class ParaWalletError extends Error {
  constructor(
    public code: ParaWalletErrorCode,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ParaWalletError';
  }
}

// Error handling utility
export const handleParaWalletError = (error: any): string => {
  if (error instanceof ParaWalletError) {
    switch (error.code) {
      case ParaWalletErrorCode.INVALID_CREDENTIALS:
        return 'Invalid email or password. Please try again.';
      case ParaWalletErrorCode.EMAIL_NOT_VERIFIED:
        return 'Please verify your email address before signing in.';
      case ParaWalletErrorCode.WALLET_CONNECTION_FAILED:
        return 'Failed to connect wallet. Please try again.';
      case ParaWalletErrorCode.USER_CANCELLED:
        return 'Authentication was cancelled.';
      case ParaWalletErrorCode.NETWORK_ERROR:
        return 'Network error. Please check your connection.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
  
  return error.message || 'An unexpected error occurred.';
};
```

## Testing

### Para Wallet Service Tests

```typescript
// __tests__/services/paraWalletService.test.ts
import { paraWalletService } from '../../services/auth/paraWalletService';

describe('ParaWalletService', () => {
  beforeEach(() => {
    // Mock Para Wallet SDK
    jest.clearAllMocks();
  });

  describe('Email Authentication', () => {
    it('should sign up with email successfully', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      
      const result = await paraWalletService.signUpWithEmail(email, password);
      
      expect(result.user).toBeDefined();
      expect(result.accessToken).toBeDefined();
    });

    it('should sign in with email successfully', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      
      const result = await paraWalletService.signInWithEmail(email, password);
      
      expect(result.user).toBeDefined();
      expect(result.accessToken).toBeDefined();
    });
  });

  describe('Social Authentication', () => {
    it('should sign in with Google successfully', async () => {
      const result = await paraWalletService.signInWithGoogle();
      
      expect(result.user).toBeDefined();
      expect(result.accessToken).toBeDefined();
    });
  });

  describe('Wallet Connection', () => {
    it('should connect wallet successfully', async () => {
      const result = await paraWalletService.connectWallet();
      
      expect(result.user).toBeDefined();
      expect(result.wallet).toBeDefined();
    });
  });
});
```

## Best Practices

### Security Best Practices
1. **Never store sensitive data in plain text**
2. **Use secure storage for tokens and keys**
3. **Implement proper token refresh mechanisms**
4. **Validate all user inputs**
5. **Use HTTPS for all communications**

### Performance Best Practices
1. **Initialize Para Wallet SDK once at app startup**
2. **Use singleton pattern for service instances**
3. **Implement proper loading states**
4. **Cache user data appropriately**
5. **Handle network errors gracefully**

### User Experience Best Practices
1. **Provide clear error messages**
2. **Show loading indicators during authentication**
3. **Allow users to choose their preferred login method**
4. **Implement progressive enhancement (Web2 → Web3)**
5. **Provide help and support documentation**

This integration guide provides a comprehensive foundation for implementing Para Wallet SDK in the Everchess application, ensuring a seamless authentication experience for all user types.
