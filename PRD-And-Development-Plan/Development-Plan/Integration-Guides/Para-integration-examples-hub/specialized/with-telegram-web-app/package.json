{"name": "para-telegram-miniapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "predeploy": "yarn run build", "deploy": "gh-pages -d dist --dest with-telegram-web-app --add true -b gh-pages"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@twa-dev/sdk": "^7.8.0", "@types/lodash": "^4.17.13", "@getpara/ethers-v6-integration": "1.16.0", "@getpara/web-sdk": "1.16.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "ethers": "6.13.4", "lodash": "^4.17.21", "lucide-react": "^0.429.0", "qrcode.react": "^4.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "vite-plugin-node-polyfills": "^0.22.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/node": "^22.5.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "gh-pages": "^6.1.1", "globals": "^15.9.0", "postcss": "^8.4.41", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}