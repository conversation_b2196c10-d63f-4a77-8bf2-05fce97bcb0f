{"name": "capsule-electron", "productName": "capsule-electron", "version": "0.0.0", "description": "My Electron application description", "main": ".webpack/main", "scripts": {"start": "NODE_ENV=development electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "build": "yarn make", "publish": "electron-forge publish", "lint": "eslint --ext .js,.jsx ."}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@electron-forge/cli": "^7.4.0", "@electron-forge/maker-deb": "^7.4.0", "@electron-forge/maker-rpm": "^7.4.0", "@electron-forge/maker-squirrel": "^7.4.0", "@electron-forge/maker-zip": "^7.4.0", "@electron-forge/plugin-auto-unpack-natives": "^7.4.0", "@electron-forge/plugin-fuses": "^7.4.0", "@electron-forge/plugin-webpack": "^7.4.0", "@electron/fuses": "^1.8.0", "@vercel/webpack-asset-relocator-loader": "1.7.3", "babel-loader": "^9.1.3", "css-loader": "^6.0.0", "electron": "31.0.0", "node-loader": "^2.0.0", "style-loader": "^3.0.0"}, "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@getpara/react-sdk": "1.16.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "dotenv": "^16.4.5", "electron-squirrel-startup": "^1.0.1", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "stream-browserify": "^3.0.0", "ts-loader": "^9.5.1", "vm-browserify": "^1.1.2"}}