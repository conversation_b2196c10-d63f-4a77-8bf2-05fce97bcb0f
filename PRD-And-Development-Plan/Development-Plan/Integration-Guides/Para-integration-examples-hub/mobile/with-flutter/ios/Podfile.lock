PODS:
  - app_links (0.0.2):
    - Flutter
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_web_auth_2 (3.0.0):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - passkeys_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ua_client_hints (1.4.0):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_web_auth_2 (from `.symlinks/plugins/flutter_web_auth_2/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - passkeys_ios (from `.symlinks/plugins/passkeys_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - ua_client_hints (from `.symlinks/plugins/ua_client_hints/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_web_auth_2:
    :path: ".symlinks/plugins/flutter_web_auth_2/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  passkeys_ios:
    :path: ".symlinks/plugins/passkeys_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  ua_client_hints:
    :path: ".symlinks/plugins/ua_client_hints/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: e7a6750a915a9e161c58d91bc610e8cd1d4d0ad0
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_web_auth_2: 06d500582775790a0d4c323222fcb6d7990f9603
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  passkeys_ios: fdae8c06e2178a9fcb9261a6cb21fb9a06a81d53
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  ua_client_hints: 46bb5817a868f9e397c0ba7e3f2f5c5d90c35156
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2
