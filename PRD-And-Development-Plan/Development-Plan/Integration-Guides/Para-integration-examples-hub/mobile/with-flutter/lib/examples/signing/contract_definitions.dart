// contract_definitions.dart
import 'dart:convert';

final paraTestTokenAbiJson = '''
{
  "_format": "hh-sol-artifact-1",
  "contractName": "ParaTestToken",
  "sourceName": "contracts/ParaTestToken.sol",
  "abi": [
    {
      "inputs": [],
      "stateMutability": "nonpayable",
      "type": "constructor"
    },
    {
      "inputs": [],
      "name": "ECDSAInvalidSignature",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "length",
          "type": "uint256"
        }
      ],
      "name": "ECDSAInvalidSignatureLength",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "bytes32",
          "name": "s",
          "type": "bytes32"
        }
      ],
      "name": "ECDSAInvalidSignatureS",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "increasedSupply",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "cap",
          "type": "uint256"
        }
      ],
      "name": "ERC20ExceededCap",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "spender",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "allowance",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "needed",
          "type": "uint256"
        }
      ],
      "name": "ERC20InsufficientAllowance",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "sender",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "balance",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "needed",
          "type": "uint256"
        }
      ],
      "name": "ERC20InsufficientBalance",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "approver",
          "type": "address"
        }
      ],
      "name": "ERC20InvalidApprover",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "cap",
          "type": "uint256"
        }
      ],
      "name": "ERC20InvalidCap",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "receiver",
          "type": "address"
        }
      ],
      "name": "ERC20InvalidReceiver",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "sender",
          "type": "address"
        }
      ],
      "name": "ERC20InvalidSender",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "spender",
          "type": "address"
        }
      ],
      "name": "ERC20InvalidSpender",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "deadline",
          "type": "uint256"
        }
      ],
      "name": "ERC2612ExpiredSignature",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "signer",
          "type": "address"
        },
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        }
      ],
      "name": "ERC2612InvalidSigner",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "currentNonce",
          "type": "uint256"
        }
      ],
      "name": "InvalidAccountNonce",
      "type": "error"
    },
    {
      "inputs": [],
      "name": "InvalidShortString",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        }
      ],
      "name": "OwnableInvalidOwner",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "OwnableUnauthorizedAccount",
      "type": "error"
    },
    {
      "inputs": [
        {
          "internalType": "string",
          "name": "str",
          "type": "string"
        }
      ],
      "name": "StringTooLong",
      "type": "error"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "owner",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "spender",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "Approval",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [],
      "name": "EIP712DomainChanged",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "previousOwner",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "OwnershipTransferred",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "internalType": "address",
          "name": "from",
          "type": "address"
        },
        {
          "indexed": true,
          "internalType": "address",
          "name": "to",
          "type": "address"
        },
        {
          "indexed": false,
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "Transfer",
      "type": "event"
    },
    {
      "inputs": [],
      "name": "DOMAIN_SEPARATOR",
      "outputs": [
        {
          "internalType": "bytes32",
          "name": "",
          "type": "bytes32"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "MINT_LIMIT",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        },
        {
          "internalType": "address",
          "name": "spender",
          "type": "address"
        }
      ],
      "name": "allowance",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "spender",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "approve",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        }
      ],
      "name": "balanceOf",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "burn",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "account",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "burnFrom",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "cap",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "decimals",
      "outputs": [
        {
          "internalType": "uint8",
          "name": "",
          "type": "uint8"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "eip712Domain",
      "outputs": [
        {
          "internalType": "bytes1",
          "name": "fields",
          "type": "bytes1"
        },
        {
          "internalType": "string",
          "name": "name",
          "type": "string"
        },
        {
          "internalType": "string",
          "name": "version",
          "type": "string"
        },
        {
          "internalType": "uint256",
          "name": "chainId",
          "type": "uint256"
        },
        {
          "internalType": "address",
          "name": "verifyingContract",
          "type": "address"
        },
        {
          "internalType": "bytes32",
          "name": "salt",
          "type": "bytes32"
        },
        {
          "internalType": "uint256[]",
          "name": "extensions",
          "type": "uint256[]"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "uint256",
          "name": "amount",
          "type": "uint256"
        }
      ],
      "name": "mint",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "name": "mintedAmount",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "name",
      "outputs": [
        {
          "internalType": "string",
          "name": "",
          "type": "string"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        }
      ],
      "name": "nonces",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "owner",
      "outputs": [
        {
          "internalType": "address",
          "name": "",
          "type": "address"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "owner",
          "type": "address"
        },
        {
          "internalType": "address",
          "name": "spender",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        },
        {
          "internalType": "uint256",
          "name": "deadline",
          "type": "uint256"
        },
        {
          "internalType": "uint8",
          "name": "v",
          "type": "uint8"
        },
        {
          "internalType": "bytes32",
          "name": "r",
          "type": "bytes32"
        },
        {
          "internalType": "bytes32",
          "name": "s",
          "type": "bytes32"
        }
      ],
      "name": "permit",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "renounceOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "symbol",
      "outputs": [
        {
          "internalType": "string",
          "name": "",
          "type": "string"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [],
      "name": "totalSupply",
      "outputs": [
        {
          "internalType": "uint256",
          "name": "",
          "type": "uint256"
        }
      ],
      "stateMutability": "view",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "to",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "transfer",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "from",
          "type": "address"
        },
        {
          "internalType": "address",
          "name": "to",
          "type": "address"
        },
        {
          "internalType": "uint256",
          "name": "value",
          "type": "uint256"
        }
      ],
      "name": "transferFrom",
      "outputs": [
        {
          "internalType": "bool",
          "name": "",
          "type": "bool"
        }
      ],
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "internalType": "address",
          "name": "newOwner",
          "type": "address"
        }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "stateMutability": "nonpayable",
      "type": "function"
    }
  ],
  "bytecode": "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",
  "deployedBytecode": "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",
  "linkReferences": {},
  "deployedLinkReferences": {}
}
''';

final paraTestTokenData = jsonDecode(paraTestTokenAbiJson);
final paraTestTokenName = paraTestTokenData['contractName'];
final paraTestTokenAbi = paraTestTokenData['abi'];
