{"name": "para-react-native", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "postinstall": "cd ios && pod install && cd ..", "build": "npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output /dev/null && npx react-native bundle --platform ios --dev false --entry-file index.js --bundle-output /dev/null"}, "dependencies": {"@cosmjs/stargate": "0.33.1", "@craftzdog/react-native-buffer": "6.0.5", "@getpara/cosmjs-v0-integration": "1.16.0", "@getpara/ethers-v6-integration": "1.16.0", "@getpara/react-native-wallet": "1.16.0", "@getpara/solana-web3.js-v1-integration": "1.16.0", "@getpara/viem-v2-integration": "1.16.0", "@react-native-async-storage/async-storage": "2.1.2", "@solana/web3.js": "1.98.0", "ethers": "6.13.5", "node-libs-react-native": "1.2.1", "react": "19.0.0", "react-native": "0.78.2", "react-native-gesture-handler": "2.25.0", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-keychain": "10.0.0", "react-native-modpow": "1.1.0", "react-native-passkey": "3.1.0", "react-native-quick-base64": "2.1.2", "react-native-quick-crypto": "0.7.12", "react-native-safe-area-context": "5.3.0", "react-native-screens": "4.10.0", "viem": "2.24.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/runtime": "^7.25.0", "@react-native-clipboard/clipboard": "1.16.2", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@react-navigation/native": "7.1.1", "@react-navigation/stack": "7.2.5", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@types/react": "19.0.0", "@types/react-native-vector-icons": "6.4.18", "react-native-dotenv": "3.4.11", "react-native-dropdown-select-list": "2.0.5", "react-native-vector-icons": "10.2.0", "typescript": "5.0.4"}}