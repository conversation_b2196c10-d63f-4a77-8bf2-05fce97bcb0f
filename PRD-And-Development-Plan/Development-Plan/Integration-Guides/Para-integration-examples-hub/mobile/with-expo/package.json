{"name": "para-expo", "version": "1.0.0", "private": true, "main": "index.js", "scripts": {"android": "expo run:android --device", "ios": "expo run:ios", "lint": "expo lint", "reset-project": "node ./scripts/reset-project.js", "start": "expo start", "test": "jest --watchAll", "web": "expo start --web", "clean": "expo prebuild --clean", "expo-upgrade": "expo upgrade"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@cosmjs/stargate": "0.32.4", "@craftzdog/react-native-buffer": "6.0.5", "@expo/vector-icons": "14.0.2", "@getpara/cosmjs-v0-integration": "1.16.0", "@getpara/ethers-v6-integration": "1.16.0", "@getpara/react-native-wallet": "1.16.0", "@getpara/solana-web3.js-v1-integration": "1.16.0", "@getpara/viem-v2-integration": "1.16.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^7.0.14", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@solana/web3.js": "1.98.0", "ethers": "6", "expo": "^52.0.46", "expo-clipboard": "~7.0.1", "expo-linking": "~7.0.5", "expo-router": "~4.0.19", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.0", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-native": "0.76.9", "react-native-dropdown-select-list": "2.0.5", "react-native-keychain": "9.2.3", "react-native-modpow": "1.1.0", "react-native-passkey": "3.1.0", "react-native-quick-base64": "2.1.2", "react-native-quick-crypto": "0.7.12", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "viem": "2.22.8"}, "devDependencies": {"@babel/core": "7.25.2", "@types/react": "18.3.12", "typescript": "5.3.3"}}