# Everchess Backend API Integration Guide

## Overview

This guide provides comprehensive backend integration patterns for the Everchess platform, covering API design, data models, real-time updates, and state synchronization strategies. It complements the UI/UX and Dashboard integration guides with robust backend implementation details.

## API Architecture

### RESTful API Design for React Native + Expo

The Everchess backend follows RESTful principles optimized for mobile clients:

```typescript
// Mobile-optimized API response structure
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    retryable?: boolean; // Important for mobile error handling
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasMore: boolean; // Useful for infinite scroll
    };
    timestamp: string;
    cacheControl?: {
      maxAge: number;
      etag: string;
    };
  };
}

// Mobile-specific HTTP considerations
enum HttpStatus {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  NOT_MODIFIED = 304, // Important for mobile caching
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429, // Critical for mobile rate limiting
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503, // For maintenance mode
}

// Mobile-specific error types
interface MobileApiError {
  code: string;
  message: string;
  retryable: boolean;
  retryAfter?: number; // Seconds to wait before retry
  offline?: boolean; // Indicates if error is due to offline state
}
```

### Authentication & Authorization

```typescript
// JWT token structure
interface JWTPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin' | 'moderator';
  permissions: string[];
  iat: number;
  exp: number;
}

// Authentication middleware
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Access token is required',
        },
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid or expired token',
      },
    });
  }
};
```

## Core API Endpoints

### User Management

```typescript
// User profile endpoints
GET    /api/users/profile           // Get current user profile
PUT    /api/users/profile           // Update user profile
GET    /api/users/stats             // Get user statistics
POST   /api/users/avatar            // Upload avatar image

// User preferences
GET    /api/users/preferences       // Get user preferences
PUT    /api/users/preferences       // Update preferences
```

### Game System

```typescript
// Matchmaking
POST   /api/matchmaking/join        // Join matchmaking queue
POST   /api/matchmaking/leave       // Leave matchmaking queue
GET    /api/matchmaking/status      // Get queue status

// Games
GET    /api/games                   // Get user's games
GET    /api/games/:gameId           // Get specific game
POST   /api/games/:gameId/move      // Make a move
POST   /api/games/:gameId/resign    // Resign game
GET    /api/games/live              // Get live games for spectating
```

### Progression System

```typescript
// Battlepass
GET    /api/battlepass/current      // Get current battlepass
GET    /api/battlepass/tiers        // Get all tiers and rewards
POST   /api/battlepass/claim/:tier  // Claim tier reward
POST   /api/battlepass/purchase     // Purchase premium battlepass

// Missions
GET    /api/missions               // Get daily/weekly missions
POST   /api/missions/claim-xp      // Claim completed mission XP
PUT    /api/missions/:id/progress  // Update mission progress
```

### Inventory & Market

```typescript
// Chess Sets
GET    /api/inventory/chess-sets    // Get owned chess sets
POST   /api/inventory/chess-sets/select/:setId  // Select active set
GET    /api/chess-sets              // Get all available sets
GET    /api/chess-sets/:setId       // Get set details

// Market
GET    /api/market/items            // Get market items
POST   /api/market/purchase/:itemId // Purchase item
GET    /api/market/transactions     // Get purchase history
```

## Data Models

### User Model

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  avatar_url TEXT,
  rating INTEGER DEFAULT 1200,
  xp INTEGER DEFAULT 0,
  level INTEGER DEFAULT 1,
  gold INTEGER DEFAULT 0,
  premium_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);
```

### Game Model

```sql
CREATE TABLE games (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  white_player_id UUID REFERENCES users(id) NOT NULL,
  black_player_id UUID REFERENCES users(id) NOT NULL,
  game_mode VARCHAR(20) NOT NULL, -- 'ranked', 'casual', 'tournament'
  time_control VARCHAR(10) NOT NULL, -- '5+5', '10+0', etc.
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'abandoned'
  result VARCHAR(20), -- 'white_wins', 'black_wins', 'draw'
  fen TEXT NOT NULL DEFAULT 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
  moves JSONB DEFAULT '[]',
  white_time_remaining INTEGER, -- in seconds
  black_time_remaining INTEGER, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  completed_at TIMESTAMP WITH TIME ZONE
);
```

### Complete Database Schema

#### **Battlepass System**
```sql
CREATE TABLE battlepasses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  season INTEGER NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

CREATE TABLE battlepass_tiers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  battlepass_id UUID REFERENCES battlepasses(id) NOT NULL,
  tier_level INTEGER NOT NULL,
  xp_required INTEGER NOT NULL,
  free_reward_type TEXT NOT NULL,
  free_reward_data JSONB NOT NULL,
  premium_reward_type TEXT NOT NULL,
  premium_reward_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(battlepass_id, tier_level)
);

CREATE TABLE user_battlepass_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  battlepass_id UUID REFERENCES battlepasses(id) NOT NULL,
  current_tier INTEGER DEFAULT 1,
  current_xp INTEGER DEFAULT 0,
  premium BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(user_id, battlepass_id)
);

CREATE TABLE battlepass_reward_claims (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  battlepass_id UUID REFERENCES battlepasses(id) NOT NULL,
  tier_level INTEGER NOT NULL,
  reward_type TEXT NOT NULL, -- 'free' or 'premium'
  claimed_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(user_id, battlepass_id, tier_level, reward_type)
);
```

#### **Mission System**
```sql
CREATE TABLE missions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mission_type TEXT NOT NULL, -- 'daily' or 'weekly'
  title TEXT NOT NULL,
  description TEXT,
  steps JSONB NOT NULL, -- Array of step objects with target, xp, etc.
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

CREATE TABLE mission_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  mission_id UUID REFERENCES missions(id) NOT NULL,
  progress JSONB NOT NULL, -- Array of progress values for each step
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(user_id, mission_id)
);
```

#### **Chess Sets System**
```sql
CREATE TABLE chess_sets (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  rarity TEXT NOT NULL,
  acquisition_type TEXT NOT NULL, -- 'starter', 'purchase', 'battlepass', 'achievement'
  price INTEGER,
  battlepass_season INTEGER,
  battlepass_tier INTEGER,
  preview_image TEXT NOT NULL,
  icon_image TEXT NOT NULL,
  piece_images JSONB NOT NULL,
  board_image TEXT NOT NULL,
  special_effects JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

CREATE TABLE user_chess_sets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) NOT NULL,
  chess_set_id TEXT REFERENCES chess_sets(id) NOT NULL,
  acquired_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(user_id, chess_set_id)
);

CREATE TABLE user_selected_chess_set (
  user_id UUID REFERENCES users(id) PRIMARY KEY,
  chess_set_id TEXT REFERENCES chess_sets(id) NOT NULL,
  selected_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);
```

#### **Tournament System**
```sql
CREATE TABLE tournaments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  time_control TEXT NOT NULL,
  max_participants INTEGER NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  prize TEXT NOT NULL,
  status TEXT DEFAULT 'upcoming', -- 'upcoming', 'active', 'completed'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

CREATE TABLE tournament_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tournament_id UUID REFERENCES tournaments(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  registered_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE(tournament_id, user_id)
);

CREATE TABLE tournament_matches (
  id TEXT PRIMARY KEY,
  tournament_id UUID REFERENCES tournaments(id) NOT NULL,
  round INTEGER NOT NULL,
  player1_id UUID REFERENCES users(id) NOT NULL,
  player2_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'pending', -- 'pending', 'active', 'completed'
  winner_id UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  completed_at TIMESTAMP WITH TIME ZONE
);
```

## WebSocket Integration

### Event Types

```typescript
// Client to Server events
interface ClientEvents {
  'join_room': { roomId: string };
  'leave_room': { roomId: string };
  'game_move': { gameId: string; move: string };
  'chat_message': { gameId: string; message: string };
}

// Server to Client events
interface ServerEvents {
  'game_update': { gameId: string; fen: string; lastMove: string };
  'match_found': { gameId: string; opponent: Player };
  'mission_completed': { missionId: string; xp: number };
  'battlepass_level_up': { newTier: number; rewards: any[] };
  'notification': { type: string; message: string };
}
```

### WebSocket Server Implementation

```typescript
// WebSocket server setup with Socket.IO
import { Server } from 'socket.io';
import { authenticateSocket } from './middleware/auth';

export class WebSocketServer {
  private io: Server;

  constructor(server: any) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL,
        methods: ['GET', 'POST'],
      },
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    this.io.use(authenticateSocket);
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User ${socket.userId} connected`);

      // Join user to their personal room
      socket.join(`user:${socket.userId}`);

      // Handle game events
      socket.on('join_game', (data) => {
        socket.join(`game:${data.gameId}`);
      });

      socket.on('game_move', async (data) => {
        try {
          const result = await this.handleGameMove(socket.userId, data);
          this.io.to(`game:${data.gameId}`).emit('game_update', result);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('disconnect', () => {
        console.log(`User ${socket.userId} disconnected`);
      });
    });
  }

  // Emit events to specific users or rooms
  emitToUser(userId: string, event: string, data: any) {
    this.io.to(`user:${userId}`).emit(event, data);
  }

  emitToGame(gameId: string, event: string, data: any) {
    this.io.to(`game:${gameId}`).emit(event, data);
  }

  emitToAll(event: string, data: any) {
    this.io.emit(event, data);
  }
}
```

## Error Handling

### Standardized Error Responses

```typescript
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Error handling middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (error instanceof ApiError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
      },
    });
  }

  // Log unexpected errors
  console.error('Unexpected error:', error);

  return res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred',
    },
  });
};
```

## Performance Optimization

### Caching Strategy

```typescript
// Redis caching for frequently accessed data
import Redis from 'ioredis';

export class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!);
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds: number = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttlSeconds, JSON.stringify(value));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }
}
```

## Advanced Matchmaking System

### Queue Management Architecture

```typescript
// Advanced Queue Manager with rating-based matching
export class QueueManager {
  private queues: Map<string, Queue> = new Map();

  constructor() {
    this.initializeQueues();
    setInterval(() => this.processQueues(), 5000);
  }

  private initializeQueues() {
    const gameModes = ['ranked', 'casual'];
    const timeControls = ['3+2', '5+5', '10+5', '15+10'];

    for (const mode of gameModes) {
      for (const timeControl of timeControls) {
        const queueId = `${mode}-${timeControl}`;
        this.queues.set(queueId, new Queue(queueId));
      }
    }
  }

  public addToQueue(userId: string, mode: string, timeControl: string, rating: number): string {
    const queueId = `${mode}-${timeControl}`;
    const queue = this.queues.get(queueId);

    if (!queue) {
      throw new Error(`Queue not found: ${queueId}`);
    }

    return queue.addPlayer(userId, rating);
  }

  public removeFromQueue(userId: string): boolean {
    let removed = false;

    for (const queue of this.queues.values()) {
      if (queue.removePlayer(userId)) {
        removed = true;
      }
    }

    return removed;
  }

  private processQueues() {
    for (const queue of this.queues.values()) {
      const matches = queue.findMatches();

      for (const match of matches) {
        this.createGame(match);
      }
    }
  }

  private createGame(match: Match) {
    const gameId = `game-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    this.notifyPlayers(match, gameId);
    this.saveGame(match, gameId);
  }

  private notifyPlayers(match: Match, gameId: string) {
    const { player1, player2 } = match;

    io.to(player1.userId).emit('match_found', { gameId });
    io.to(player2.userId).emit('match_found', { gameId });
  }
}
```

### Advanced Queue with Rating-Based Matching

```typescript
interface QueuePlayer {
  userId: string;
  rating: number;
  joinTime: Date;
}

interface Match {
  player1: QueuePlayer;
  player2: QueuePlayer;
}

export class Queue {
  private id: string;
  private players: QueuePlayer[] = [];

  constructor(id: string) {
    this.id = id;
  }

  public findMatches(): Match[] {
    const matches: Match[] = [];

    if (this.players.length < 2) {
      return matches;
    }

    // Sort by join time (oldest first)
    this.players.sort((a, b) => a.joinTime.getTime() - b.joinTime.getTime());

    const playersToRemove = new Set<string>();

    for (let i = 0; i < this.players.length; i++) {
      if (playersToRemove.has(this.players[i].userId)) {
        continue;
      }

      const player1 = this.players[i];
      const waitTimeMinutes = (new Date().getTime() - player1.joinTime.getTime()) / 60000;

      let bestMatchIndex = -1;
      let minRatingDiff = Infinity;

      for (let j = i + 1; j < this.players.length; j++) {
        if (playersToRemove.has(this.players[j].userId)) {
          continue;
        }

        const player2 = this.players[j];
        const ratingDiff = Math.abs(player1.rating - player2.rating);

        // Accept match if rating difference is small or wait time is long
        if (ratingDiff < minRatingDiff && (ratingDiff < 200 || waitTimeMinutes > 2)) {
          minRatingDiff = ratingDiff;
          bestMatchIndex = j;
        }
      }

      if (bestMatchIndex !== -1) {
        const player2 = this.players[bestMatchIndex];

        matches.push({ player1, player2 });

        playersToRemove.add(player1.userId);
        playersToRemove.add(player2.userId);
      }
    }

    // Remove matched players from queue
    this.players = this.players.filter(p => !playersToRemove.has(p.userId));

    return matches;
  }

  public getEstimatedWaitTime(rating: number): number {
    const queueLength = this.players.length;

    if (queueLength === 0) {
      return 10; // Base wait time in seconds
    }

    // Find players with similar ratings
    const similarRatings = this.players.filter(p => Math.abs(p.rating - rating) < 200).length;

    if (similarRatings > 0) {
      return 30; // Shorter wait time if similar players are in queue
    }

    return 60; // Longer wait time if no similar players
  }
}
```

## Tournament Management System

### Advanced Tournament Manager

```typescript
export class TournamentManager {
  private tournaments: Map<string, Tournament> = new Map();

  constructor() {
    this.loadTournaments();
    setInterval(() => this.checkTournamentStatus(), 60000);
  }

  public async createTournament(data: TournamentCreateData): Promise<Tournament> {
    try {
      const tournamentData = await db.tournaments.create({
        data: {
          title: data.title,
          time_control: data.timeControl,
          max_participants: data.maxParticipants,
          start_time: data.startDate,
          prize: data.prize,
          status: 'upcoming'
        }
      });

      const tournament = new Tournament(tournamentData);
      this.tournaments.set(tournament.id, tournament);

      return tournament;
    } catch (error) {
      console.error('Failed to create tournament:', error);
      throw error;
    }
  }

  public async registerPlayer(tournamentId: string, userId: string): Promise<boolean> {
    const tournament = this.tournaments.get(tournamentId);

    if (!tournament) {
      throw new Error(`Tournament not found: ${tournamentId}`);
    }

    return tournament.registerPlayer(userId);
  }

  private async checkTournamentStatus() {
    const now = new Date();

    for (const tournament of this.tournaments.values()) {
      if (tournament.status === 'upcoming' && tournament.startTime <= now) {
        await tournament.start();
      }

      if (tournament.status === 'active' && tournament.isCompleted()) {
        await tournament.end();
      }
    }
  }
}
```

This backend integration guide provides the foundation for implementing robust, scalable backend services that seamlessly integrate with the Everchess frontend components and real-time features.
