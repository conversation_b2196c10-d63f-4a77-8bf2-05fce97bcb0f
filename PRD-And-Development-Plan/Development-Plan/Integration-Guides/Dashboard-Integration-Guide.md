# Everchess Comprehensive Dashboard Integration Guide

## Overview

This comprehensive guide provides complete implementation details for the entire Everchess dashboard system in React Native + Expo. Based on thorough analysis of the UI-UX Foundation Reference, this covers ALL dashboard functionality including:

- **Complete Dashboard Pages**: Main dashboard, play page, battlepass, chess sets, profile, ranking, market, and game
- **Interactive Game Mode Selection**: Dynamic time controls, expandable options, queue system
- **Full Battlepass System**: Horizontal scrolling rewards, free/premium tiers, tier progression
- **Complete Mission System**: Daily/weekly missions, XP tracking, tabbed interface, step indicators
- **Chess Sets Management**: Collection display, rarity system, selection interface
- **Profile & Rankings**: User stats, achievements, leaderboard integration
- **Advanced Animation System**: XP claim, level up, battlepass rewards, mission completion
- **Real-time Game System**: 3D chess board, drag-and-drop pieces, chat, spectating
- **Tournament System**: Creation, registration, live tournaments
- **Market System**: Chess set purchasing, rarity-based pricing
- **Backend Integration**: Complete API endpoints, data models, and real-time updates
- **State Management**: Context providers, WebSocket integration, and optimistic updates

## Complete Dashboard Architecture

### Main Dashboard Screen
```typescript
// screens/DashboardScreen.tsx
import React, { useState, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GameModeSelection } from '../components/dashboard/GameModeSelection';
import { BattlepassCard } from '../components/dashboard/BattlepassCard';
import { MissionsCard } from '../components/dashboard/MissionsCard';
import { ChessSetsCard } from '../components/dashboard/ChessSetsCard';
import { LeaderboardCard } from '../components/dashboard/LeaderboardCard';
import { DashboardHeader } from '../components/dashboard/DashboardHeader';

export const DashboardScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading dashboard data
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <DashboardHeader />
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Welcome back, Einstein</Text>
          <Text style={styles.welcomeSubtitle}>Choose a mode, time, and chess set.</Text>
        </View>

        {/* Game Mode Selection */}
        <GameModeSelection />

        {/* Dashboard Cards Grid */}
        <View style={styles.cardsGrid}>
          <View style={styles.cardsRow}>
            <BattlepassCard />
            <MissionsCard />
          </View>

          <View style={styles.cardsRow}>
            <ChessSetsCard />
            <LeaderboardCard />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0b0b0e',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    gap: 24,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: 8,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    fontFamily: 'Sora_700Bold',
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    marginTop: 4,
    fontFamily: 'Lora_400Regular',
  },
  cardsGrid: {
    gap: 16,
  },
  cardsRow: {
    flexDirection: 'row',
    gap: 16,
  },
});
```

## Complete Game Mode Selection System

### Interactive Game Mode Cards with Full Functionality
```typescript
// components/dashboard/GameModeSelection.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Gamepad2, Trophy, Settings } from 'lucide-react-native';
import { useQueue } from '../../contexts/QueueContext';

// Wagers Icon Component (custom SVG from UI-UX Foundation Reference)
const WagersIcon: React.FC = () => (
  <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
    <Path
      d="M16 2H8L2 8L12 22L22 8L16 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path d="M2 8H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M8 2L12 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <Path d="M16 2L12 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

interface GameMode {
  id: string;
  title: string;
  icon: React.ComponentType;
  hasMoreOptions: boolean;
  comingSoon?: boolean;
  iconColor: string;
  description: string;
  timeControl: string;
  players: string;
  featured?: boolean;
}

interface TimeOption {
  label: string;
  minutes: number;
  increment: number;
}

export const GameModeSelection: React.FC = () => {
  const [activeGameMode, setActiveGameMode] = useState<string | null>(null);
  const [expandedTimeOptions, setExpandedTimeOptions] = useState<string[]>([]);
  const { startQueue } = useQueue();

  const gameModes: GameMode[] = [
    {
      id: 'ranked',
      title: 'Ranked',
      icon: Gamepad2,
      hasMoreOptions: true,
      iconColor: '#ffd432',
      description: 'Compete for ranking points and climb the leaderboard',
      timeControl: 'Various',
      players: '1v1',
      featured: true,
      comingSoon: false,
    },
    {
      id: 'tournaments',
      title: 'Tournaments',
      icon: Trophy,
      hasMoreOptions: true,
      iconColor: '#ffd432',
      description: 'Join scheduled tournaments with prizes',
      timeControl: 'Various',
      players: 'Multiple',
      comingSoon: false,
    },
    {
      id: 'wagers',
      title: 'Wagers',
      icon: WagersIcon,
      hasMoreOptions: true,
      comingSoon: true,
      iconColor: '#ffd432',
      description: 'Jump into a game with random players',
      timeControl: 'Various',
      players: '1v1',
    },
    {
      id: 'custom',
      title: 'Custom',
      icon: Settings,
      hasMoreOptions: true,
      comingSoon: true,
      iconColor: '#ffd432',
      description: 'Practice against different AI difficulty levels',
      timeControl: 'Your choice',
      players: '1v1',
    },
  ];

  // Initial time options (always shown)
  const initialTimeOptions: TimeOption[] = [
    { label: '3 | 2+', minutes: 3, increment: 2 },
    { label: '5 | 5+', minutes: 5, increment: 5 },
    { label: '15 | 10+', minutes: 15, increment: 10 },
  ];

  // Extended time options (shown when "More Time Options" is clicked)
  const extendedTimeOptions: TimeOption[] = [
    { label: '2 | 1+', minutes: 2, increment: 1 },
    { label: '5 | 2+', minutes: 5, increment: 2 },
    { label: '10 | 5+', minutes: 10, increment: 5 },
    { label: '2 | 0', minutes: 2, increment: 0 },
    { label: '5 | 0', minutes: 5, increment: 0 },
    { label: '10 | 0', minutes: 10, increment: 0 },
  ];

  const handleGameModePress = (modeId: string) => {
    const mode = gameModes.find(m => m.id === modeId);
    if (mode && !mode.comingSoon) {
      setActiveGameMode(activeGameMode === modeId ? null : modeId);
    }
  };

  const handleTimeOptionPress = (modeId: string, option: TimeOption) => {
    const mode = gameModes.find(m => m.id === modeId);
    if (mode && !mode.comingSoon) {
      startQueue(mode.title, option.label);
      setActiveGameMode(null);
    }
  };

  const toggleExpandedTimeOptions = (modeId: string) => {
    if (expandedTimeOptions.includes(modeId)) {
      setExpandedTimeOptions(expandedTimeOptions.filter(id => id !== modeId));
    } else {
      setExpandedTimeOptions([...expandedTimeOptions, modeId]);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.gameModesGrid}>
        {gameModes.map((mode) => {
          const Icon = mode.icon;
          const isActive = activeGameMode === mode.id;
          const isExpanded = expandedTimeOptions.includes(mode.id);

          return (
            <GameModeCard
              key={mode.id}
              mode={mode}
              isActive={isActive}
              isExpanded={isExpanded}
              timeOptions={initialTimeOptions}
              extendedTimeOptions={extendedTimeOptions}
              onPress={() => handleGameModePress(mode.id)}
              onTimeOptionPress={(option) => handleTimeOptionPress(mode.id, option)}
              onToggleExpanded={() => toggleExpandedTimeOptions(mode.id)}
            />
          );
        })}
      </View>
    </View>
  );
};

// Game Mode Card Component (Complete Implementation)
const GameModeCard: React.FC<{
  mode: GameMode;
  isActive: boolean;
  isExpanded: boolean;
  timeOptions: TimeOption[];
  extendedTimeOptions: TimeOption[];
  onPress: () => void;
  onTimeOptionPress: (option: TimeOption) => void;
  onToggleExpanded: () => void;
}> = ({
  mode,
  isActive,
  isExpanded,
  timeOptions,
  extendedTimeOptions,
  onPress,
  onTimeOptionPress,
  onToggleExpanded,
}) => {
  const [scaleAnim] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const Icon = mode.icon;

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
      disabled={mode.comingSoon}
    >
      <Animated.View
        style={[
          styles.gameModeCard,
          isActive && styles.gameModeCardActive,
          mode.comingSoon && styles.gameModeCardDisabled,
          { transform: [{ scale: scaleAnim }] }
        ]}
      >
        {/* Card Header */}
        <View style={[styles.cardHeader, isActive && styles.cardHeaderActive]}>
          <Text style={styles.cardTitle}>{mode.title}</Text>
          <View style={[styles.iconContainer, isActive && styles.iconContainerActive]}>
            <Icon size={24} color={mode.iconColor} />
          </View>
        </View>

        {/* Card Content */}
        <View style={styles.cardContent}>
          {/* Always show initial time options */}
          <View style={styles.timeOptionsGrid}>
            {timeOptions.map((option) => (
              <TouchableOpacity
                key={option.label}
                style={[
                  styles.timeOptionButton,
                  mode.comingSoon && styles.timeOptionButtonDisabled
                ]}
                onPress={() => {
                  if (!mode.comingSoon) {
                    onTimeOptionPress(option);
                  }
                }}
                disabled={mode.comingSoon}
              >
                <Text style={styles.timeOptionText}>{option.label}</Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Show either More Time Options button or extended time options */}
          {!isExpanded ? (
            <TouchableOpacity
              style={[
                styles.moreOptionsButton,
                mode.comingSoon && styles.moreOptionsButtonDisabled
              ]}
              onPress={() => {
                if (!mode.comingSoon && mode.hasMoreOptions) {
                  onToggleExpanded();
                }
              }}
              disabled={mode.comingSoon || !mode.hasMoreOptions}
            >
              <Text style={styles.moreOptionsText}>
                {mode.hasMoreOptions ? 'More Time Options' : 'Coming Soon'}
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.extendedTimeOptions}>
              {/* Extended time options - 2 rows of 3 */}
              <View style={styles.timeOptionsGrid}>
                {extendedTimeOptions.slice(0, 3).map((option) => (
                  <TouchableOpacity
                    key={option.label}
                    style={styles.timeOptionButton}
                    onPress={() => onTimeOptionPress(option)}
                  >
                    <Text style={styles.timeOptionText}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
              <View style={styles.timeOptionsGrid}>
                {extendedTimeOptions.slice(3, 6).map((option) => (
                  <TouchableOpacity
                    key={option.label}
                    style={styles.timeOptionButton}
                    onPress={() => onTimeOptionPress(option)}
                  >
                    <Text style={styles.timeOptionText}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Show fewer options button */}
              <TouchableOpacity
                style={styles.moreOptionsButton}
                onPress={onToggleExpanded}
              >
                <Text style={styles.moreOptionsText}>Show Fewer Options</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Coming Soon Badge */}
          {mode.comingSoon && (
            <View style={styles.comingSoonBadge}>
              <Text style={styles.comingSoonText}>Coming Soon</Text>
            </View>
          )}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  gameModesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  gameModeCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#1C1C1E',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 182, 255, 0.6)',
    overflow: 'hidden',
    minHeight: 200,
  },
  gameModeCardActive: {
    borderColor: '#00b6ff',
    shadowColor: '#00b6ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 8,
  },
  gameModeCardDisabled: {
    opacity: 0.6,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 182, 255, 0.2)',
  },
  cardHeaderActive: {
    borderBottomColor: '#00b6ff',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_700Bold',
  },
  iconContainer: {
    padding: 4,
  },
  iconContainerActive: {
    transform: [{ scale: 1.1 }],
  },
  cardContent: {
    padding: 16,
    paddingTop: 8,
  },
  timeOptionsGrid: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  timeOptionButton: {
    flex: 1,
    backgroundColor: '#dc2626',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeOptionButtonDisabled: {
    backgroundColor: '#374151',
    opacity: 0.8,
  },
  timeOptionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'Sora_700Bold',
  },
  extendedTimeOptions: {
    marginTop: 8,
  },
  moreOptionsButton: {
    backgroundColor: '#2C2C2E',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  moreOptionsButtonDisabled: {
    backgroundColor: '#374151',
    opacity: 0.8,
  },
  moreOptionsText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    fontFamily: 'Sora_700Bold',
  },
  comingSoonBadge: {
    alignItems: 'center',
    marginTop: 12,
  },
  comingSoonText: {
    backgroundColor: '#d97706',
    color: '#ffffff',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: '600',
  },
});
```

## Complete Play Page Implementation

### Play Page with Tournaments, Stats, and Spectating
```typescript
// screens/PlayScreen.tsx
import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Progress } from '../ui/Progress';
import {
  Trophy,
  Gamepad2,
  Settings,
  Users,
  Clock,
  Eye,
  Star,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  X,
  AlertTriangle
} from 'lucide-react-native';
import { useQueue } from '../contexts/QueueContext';
import { GameModeSelection } from '../components/dashboard/GameModeSelection';
import { TournamentCreationModal } from '../components/dashboard/TournamentCreationModal';

export const PlayScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateTournament, setShowCreateTournament] = useState(false);
  const [registeredTournaments, setRegisteredTournaments] = useState<string[]>([]);
  const { startQueue } = useQueue();

  // Tournament data
  const [tournaments, setTournaments] = useState([
    {
      id: 'hello-everchess',
      title: 'Hello Everchess',
      timeControl: '5+5',
      participants: '0/32',
      startTime: 'In 3 hours',
      prize: '500 XP',
      status: 'upcoming',
    },
    {
      id: 'weekend-blitz',
      title: 'Weekend Blitz',
      timeControl: '3+2',
      participants: '32/64',
      startTime: 'In 2 hours',
      prize: '500 XP',
      status: 'upcoming',
    },
    {
      id: 'daily-rapid',
      title: 'Daily Rapid',
      timeControl: '10+5',
      participants: '16/32',
      startTime: 'In 5 hours',
      prize: '300 XP',
      status: 'upcoming',
    },
    {
      id: 'grandmaster-invitational',
      title: 'GM Invitational',
      timeControl: '15+10',
      participants: '8/8',
      startTime: 'Tomorrow',
      prize: '1000 XP',
      status: 'upcoming',
    },
  ]);

  // Live games data for spectating
  const liveGames = [
    {
      id: 'game1',
      whitePlayer: 'GrandMaster99',
      whiteRating: 2450,
      blackPlayer: 'ChessWizard',
      blackRating: 2380,
      timeControl: '5+3',
      moveCount: 24,
      viewerCount: 128,
    },
    {
      id: 'game2',
      whitePlayer: 'QueenKnight42',
      whiteRating: 2380,
      blackPlayer: 'BishopPro',
      blackRating: 2310,
      timeControl: '3+2',
      moveCount: 18,
      viewerCount: 76,
    },
    {
      id: 'game3',
      whitePlayer: 'RookMaster',
      whiteRating: 2280,
      blackPlayer: 'KnightRider',
      blackRating: 2210,
      timeControl: '10+5',
      moveCount: 32,
      viewerCount: 54,
    },
  ];

  const handleTournamentRegistration = (tournamentId: string) => {
    if (registeredTournaments.includes(tournamentId)) {
      // Unregister from tournament
      setRegisteredTournaments(registeredTournaments.filter(id => id !== tournamentId));

      // Update participants count
      setTournaments(tournaments.map(tournament => {
        if (tournament.id === tournamentId) {
          const [current, max] = tournament.participants.split('/');
          const newCurrent = parseInt(current) - 1;
          return {
            ...tournament,
            participants: `${newCurrent}/${max}`,
          };
        }
        return tournament;
      }));
    } else {
      // Register for tournament
      setRegisteredTournaments([...registeredTournaments, tournamentId]);

      // Update participants count
      setTournaments(tournaments.map(tournament => {
        if (tournament.id === tournamentId) {
          const [current, max] = tournament.participants.split('/');
          const newCurrent = parseInt(current) + 1;
          return {
            ...tournament,
            participants: `${newCurrent}/${max}`,
          };
        }
        return tournament;
      }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Play Now</Text>
          <Text style={styles.subtitle}>Choose your game mode and start playing</Text>
        </View>

        {/* Game Mode Selection */}
        <GameModeSelection />

        {/* Tournaments and Stats Container */}
        <View style={styles.sectionsContainer}>
          {/* Tournaments Container */}
          <Card style={styles.tournamentsCard}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Tournaments</Text>
              <Trophy size={20} color="#ffd432" />
            </View>

            <ScrollView
              style={styles.tournamentsScrollView}
              showsVerticalScrollIndicator={false}
            >
              {tournaments.map((tournament) => {
                const isRegistered = registeredTournaments.includes(tournament.id);

                return (
                  <TouchableOpacity
                    key={tournament.id}
                    style={[
                      styles.tournamentItem,
                      isRegistered && styles.tournamentItemRegistered
                    ]}
                    onPress={() => handleTournamentRegistration(tournament.id)}
                  >
                    <View style={styles.tournamentHeader}>
                      <Text style={styles.tournamentTitle}>{tournament.title}</Text>
                      <Badge style={[
                        styles.tournamentBadge,
                        isRegistered && styles.tournamentBadgeRegistered
                      ]}>
                        {isRegistered ? (
                          <View style={styles.registeredBadge}>
                            <CheckCircle size={12} color="#ffffff" />
                            <Text style={styles.badgeText}>Registered</Text>
                          </View>
                        ) : (
                          <Text style={styles.badgeText}>Register</Text>
                        )}
                      </Badge>
                    </View>

                    <View style={styles.tournamentDetails}>
                      <View style={styles.tournamentDetailItem}>
                        <Clock size={12} color="#888888" />
                        <Text style={styles.tournamentDetailText}>{tournament.timeControl}</Text>
                      </View>
                      <View style={styles.tournamentDetailItem}>
                        <Users size={12} color="#888888" />
                        <Text style={styles.tournamentDetailText}>{tournament.participants}</Text>
                      </View>
                    </View>

                    <View style={styles.tournamentFooter}>
                      <Text style={styles.tournamentStartTime}>{tournament.startTime}</Text>
                      <Text style={styles.tournamentPrize}>{tournament.prize}</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            <Button
              title="Create Tournament"
              onPress={() => setShowCreateTournament(true)}
              style={styles.createTournamentButton}
            />
          </Card>

          {/* Stats Container */}
          <Card style={styles.statsCard}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Your Stats</Text>
              <Star size={20} color="#ffd432" />
            </View>

            <View style={styles.statsContent}>
              {/* Rating Progress */}
              <View style={styles.ratingSection}>
                <View style={styles.ratingHeader}>
                  <Text style={styles.ratingLabel}>Rating</Text>
                  <View style={styles.ratingValue}>
                    <Text style={styles.ratingNumber}>1250</Text>
                    <Badge style={styles.ratingBadge}>
                      <Text style={styles.ratingBadgeText}>+15</Text>
                    </Badge>
                  </View>
                </View>
                <Progress
                  value={65}
                  style={styles.ratingProgress}
                  trackColor="#2C2C2E"
                  progressColor="#00b6ff"
                />
                <Text style={styles.nextRankText}>Next rank: 1300</Text>
              </View>

              {/* Stats Grid */}
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Games Played</Text>
                  <Text style={styles.statValue}>42</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Win Rate</Text>
                  <Text style={styles.statValue}>58%</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Current Streak</Text>
                  <Text style={[styles.statValue, styles.streakValue]}>3W</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statLabel}>Best Time Control</Text>
                  <Text style={styles.statValue}>5+5</Text>
                </View>
              </View>
            </View>
          </Card>
        </View>

        {/* Spectate Games Container */}
        <Card style={styles.spectateCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Spectate Games</Text>
            <Eye size={20} color="#ffd432" />
            <Badge style={styles.comingSoonBadge}>
              <Text style={styles.comingSoonText}>Coming Soon</Text>
            </Badge>
          </View>

          <View style={styles.liveGamesGrid}>
            {liveGames.map((game) => (
              <TouchableOpacity
                key={game.id}
                style={styles.liveGameItem}
              >
                <View style={styles.liveGameHeader}>
                  <View style={styles.liveIndicator}>
                    <View style={styles.liveDot} />
                    <Text style={styles.liveText}>Live</Text>
                  </View>
                  <View style={styles.viewerCount}>
                    <Eye size={12} color="#888888" />
                    <Text style={styles.viewerCountText}>{game.viewerCount}</Text>
                  </View>
                </View>

                <View style={styles.playersSection}>
                  <View style={styles.player}>
                    <Text style={styles.playerName}>{game.whitePlayer}</Text>
                    <Text style={styles.playerRating}>{game.whiteRating}</Text>
                  </View>
                  <Text style={styles.vsText}>vs</Text>
                  <View style={styles.player}>
                    <Text style={styles.playerName}>{game.blackPlayer}</Text>
                    <Text style={styles.playerRating}>{game.blackRating}</Text>
                  </View>
                </View>

                <View style={styles.gamePreview}>
                  {/* Placeholder for game preview */}
                </View>

                <View style={styles.gameFooter}>
                  <View style={styles.gameDetailItem}>
                    <Clock size={12} color="#888888" />
                    <Text style={styles.gameDetailText}>{game.timeControl}</Text>
                  </View>
                  <Text style={styles.moveCount}>Move {game.moveCount}</Text>
                </View>

                <Button
                  title="Watch Game"
                  style={styles.watchGameButton}
                  titleStyle={styles.watchGameButtonText}
                />
              </TouchableOpacity>
            ))}
          </View>
        </Card>
      </ScrollView>

      {/* Tournament Creation Modal */}
      <TournamentCreationModal
        isVisible={showCreateTournament}
        onClose={() => setShowCreateTournament(false)}
        onCreateTournament={(tournamentData) => {
          // Handle tournament creation
          const newTournament = {
            id: `tournament-${Date.now()}`,
            title: tournamentData.name,
            timeControl: tournamentData.timeControl,
            participants: `0/${tournamentData.size}`,
            startTime: 'In 1 hour',
            prize: '500 XP',
            status: 'upcoming',
          };
          setTournaments([newTournament, ...tournaments]);
          setShowCreateTournament(false);
        }}
      />
    </SafeAreaView>
  );
};

## Battlepass System

### Battlepass Card with Horizontal Scrolling
```typescript
// components/dashboard/BattlepassCard.tsx
import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Animated } from 'react-native';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Progress } from '../ui/Progress';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react-native';
import { BattlepassRewardAnimation } from '../animations/BattlepassRewardAnimation';

interface BattlepassTier {
  tier: number;
  freeReward: {
    type: string;
    amount?: number | string;
    name?: string;
    rarity?: string;
    claimed: boolean;
  };
  premiumReward: {
    type: string;
    amount?: number | string;
    name?: string;
    rarity?: string;
    claimed: boolean;
  };
}

export const BattlepassCard: React.FC = () => {
  const [currentTier, setCurrentTier] = useState(10);
  const [xpProgress, setXpProgress] = useState(210);
  const [xpRequired, setXpRequired] = useState(250);
  const [isPremium, setIsPremium] = useState(false);
  const [visibleTierRange, setVisibleTierRange] = useState({ start: 1, end: 3 });
  const [showRewardAnimation, setShowRewardAnimation] = useState(false);
  const [rewardType, setRewardType] = useState<'chess-set' | 'app-coin' | 'emote'>('app-coin');

  const [allTiers, setAllTiers] = useState<BattlepassTier[]>([
    {
      tier: 1,
      freeReward: { type: 'gold', amount: 50, claimed: true },
      premiumReward: { type: 'gold', amount: 100, claimed: true },
    },
    // ... more tiers
  ]);

  const navigateTiers = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && visibleTierRange.start > 1) {
      setVisibleTierRange({
        start: Math.max(1, visibleTierRange.start - 2),
        end: Math.max(3, visibleTierRange.end - 2),
      });
    } else if (direction === 'next' && visibleTierRange.end < allTiers.length) {
      setVisibleTierRange({
        start: Math.min(allTiers.length - 2, visibleTierRange.start + 2),
        end: Math.min(allTiers.length, visibleTierRange.end + 2),
      });
    }
  };

  const handleClaimAllRewards = () => {
    // Logic for claiming all available rewards
    setRewardType('chess-set');
    setShowRewardAnimation(true);
  };

  const visibleTiers = allTiers.filter(
    tier => tier.tier >= visibleTierRange.start && tier.tier <= visibleTierRange.end
  );

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Star size={20} color="#ffd432" />
          <Text style={styles.title}>Season 1 Battlepass</Text>
        </View>
        <Text style={styles.subtitle}>Your battlepass progress and rewards</Text>
      </View>

      <View style={styles.content}>
        {/* Progress Section */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressText}>Tier {currentTier}/100</Text>
            <Text style={styles.progressText}>{xpProgress}/{xpRequired} XP</Text>
          </View>
          <Progress 
            value={currentTier} 
            max={100} 
            style={styles.progressBar}
            trackColor="#2C2C2E"
            progressColor="#00b6ff"
          />
        </View>

        {/* Rewards Horizontal Scroll */}
        <View style={styles.rewardsContainer}>
          {/* Navigation Arrows */}
          <TouchableOpacity
            style={[styles.navButton, styles.navButtonLeft]}
            onPress={() => navigateTiers('prev')}
            disabled={visibleTierRange.start <= 1}
          >
            <ChevronLeft size={16} color="#ffffff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, styles.navButtonRight]}
            onPress={() => navigateTiers('next')}
            disabled={visibleTierRange.end >= allTiers.length}
          >
            <ChevronRight size={16} color="#ffffff" />
          </TouchableOpacity>

          {/* Free Rewards Row */}
          <View style={styles.rewardsRow}>
            <View style={styles.rewardsRowHeader}>
              <Text style={styles.rewardsRowTitle}>Free</Text>
            </View>
            <View style={styles.rewardsGrid}>
              {visibleTiers.map((tierData) => (
                <BattlepassRewardItem
                  key={`free-${tierData.tier}`}
                  reward={tierData.freeReward}
                  tier={tierData.tier}
                  currentTier={currentTier}
                  isPremium={false}
                />
              ))}
            </View>
          </View>

          {/* Premium Rewards Row */}
          <View style={styles.rewardsRow}>
            <View style={styles.rewardsRowHeader}>
              <Text style={[styles.rewardsRowTitle, styles.premiumTitle]}>Premium</Text>
            </View>
            <View style={styles.rewardsGrid}>
              {visibleTiers.map((tierData) => (
                <BattlepassRewardItem
                  key={`premium-${tierData.tier}`}
                  reward={tierData.premiumReward}
                  tier={tierData.tier}
                  currentTier={currentTier}
                  isPremium={true}
                  isPremiumUnlocked={isPremium}
                />
              ))}
            </View>
          </View>
        </View>

        {/* Claim Button */}
        <Button
          title="Claim All Rewards"
          onPress={handleClaimAllRewards}
          style={styles.claimButton}
        />
      </View>

      {/* Reward Animation */}
      <BattlepassRewardAnimation
        isVisible={showRewardAnimation}
        rewardType={rewardType}
        rewardTier={currentTier + 1}
        onComplete={() => setShowRewardAnimation(false)}
      />
    </Card>
  );
};

// Individual Reward Item Component
const BattlepassRewardItem: React.FC<{
  reward: any;
  tier: number;
  currentTier: number;
  isPremium: boolean;
  isPremiumUnlocked?: boolean;
}> = ({ reward, tier, currentTier, isPremium, isPremiumUnlocked = false }) => {
  const isClaimed = reward.claimed;
  const isClaimable = !reward.claimed && tier <= currentTier && (!isPremium || isPremiumUnlocked);
  const isLocked = tier > currentTier || (isPremium && !isPremiumUnlocked);

  const getBadgeStyle = () => {
    if (isClaimed) return styles.badgeClaimed;
    if (isClaimable) return styles.badgeClaimable;
    return styles.badgeLocked;
  };

  const getBadgeText = () => {
    if (isClaimed) return '✓';
    if (isClaimable) return 'Claim';
    return '🔒';
  };

  return (
    <View style={[styles.rewardItem, isLocked && styles.rewardItemLocked]}>
      <View style={[styles.rewardBadge, getBadgeStyle()]}>
        <Text style={styles.rewardBadgeText}>{getBadgeText()}</Text>
      </View>
      
      <View style={styles.rewardIcon}>
        <RewardIcon type={reward.type} rarity={reward.rarity} />
      </View>
      
      <Text style={styles.rewardText}>
        {reward.type === 'gold' && `${reward.amount} Gold`}
        {reward.type === 'emote' && reward.name}
        {reward.type === 'chess-piece' && reward.rarity}
        {reward.type === 'xp-boost' && `${reward.amount} XP`}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
    borderColor: 'rgba(75, 85, 99, 0.5)',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_700Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#888888',
    fontFamily: 'Lora_400Regular',
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    color: '#888888',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  rewardsContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  navButton: {
    position: 'absolute',
    top: '50%',
    zIndex: 10,
    backgroundColor: '#2C2C2E',
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#374151',
  },
  navButtonLeft: {
    left: -16,
  },
  navButtonRight: {
    right: -16,
  },
  rewardsRow: {
    backgroundColor: '#2C2C2E',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(75, 85, 99, 0.5)',
  },
  rewardsRowHeader: {
    marginBottom: 12,
  },
  rewardsRowTitle: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#2C2C2E',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#374151',
    alignSelf: 'flex-start',
  },
  premiumTitle: {
    backgroundColor: 'rgba(220, 38, 38, 0.2)',
    borderColor: '#dc2626',
    color: '#ff6b6b',
  },
  rewardsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  rewardItem: {
    flex: 1,
    alignItems: 'center',
  },
  rewardItemLocked: {
    opacity: 0.5,
  },
  rewardBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginBottom: 4,
    borderWidth: 1,
  },
  badgeClaimed: {
    backgroundColor: 'rgba(34, 197, 94, 0.2)',
    borderColor: '#22c55e',
  },
  badgeClaimable: {
    backgroundColor: 'rgba(255, 212, 50, 0.2)',
    borderColor: '#ffd432',
  },
  badgeLocked: {
    backgroundColor: '#2C2C2E',
    borderColor: '#374151',
  },
  rewardBadgeText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
  },
  rewardIcon: {
    width: 48,
    height: 48,
    marginBottom: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rewardText: {
    fontSize: 12,
    color: '#ffffff',
    textAlign: 'center',
    fontFamily: 'Lora_400Regular',
  },
  claimButton: {
    backgroundColor: 'linear-gradient(to right, #dc2626, #b91c1c)',
    marginTop: 'auto',
  },
});
```

## Mission System

### Mission Card with Tabbed Interface
```typescript
// components/dashboard/MissionsCard.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { Zap, Gamepad2, Trophy } from 'lucide-react-native';
import { MissionItem } from './MissionItem';
import { XpClaimAnimation } from '../animations/XpClaimAnimation';
import { LevelUpAnimation } from '../animations/LevelUpAnimation';

interface Mission {
  title: string;
  progress: string;
  completed: boolean;
  xp: number;
}

export const MissionsCard: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showXpClaimAnimation, setShowXpClaimAnimation] = useState(false);
  const [showLevelUpAnimation, setShowLevelUpAnimation] = useState(false);
  const [totalXpToClaim, setTotalXpToClaim] = useState(0);
  const [currentLevel, setCurrentLevel] = useState(5);
  const [newLevel, setNewLevel] = useState(6);

  // Daily Missions Data
  const winGamesMissions: Mission[] = [
    { title: 'Win 1 Game', progress: '1/1', completed: true, xp: 25 },
    { title: 'Win 2 Games', progress: '2/2', completed: true, xp: 50 },
    { title: 'Win 3 Games', progress: '2/3', completed: false, xp: 75 },
  ];

  const playGamesMissions: Mission[] = [
    { title: 'Play 1 Game', progress: '1/1', completed: true, xp: 25 },
    { title: 'Play 3 Games', progress: '3/3', completed: true, xp: 50 },
    { title: 'Play 5 Games', progress: '3/5', completed: false, xp: 75 },
  ];

  const tournamentMissions: Mission[] = [
    { title: 'Play 1 Tournament', progress: '0/1', completed: false, xp: 50 },
    { title: 'Play 2 Tournaments', progress: '0/2', completed: false, xp: 100 },
    { title: 'Place Top 3 in Tourney', progress: '0/1', completed: false, xp: 150 },
  ];

  // Weekly Missions Data
  const weeklyWinGamesMissions: Mission[] = [
    { title: 'Win 5 Games', progress: '4/5', completed: false, xp: 100 },
    { title: 'Win 10 Games', progress: '4/10', completed: false, xp: 150 },
    { title: 'Win 15 Games', progress: '4/15', completed: false, xp: 200 },
  ];

  const weeklyPlayGamesMissions: Mission[] = [
    { title: 'Play 10 Games', progress: '7/10', completed: false, xp: 100 },
    { title: 'Play 15 Games', progress: '7/15', completed: false, xp: 150 },
    { title: 'Play 20 Games', progress: '7/20', completed: false, xp: 200 },
  ];

  const weeklyTournamentMissions: Mission[] = [
    { title: 'Play 3 Tournaments', progress: '1/3', completed: false, xp: 150 },
    { title: 'Play 5 Tournaments', progress: '1/5', completed: false, xp: 200 },
    { title: 'Place Top 3 in Tourneys (3)', progress: '0/3', completed: false, xp: 250 },
  ];

  const handleClaimXp = () => {
    // Calculate total XP to claim from completed missions
    let totalXp = 0;

    // Add XP from daily missions that are completed
    winGamesMissions.forEach((mission) => {
      if (mission.completed) totalXp += mission.xp;
    });

    playGamesMissions.forEach((mission) => {
      if (mission.completed) totalXp += mission.xp;
    });

    tournamentMissions.forEach((mission) => {
      if (mission.completed) totalXp += mission.xp;
    });

    // Set the total XP and show the animation
    setTotalXpToClaim(totalXp > 0 ? totalXp : 250); // Default to 250 XP for demo
    setShowXpClaimAnimation(true);

    // Simulate level up check
    const shouldLevelUp = Math.random() > 0.3; // 70% chance to level up for demo
    if (shouldLevelUp) {
      setCurrentLevel(5);
      setNewLevel(6);
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Zap size={20} color="#ffd432" />
          <Text style={styles.title}>Missions</Text>
        </View>
        <Text style={styles.subtitle}>Complete missions to earn XP and rewards</Text>
      </View>

      <View style={styles.content}>
        <Tabs defaultValue="daily" style={styles.tabs}>
          <TabsList style={styles.tabsList}>
            <TabsTrigger value="daily" style={styles.tabsTrigger}>
              <Text style={styles.tabText}>Daily</Text>
            </TabsTrigger>
            <TabsTrigger value="weekly" style={styles.tabsTrigger}>
              <Text style={styles.tabText}>Weekly</Text>
            </TabsTrigger>
          </TabsList>

          <View style={styles.tabsContent}>
            <TabsContent value="daily" style={styles.tabContent}>
              <View style={styles.missionsContainer}>
                {!isLoading ? (
                  <>
                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={winGamesMissions}
                        icon={<Gamepad2 size={24} color="#ffd432" />}
                        initialStep={0}
                      />
                    </View>

                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={playGamesMissions}
                        icon={<Gamepad2 size={24} color="#ffd432" />}
                        initialStep={1}
                      />
                    </View>

                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={tournamentMissions}
                        icon={<Trophy size={24} color="#ffd432" />}
                        initialStep={0}
                      />
                    </View>
                  </>
                ) : (
                  <MissionLoadingSkeleton />
                )}
              </View>
            </TabsContent>

            <TabsContent value="weekly" style={styles.tabContent}>
              <View style={styles.missionsContainer}>
                {!isLoading ? (
                  <>
                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={weeklyWinGamesMissions}
                        icon={<Gamepad2 size={24} color="#ffd432" />}
                        initialStep={0}
                      />
                    </View>

                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={weeklyPlayGamesMissions}
                        icon={<Gamepad2 size={24} color="#ffd432" />}
                        initialStep={0}
                      />
                    </View>

                    <View style={styles.missionGroup}>
                      <MissionItem
                        missions={weeklyTournamentMissions}
                        icon={<Trophy size={24} color="#ffd432" />}
                        initialStep={0}
                      />
                    </View>
                  </>
                ) : (
                  <MissionLoadingSkeleton />
                )}
              </View>
            </TabsContent>
          </View>
        </Tabs>

        <Button
          title="Claim XP"
          onPress={handleClaimXp}
          style={styles.claimButton}
        />
      </View>

      {/* XP Claim Animation */}
      <XpClaimAnimation
        isVisible={showXpClaimAnimation}
        totalXp={totalXpToClaim}
        onComplete={() => {
          setShowXpClaimAnimation(false);
          // Check if we should show level up animation
          const shouldLevelUp = Math.random() > 0.3;
          if (shouldLevelUp) {
            setShowLevelUpAnimation(true);
          }
        }}
      />

      {/* Level Up Animation */}
      <LevelUpAnimation
        isVisible={showLevelUpAnimation}
        oldLevel={currentLevel}
        newLevel={newLevel}
        rewards={[
          { type: 'gold', amount: 100 },
          { type: 'xp-boost', amount: 5 },
        ]}
        onComplete={() => setShowLevelUpAnimation(false)}
      />
    </Card>
  );
};

// Mission Loading Skeleton
const MissionLoadingSkeleton: React.FC = () => (
  <View style={styles.loadingContainer}>
    {[1, 2, 3].map((i) => (
      <View key={i} style={styles.loadingItem}>
        <View style={styles.loadingHeader}>
          <View style={styles.loadingIcon} />
          <View style={styles.loadingTextContainer}>
            <View style={styles.loadingTitle} />
            <View style={styles.loadingSubtitle} />
          </View>
          <View style={styles.loadingXp} />
        </View>
        <View style={styles.loadingProgress} />
      </View>
    ))}
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
    borderColor: 'rgba(75, 85, 99, 0.5)',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_700Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#888888',
    fontFamily: 'Lora_400Regular',
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 8,
  },
  tabs: {
    flex: 1,
  },
  tabsList: {
    flexDirection: 'row',
    backgroundColor: '#2C2C2E',
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  tabsTrigger: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Sora_600SemiBold',
  },
  tabsContent: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
  },
  missionsContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  missionGroup: {
    marginBottom: 32,
  },
  claimButton: {
    backgroundColor: 'linear-gradient(to right, #dc2626, #b91c1c)',
    marginTop: 'auto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  loadingItem: {
    marginBottom: 20,
  },
  loadingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  loadingIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 212, 50, 0.2)',
    marginRight: 12,
  },
  loadingTextContainer: {
    flex: 1,
  },
  loadingTitle: {
    height: 16,
    width: 128,
    backgroundColor: '#2C2C2E',
    borderRadius: 4,
    marginBottom: 8,
  },
  loadingSubtitle: {
    height: 12,
    width: 80,
    backgroundColor: '#2C2C2E',
    borderRadius: 4,
  },
  loadingXp: {
    height: 16,
    width: 64,
    backgroundColor: '#2C2C2E',
    borderRadius: 4,
  },
  loadingProgress: {
    height: 8,
    width: '100%',
    backgroundColor: '#2C2C2E',
    borderRadius: 4,
  },
});
```

### Individual Mission Item Component
```typescript
// components/dashboard/MissionItem.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Progress } from '../ui/Progress';
import { MissionStepIndicator } from './MissionStepIndicator';

interface Mission {
  title: string;
  progress: string;
  completed: boolean;
  xp: number;
}

interface MissionItemProps {
  missions: Mission[];
  icon: React.ReactNode;
  initialStep?: number;
}

export const MissionItem: React.FC<MissionItemProps> = ({
  missions,
  icon,
  initialStep = 0,
}) => {
  const [activeStep, setActiveStep] = useState(initialStep);

  // Current mission based on active step
  const currentMission = missions[activeStep];

  // Parse progress string to get values for progress bar
  const [current, total] = currentMission.progress.split('/').map(Number);
  const progressValue = (current / total) * 100;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.leftSection}>
          {icon}
          <View style={styles.textContainer}>
            <Text style={styles.title}>{currentMission.title}</Text>
            <Text style={styles.progress}>Progress: {currentMission.progress}</Text>
          </View>
        </View>
        <View style={styles.rightSection}>
          <Text style={styles.xpText}>+{currentMission.xp} XP</Text>
          <MissionStepIndicator
            steps={missions.length}
            activeStep={activeStep}
            setActiveStep={setActiveStep}
          />
        </View>
      </View>

      <View style={styles.progressContainer}>
        <Progress
          value={progressValue}
          style={styles.progressBar}
          trackColor="#2C2C2E"
          progressColor="linear-gradient(to right, #00b6ff, #0ea5e9)"
        />
        <View
          style={[
            styles.progressIndicator,
            { left: `${progressValue}%` }
          ]}
        />
      </View>
    </View>
  );
};

// Mission Step Indicator Component
const MissionStepIndicator: React.FC<{
  steps: number;
  activeStep: number;
  setActiveStep: (step: number) => void;
}> = ({ steps, activeStep, setActiveStep }) => {
  return (
    <View style={styles.stepIndicatorContainer}>
      {Array.from({ length: steps }, (_, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.stepIndicator,
            index === activeStep && styles.stepIndicatorActive
          ]}
          onPress={() => setActiveStep(index)}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: '#ffffff',
    marginBottom: 4,
    fontFamily: 'Sora_500Medium',
  },
  progress: {
    fontSize: 14,
    color: '#888888',
    fontFamily: 'Lora_400Regular',
  },
  rightSection: {
    alignItems: 'flex-end',
    gap: 8,
  },
  xpText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#ffd432',
    fontFamily: 'Sora_500Medium',
  },
  progressContainer: {
    position: 'relative',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressIndicator: {
    position: 'absolute',
    top: -2,
    width: 8,
    height: 16,
    backgroundColor: '#00b6ff',
    borderRadius: 4,
    shadowColor: '#00b6ff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.6,
    shadowRadius: 8,
    elevation: 4,
    transform: [{ translateX: -4 }],
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  stepIndicator: {
    width: 12,
    height: 12,
    backgroundColor: '#2C2C2E',
    borderRadius: 2,
    transform: [{ rotate: '45deg' }],
  },
  stepIndicatorActive: {
    backgroundColor: '#ffd432',
  },
});
```

## Chess Sets Management

### Chess Sets Card Component
```typescript
// components/dashboard/ChessSetsCard.tsx
import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Image } from 'expo-image';
import { CastleIcon } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

interface ChessSet {
  id: string;
  name: string;
  rarity: 'Common' | 'Rare' | 'Epic' | 'Legendary';
  owned: boolean;
  selected: boolean;
  image: string;
  color: string;
  bgColor: string;
}

export const ChessSetsCard: React.FC = () => {
  const navigation = useNavigation();
  const [selectedSet, setSelectedSet] = useState('classic-wood');

  const chessSets: ChessSet[] = [
    {
      id: 'classic-wood',
      name: 'Classic Wood',
      rarity: 'Common',
      owned: true,
      selected: true,
      image: '/chess-pawn-blue.png',
      color: 'amber',
      bgColor: 'rgba(217, 119, 6, 0.2)',
    },
    {
      id: 'crystal-set',
      name: 'Crystal Set',
      rarity: 'Rare',
      owned: true,
      selected: false,
      image: '/chess-pawn-blue.png',
      color: 'cyan',
      bgColor: 'rgba(0, 182, 255, 0.2)',
    },
    {
      id: 'dragon-kingdom',
      name: 'Dragon Kingdom',
      rarity: 'Epic',
      owned: true,
      selected: false,
      image: '/chess-pawn-purple.png',
      color: 'purple',
      bgColor: 'rgba(168, 85, 247, 0.2)',
    },
    {
      id: 'chinese-dragon',
      name: 'Chinese Dragon',
      rarity: 'Legendary',
      owned: true,
      selected: false,
      image: '/chess-pawn-red.png',
      color: 'red',
      bgColor: 'rgba(220, 38, 38, 0.2)',
    },
  ];

  const handleSetSelection = (setId: string) => {
    setSelectedSet(setId);
    // In a real app, you would save this selection to the user's profile
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return '#d97706';
      case 'Rare': return '#00b6ff';
      case 'Epic': return '#a855f7';
      case 'Legendary': return '#dc2626';
      default: return '#888888';
    }
  };

  const getImageStyle = (setId: string) => {
    if (setId === 'classic-wood') {
      return {
        tintColor: undefined,
        filter: 'sepia(100%) brightness(60%) saturate(400%) hue-rotate(320deg)',
      };
    }
    return {};
  };

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <CastleIcon size={20} color="#ffd432" />
          <Text style={styles.title}>Chess Sets</Text>
        </View>
        <Text style={styles.subtitle}>Customize your gameplay with chess sets</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {chessSets.map((chessSet) => (
          <TouchableOpacity
            key={chessSet.id}
            style={[
              styles.chessSetItem,
              { backgroundColor: chessSet.bgColor },
              chessSet.selected && styles.chessSetItemSelected
            ]}
            onPress={() => handleSetSelection(chessSet.id)}
          >
            <View style={styles.chessSetIcon}>
              <Image
                source={{ uri: chessSet.image }}
                style={[styles.chessSetImage, getImageStyle(chessSet.id)]}
                contentFit="contain"
              />
            </View>

            <View style={styles.chessSetInfo}>
              <Text style={styles.chessSetName}>{chessSet.name}</Text>
              <Text style={[
                styles.chessSetRarity,
                { color: getRarityColor(chessSet.rarity) }
              ]}>
                {chessSet.rarity}
              </Text>
            </View>

            {chessSet.selected && (
              <View style={styles.selectedIndicator}>
                <Text style={styles.selectedText}>✓</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      <Button
        title="View More Sets"
        onPress={() => navigation.navigate('ChessSets')}
        style={styles.viewMoreButton}
      />
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1C1C1E',
    borderColor: 'rgba(75, 85, 99, 0.5)',
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_700Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#888888',
    fontFamily: 'Lora_400Regular',
  },
  content: {
    flex: 1,
    padding: 16,
    paddingTop: 0,
  },
  chessSetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  chessSetItemSelected: {
    borderColor: '#ffd432',
    shadowColor: '#ffd432',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  chessSetIcon: {
    width: 64,
    height: 64,
    backgroundColor: '#2C2C2E',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  chessSetImage: {
    width: 48,
    height: 48,
  },
  chessSetInfo: {
    flex: 1,
  },
  chessSetName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#ffffff',
    marginBottom: 4,
    fontFamily: 'Sora_500Medium',
  },
  chessSetRarity: {
    fontSize: 12,
    fontFamily: 'Lora_400Regular',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    backgroundColor: '#22c55e',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  viewMoreButton: {
    backgroundColor: 'linear-gradient(to right, #dc2626, #b91c1c)',
    margin: 16,
    marginTop: 0,
  },
});
```

## Animation Components

### XP Claim Animation
```typescript
// components/animations/XpClaimAnimation.tsx
import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { Zap } from 'lucide-react-native';

interface XpClaimAnimationProps {
  isVisible: boolean;
  totalXp: number;
  onComplete: () => void;
}

export const XpClaimAnimation: React.FC<XpClaimAnimationProps> = ({
  isVisible,
  totalXp,
  onComplete,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (isVisible) {
      // Start animation sequence
      Animated.sequence([
        // Fade in and scale up
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]),
        // Hold for a moment
        Animated.delay(1500),
        // Fade out
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: -50,
            duration: 300,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        // Reset animations
        fadeAnim.setValue(0);
        scaleAnim.setValue(0.5);
        slideAnim.setValue(50);
        onComplete();
      });
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <View style={styles.overlay}>
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          },
        ]}
      >
        <View style={styles.iconContainer}>
          <Zap size={48} color="#ffd432" />
        </View>

        <Text style={styles.title}>XP Gained!</Text>
        <Text style={styles.xpAmount}>+{totalXp} XP</Text>

        <View style={styles.sparkles}>
          {Array.from({ length: 6 }, (_, i) => (
            <SparkleParticle key={i} delay={i * 100} />
          ))}
        </View>
      </Animated.View>
    </View>
  );
};

// Sparkle Particle Component
const SparkleParticle: React.FC<{ delay: number }> = ({ delay }) => {
  const sparkleAnim = useRef(new Animated.Value(0)).current;
  const sparkleScale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.timing(sparkleAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.timing(sparkleScale, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(sparkleScale, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
        ]),
        Animated.timing(sparkleAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [delay]);

  const angle = (delay / 100) * 60; // Distribute sparkles in a circle
  const radius = 80;
  const x = Math.cos((angle * Math.PI) / 180) * radius;
  const y = Math.sin((angle * Math.PI) / 180) * radius;

  return (
    <Animated.View
      style={[
        styles.sparkle,
        {
          transform: [
            { translateX: x },
            { translateY: y },
            { scale: sparkleScale },
          ],
          opacity: sparkleAnim,
        },
      ]}
    >
      <Text style={styles.sparkleText}>✨</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1C1C1E',
    borderRadius: 20,
    padding: 40,
    borderWidth: 2,
    borderColor: '#ffd432',
    shadowColor: '#ffd432',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 20,
    elevation: 10,
  },
  iconContainer: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: 'rgba(255, 212, 50, 0.2)',
    borderRadius: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    fontFamily: 'Sora_700Bold',
  },
  xpAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffd432',
    fontFamily: 'Sora_700Bold',
  },
  sparkles: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sparkle: {
    position: 'absolute',
  },
  sparkleText: {
    fontSize: 20,
  },
});
```

### Level Up Animation
```typescript
// components/animations/LevelUpAnimation.tsx
import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Trophy } from 'lucide-react-native';

interface LevelUpAnimationProps {
  isVisible: boolean;
  oldLevel: number;
  newLevel: number;
  rewards: Array<{ type: string; amount: number }>;
  onComplete: () => void;
}

export const LevelUpAnimation: React.FC<LevelUpAnimationProps> = ({
  isVisible,
  oldLevel,
  newLevel,
  rewards,
  onComplete,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.sequence([
        // Dramatic entrance
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            tension: 50,
            friction: 6,
            useNativeDriver: true,
          }),
        ]),
        // Glow effect
        Animated.loop(
          Animated.sequence([
            Animated.timing(glowAnim, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(glowAnim, {
              toValue: 0.3,
              duration: 1000,
              useNativeDriver: true,
            }),
          ]),
          { iterations: 2 }
        ),
        // Hold
        Animated.delay(1000),
        // Exit
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.3,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        fadeAnim.setValue(0);
        scaleAnim.setValue(0.3);
        glowAnim.setValue(0);
        onComplete();
      });
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <View style={styles.overlay}>
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Animated.View
          style={[
            styles.glowContainer,
            {
              opacity: glowAnim,
            },
          ]}
        />

        <View style={styles.iconContainer}>
          <Trophy size={64} color="#ffd432" />
        </View>

        <Text style={styles.title}>LEVEL UP!</Text>

        <View style={styles.levelContainer}>
          <Text style={styles.levelText}>{oldLevel}</Text>
          <Text style={styles.arrow}>→</Text>
          <Text style={styles.levelText}>{newLevel}</Text>
        </View>

        <View style={styles.rewardsContainer}>
          <Text style={styles.rewardsTitle}>Rewards:</Text>
          {rewards.map((reward, index) => (
            <Text key={index} style={styles.rewardText}>
              +{reward.amount} {reward.type}
            </Text>
          ))}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#1C1C1E',
    borderRadius: 24,
    padding: 48,
    borderWidth: 3,
    borderColor: '#ffd432',
    position: 'relative',
  },
  glowContainer: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    backgroundColor: '#ffd432',
    borderRadius: 30,
    opacity: 0.3,
  },
  iconContainer: {
    marginBottom: 24,
    padding: 20,
    backgroundColor: 'rgba(255, 212, 50, 0.2)',
    borderRadius: 60,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffd432',
    marginBottom: 24,
    fontFamily: 'Sora_700Bold',
    textShadowColor: '#ffd432',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 16,
  },
  levelText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'Sora_700Bold',
  },
  arrow: {
    fontSize: 32,
    color: '#ffd432',
    fontWeight: 'bold',
  },
  rewardsContainer: {
    alignItems: 'center',
  },
  rewardsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    fontFamily: 'Sora_700Bold',
  },
  rewardText: {
    fontSize: 16,
    color: '#22c55e',
    fontFamily: 'Sora_500Medium',
    marginBottom: 4,
  },
});
```

## Context Providers

### Queue Context for Game Matchmaking
```typescript
// contexts/QueueContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';

interface QueueContextType {
  isInQueue: boolean;
  queueTime: number;
  gameMode: string | null;
  timeControl: string | null;
  startQueue: (mode: string, time: string) => void;
  cancelQueue: () => void;
}

const QueueContext = createContext<QueueContextType | undefined>(undefined);

export const QueueProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isInQueue, setIsInQueue] = useState(false);
  const [queueTime, setQueueTime] = useState(0);
  const [gameMode, setGameMode] = useState<string | null>(null);
  const [timeControl, setTimeControl] = useState<string | null>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isInQueue) {
      interval = setInterval(() => {
        setQueueTime(prev => prev + 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isInQueue]);

  const startQueue = (mode: string, time: string) => {
    setGameMode(mode);
    setTimeControl(time);
    setIsInQueue(true);
    setQueueTime(0);

    // Simulate finding a match after random time (5-15 seconds)
    const matchTime = Math.random() * 10000 + 5000;
    setTimeout(() => {
      if (isInQueue) {
        // Navigate to game or show match found
        cancelQueue();
        // navigation.navigate('Game', { mode, timeControl: time });
      }
    }, matchTime);
  };

  const cancelQueue = () => {
    setIsInQueue(false);
    setQueueTime(0);
    setGameMode(null);
    setTimeControl(null);
  };

  return (
    <QueueContext.Provider value={{
      isInQueue,
      queueTime,
      gameMode,
      timeControl,
      startQueue,
      cancelQueue,
    }}>
      {children}
    </QueueContext.Provider>
  );
};

export const useQueue = () => {
  const context = useContext(QueueContext);
  if (context === undefined) {
    throw new Error('useQueue must be used within a QueueProvider');
  }
  return context;
};
```

### Status Context for User Status
```typescript
// contexts/StatusContext.tsx
import React, { createContext, useContext, useState } from 'react';

interface UserStatus {
  isOnline: boolean;
  currentActivity: string | null;
  lastSeen: Date | null;
}

interface StatusContextType {
  userStatus: UserStatus;
  updateStatus: (status: Partial<UserStatus>) => void;
  setActivity: (activity: string | null) => void;
}

const StatusContext = createContext<StatusContextType | undefined>(undefined);

export const StatusProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [userStatus, setUserStatus] = useState<UserStatus>({
    isOnline: true,
    currentActivity: null,
    lastSeen: new Date(),
  });

  const updateStatus = (status: Partial<UserStatus>) => {
    setUserStatus(prev => ({ ...prev, ...status }));
  };

  const setActivity = (activity: string | null) => {
    setUserStatus(prev => ({ ...prev, currentActivity: activity }));
  };

  return (
    <StatusContext.Provider value={{
      userStatus,
      updateStatus,
      setActivity,
    }}>
      {children}
    </StatusContext.Provider>
  );
};

export const useStatus = () => {
  const context = useContext(StatusContext);
  if (context === undefined) {
    throw new Error('useStatus must be used within a StatusProvider');
  }
  return context;
};
```

## Navigation Integration

### Dashboard Navigation Setup
```typescript
// navigation/DashboardNavigator.tsx
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { DashboardScreen } from '../screens/DashboardScreen';
import { BattlepassScreen } from '../screens/BattlepassScreen';
import { ChessSetsScreen } from '../screens/ChessSetsScreen';
import { ProfileScreen } from '../screens/ProfileScreen';
import { Gamepad2, Star, CastleIcon, User } from 'lucide-react-native';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const DashboardTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: '#1C1C1E',
          borderTopColor: 'rgba(75, 85, 99, 0.3)',
          borderTopWidth: 1,
        },
        tabBarActiveTintColor: '#ffd432',
        tabBarInactiveTintColor: '#888888',
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Gamepad2 size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Battlepass"
        component={BattlepassScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Star size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Chess Sets"
        component={ChessSetsScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <CastleIcon size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <User size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export const DashboardNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="DashboardTabs" component={DashboardTabs} />
    </Stack.Navigator>
  );
};
```

## Performance Optimizations

### Memoization and Optimization Strategies
```typescript
// hooks/useOptimizedDashboard.ts
import { useMemo, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';

export const useOptimizedDashboard = () => {
  // Memoized data fetching
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['dashboard'],
    queryFn: fetchDashboardData,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Memoized computed values
  const memoizedStats = useMemo(() => {
    if (!dashboardData) return null;

    return {
      totalXp: dashboardData.missions.reduce((sum, mission) =>
        sum + (mission.completed ? mission.xp : 0), 0
      ),
      completedMissions: dashboardData.missions.filter(m => m.completed).length,
      battlepassProgress: (dashboardData.currentTier / 100) * 100,
    };
  }, [dashboardData]);

  // Memoized callbacks
  const handleGameModeSelection = useCallback((mode: string, timeControl: string) => {
    // Optimized game mode selection logic
  }, []);

  const handleMissionClaim = useCallback((missionId: string) => {
    // Optimized mission claim logic
  }, []);

  return {
    dashboardData,
    isLoading,
    memoizedStats,
    handleGameModeSelection,
    handleMissionClaim,
  };
};

// Component optimization with React.memo
export const OptimizedGameModeCard = React.memo<GameModeCardProps>(
  ({ mode, isActive, onPress }) => {
    // Component implementation
  },
  (prevProps, nextProps) => {
    // Custom comparison function
    return (
      prevProps.mode.id === nextProps.mode.id &&
      prevProps.isActive === nextProps.isActive
    );
  }
);
```

## Implementation Checklist

### Required Dependencies
```json
{
  "dependencies": {
    "@react-navigation/native": "^6.1.0",
    "@react-navigation/stack": "^6.3.0",
    "@react-navigation/bottom-tabs": "^6.5.0",
    "@tanstack/react-query": "^4.29.0",
    "react-native-reanimated": "^3.3.0",
    "react-native-gesture-handler": "^2.12.0",
    "react-native-safe-area-context": "^4.7.0",
    "expo-image": "^1.3.0",
    "lucide-react-native": "^0.263.0"
  }
}
```

### File Structure
```
src/
├── components/
│   ├── dashboard/
│   │   ├── DashboardHeader.tsx
│   │   ├── GameModeSelection.tsx
│   │   ├── BattlepassCard.tsx
│   │   ├── MissionsCard.tsx
│   │   ├── MissionItem.tsx
│   │   ├── ChessSetsCard.tsx
│   │   └── LeaderboardCard.tsx
│   ├── animations/
│   │   ├── XpClaimAnimation.tsx
│   │   ├── LevelUpAnimation.tsx
│   │   └── BattlepassRewardAnimation.tsx
│   └── ui/
│       ├── Card.tsx
│       ├── Button.tsx
│       ├── Progress.tsx
│       └── Tabs.tsx
├── contexts/
│   ├── QueueContext.tsx
│   └── StatusContext.tsx
├── screens/
│   ├── DashboardScreen.tsx
│   ├── BattlepassScreen.tsx
│   ├── ChessSetsScreen.tsx
│   └── ProfileScreen.tsx
├── hooks/
│   └── useOptimizedDashboard.ts
└── navigation/
    └── DashboardNavigator.tsx
```

### Integration Steps
1. **Install Dependencies**: Add all required packages to your project
2. **Setup Navigation**: Implement the dashboard navigation structure
3. **Create Context Providers**: Set up queue and status management
4. **Build UI Components**: Implement all dashboard components step by step
5. **Add Animations**: Integrate the animation components
6. **Optimize Performance**: Apply memoization and optimization strategies
7. **Test Integration**: Ensure all components work together seamlessly
8. **Style Customization**: Adapt colors and styling to match your brand

## Backend Integration Architecture

### React Native + Expo API Integration

The dashboard system requires comprehensive backend support adapted for React Native + Expo:

#### **React Native API Service**
```typescript
// services/api.ts - React Native optimized
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { Platform } from 'react-native';

class ApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = Platform.select({
      ios: __DEV__ ? 'http://localhost:3000' : 'https://api.everchess.app',
      android: __DEV__ ? 'http://********:3000' : 'https://api.everchess.app',
      web: 'https://api.everchess.app'
    });
    this.loadAuthToken();
  }

  private async loadAuthToken() {
    try {
      this.authToken = await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('Failed to load auth token:', error);
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new Error('No internet connection');
    }

    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.authToken && { Authorization: `Bearer ${this.authToken}` }),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        timeout: 10000, // 10 second timeout for mobile
      });

      if (!response.ok) {
        throw new ApiError(response.status, `API Error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error occurred');
    }
  }

  // Dashboard-specific endpoints
  async getDashboardData(): Promise<DashboardData> {
    return this.request<DashboardData>('/api/dashboard');
  }

  async getUserProfile(): Promise<UserProfile> {
    return this.request<UserProfile>('/api/users/profile');
  }

  async getBattlepassData(): Promise<BattlepassData> {
    return this.request<BattlepassData>('/api/battlepass/current');
  }

  async getMissions(): Promise<MissionsData> {
    return this.request<MissionsData>('/api/missions');
  }

  async claimMissionXP(): Promise<ClaimXPResponse> {
    return this.request<ClaimXPResponse>('/api/missions/claim-xp', {
      method: 'POST'
    });
  }
}

export const apiService = new ApiService();
```

#### **Tournament System Endpoints**
```typescript
GET /api/tournaments            // List tournaments
POST /api/tournaments           // Create tournament
POST /api/tournaments/:id/register // Register for tournament
GET /api/games/live            // Live games for spectating
```

### React Native WebSocket Integration

Real-time updates optimized for React Native + Expo:

```typescript
// services/websocket.ts - React Native optimized
import { io, Socket } from 'socket.io-client';
import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isAppActive = true;

  constructor() {
    this.setupAppStateListener();
    this.setupNetworkListener();
  }

  private setupAppStateListener() {
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  private setupNetworkListener() {
    NetInfo.addEventListener(state => {
      if (state.isConnected && this.isAppActive && !this.socket?.connected) {
        this.reconnect();
      }
    });
  }

  private handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      this.isAppActive = true;
      if (!this.socket?.connected) {
        this.reconnect();
      }
    } else {
      this.isAppActive = false;
      // Don't disconnect immediately, but stop reconnection attempts
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    }
  };

  async connect(token: string) {
    try {
      const wsUrl = __DEV__
        ? 'ws://localhost:3001'
        : 'wss://ws.everchess.app';

      this.socket = io(wsUrl, {
        auth: { token },
        transports: ['websocket'],
        timeout: 10000,
        forceNew: true
      });

      this.setupEventHandlers();

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.scheduleReconnect();
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      if (this.isAppActive && reason === 'io server disconnect') {
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.scheduleReconnect();
    });

    // Dashboard-specific events
    this.socket.on('mission:completed', this.handleMissionCompleted);
    this.socket.on('battlepass:level_up', this.handleBattlepassLevelUp);
    this.socket.on('match:found', this.handleMatchFound);
  }

  private scheduleReconnect() {
    if (!this.isAppActive || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    this.reconnectTimer = setTimeout(() => {
      this.reconnect();
    }, delay);
  }

  private async reconnect() {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        await this.connect(token);
      }
    } catch (error) {
      console.error('Reconnection failed:', error);
    }
  }

  // Event handlers
  private handleMissionCompleted = (data: any) => {
    // Emit to React Native event system
    // Could use EventEmitter or Context
  };

  private handleBattlepassLevelUp = (data: any) => {
    // Handle battlepass level up
  };

  private handleMatchFound = (data: any) => {
    // Handle match found - could trigger navigation
  };

  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

export const webSocketService = new WebSocketService();
```

### Advanced State Management with Backend Sync

#### **Comprehensive Dashboard Context**

```typescript
// Advanced dashboard context with complete backend integration
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { useWebSocket } from './WebSocketContext';

type Mission = {
  id: string;
  title: string;
  steps: {
    target: number;
    progress: number;
    completed: boolean;
    xp: number;
    special?: string;
  }[];
};

type BattlepassData = {
  season: number;
  currentTier: number;
  maxTier: number;
  currentXP: number;
  xpRequired: number;
  isPremium: boolean;
  nextReward: {
    tier: number;
    type: string;
    name: string;
    imageUrl: string;
  };
};

type DashboardContextType = {
  // Data
  userProfile: any;
  battlepass: BattlepassData | null;
  missions: { daily: Mission[]; weekly: Mission[] };
  chessSets: { owned: any[]; selected: string | null };
  leaderboard: { topPlayers: any[]; userRank: number | null };

  // State
  loading: boolean;
  error: string | null;

  // Actions
  refreshDashboardData: () => Promise<void>;
  claimMissionXP: () => Promise<number>;
  claimBattlepassReward: (tier: number) => Promise<void>;
  selectChessSet: (setId: string) => Promise<void>;

  // Computed values
  completedMissions: { daily: number; weekly: number };
  pendingXP: number;
  unclaimedRewards: number;
};

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export function DashboardProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const { socket, connected } = useWebSocket();

  // State
  const [userProfile, setUserProfile] = useState(null);
  const [battlepass, setBattlepass] = useState<BattlepassData | null>(null);
  const [missions, setMissions] = useState<{ daily: Mission[]; weekly: Mission[] }>({
    daily: [],
    weekly: []
  });
  const [chessSets, setChessSets] = useState<{ owned: any[]; selected: string | null }>({
    owned: [],
    selected: null
  });
  const [leaderboard, setLeaderboard] = useState<{ topPlayers: any[]; userRank: number | null }>({
    topPlayers: [],
    userRank: null
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // React Native optimized fetch functions
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use React Native optimized API service
      const [profileData, battlepassData, missionsData, chessSetsData, leaderboardData] = await Promise.allSettled([
        apiService.getUserProfile(),
        apiService.getBattlepassData(),
        apiService.getMissions(),
        apiService.getChessSetsPreview(),
        apiService.getLeaderboardPreview()
      ]);

      // Handle partial failures gracefully (important for mobile)
      if (profileData.status === 'fulfilled') {
        setUserProfile(profileData.value);
      }

      if (battlepassData.status === 'fulfilled') {
        setBattlepass({
          season: battlepassData.value.season,
          currentTier: battlepassData.value.user_progress.current_tier,
          maxTier: battlepassData.value.user_progress.max_tier,
          currentXP: battlepassData.value.user_progress.current_xp,
          xpRequired: battlepassData.value.user_progress.xp_required,
          isPremium: battlepassData.value.user_progress.premium,
          nextReward: battlepassData.value.next_reward
        });
      }

      if (missionsData.status === 'fulfilled') {
        setMissions(missionsData.value);
      }

      if (chessSetsData.status === 'fulfilled') {
        setChessSets(chessSetsData.value);
      }

      if (leaderboardData.status === 'fulfilled') {
        setLeaderboard(leaderboardData.value);
      }

      // Check if any critical data failed to load
      const failedRequests = [profileData, battlepassData, missionsData, chessSetsData, leaderboardData]
        .filter(result => result.status === 'rejected');

      if (failedRequests.length > 0) {
        console.warn(`${failedRequests.length} dashboard requests failed`);
        // Could show partial error state
      }

    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user?.token]);

  const refreshDashboardData = useCallback(async () => {
    await fetchDashboardData();
  }, [fetchDashboardData]);

  // Optimistic updates for better UX
  const claimMissionXP = useCallback(async () => {
    try {
      // Calculate pending XP
      const pendingXP = missions.daily.reduce((total, mission) => {
        return total + mission.steps.filter(step => step.completed).reduce((stepTotal, step) => stepTotal + step.xp, 0);
      }, 0) + missions.weekly.reduce((total, mission) => {
        return total + mission.steps.filter(step => step.completed).reduce((stepTotal, step) => stepTotal + step.xp, 0);
      }, 0);

      // Optimistically update battlepass XP
      if (battlepass) {
        setBattlepass(prev => prev ? {
          ...prev,
          currentXP: prev.currentXP + pendingXP
        } : null);
      }

      // Make API call
      const response = await fetch(`${process.env.API_URL}/api/missions/claim-xp`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${user?.token}` }
      });

      const result = await response.json();

      // Update with actual result
      setBattlepass(prev => prev ? {
        ...prev,
        currentXP: result.new_xp_total,
        currentTier: result.tier_progress.current_tier
      } : null);

      // Refresh missions to clear completed ones
      await fetchDashboardData();

      return result.total_xp_claimed;
    } catch (error) {
      // Revert optimistic update on error
      await fetchDashboardData();
      throw error;
    }
  }, [missions, battlepass, user?.token, fetchDashboardData]);

  const claimBattlepassReward = useCallback(async (tier: number) => {
    try {
      const response = await fetch(`${process.env.API_URL}/api/battlepass/claim/${tier}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.token}`
        },
        body: JSON.stringify({ reward_type: 'free' })
      });

      if (!response.ok) throw new Error('Failed to claim reward');

      // Refresh data to show claimed reward
      await fetchDashboardData();
    } catch (error) {
      throw error;
    }
  }, [user?.token, fetchDashboardData]);

  const selectChessSet = useCallback(async (setId: string) => {
    try {
      const response = await fetch(`${process.env.API_URL}/api/inventory/chess-sets/select/${setId}`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${user?.token}` }
      });

      if (!response.ok) throw new Error('Failed to select chess set');

      const result = await response.json();

      setChessSets(prev => ({
        ...prev,
        selected: result.selected_set
      }));
    } catch (error) {
      throw error;
    }
  }, [user?.token]);

  // WebSocket event handlers
  useEffect(() => {
    if (!socket || !connected) return;

    socket.on('mission_progress_update', (data) => {
      // Update mission progress in real-time
      setMissions(prev => ({
        ...prev,
        [data.mission_type]: prev[data.mission_type as keyof typeof prev].map(mission =>
          mission.id === data.mission_id ? { ...mission, steps: data.updated_steps } : mission
        )
      }));
    });

    socket.on('battlepass_progress_update', (data) => {
      setBattlepass(prev => prev ? {
        ...prev,
        currentXP: data.current_xp,
        currentTier: data.current_tier
      } : null);
    });

    socket.on('leaderboard_update', (data) => {
      setLeaderboard(prev => ({
        ...prev,
        topPlayers: data.top_players,
        userRank: data.user_rank
      }));
    });

    return () => {
      socket.off('mission_progress_update');
      socket.off('battlepass_progress_update');
      socket.off('leaderboard_update');
    };
  }, [socket, connected]);

  // Computed values
  const completedMissions = React.useMemo(() => {
    const dailyCompleted = missions.daily.reduce((total, mission) => {
      return total + mission.steps.filter(step => step.completed).length;
    }, 0);

    const weeklyCompleted = missions.weekly.reduce((total, mission) => {
      return total + mission.steps.filter(step => step.completed).length;
    }, 0);

    return { daily: dailyCompleted, weekly: weeklyCompleted };
  }, [missions]);

  const pendingXP = React.useMemo(() => {
    return missions.daily.reduce((total, mission) => {
      return total + mission.steps.filter(step => step.completed).reduce((stepTotal, step) => stepTotal + step.xp, 0);
    }, 0) + missions.weekly.reduce((total, mission) => {
      return total + mission.steps.filter(step => step.completed).reduce((stepTotal, step) => stepTotal + step.xp, 0);
    }, 0);
  }, [missions]);

  const unclaimedRewards = React.useMemo(() => {
    // Calculate unclaimed battlepass rewards based on current tier
    return battlepass ? Math.max(0, battlepass.currentTier - 1) : 0;
  }, [battlepass]);

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user, fetchDashboardData]);

  return (
    <DashboardContext.Provider value={{
      userProfile,
      battlepass,
      missions,
      chessSets,
      leaderboard,
      loading,
      error,
      refreshDashboardData,
      claimMissionXP,
      claimBattlepassReward,
      selectChessSet,
      completedMissions,
      pendingXP,
      unclaimedRewards
    }}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboard() {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
}
```

### Error Handling and Recovery

```typescript
// Comprehensive error handling for dashboard operations
export class DashboardApiError extends Error {
  constructor(
    public status: number,
    message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'DashboardApiError';
  }
}

export const handleDashboardError = (error: any): string => {
  if (error instanceof DashboardApiError) {
    switch (error.status) {
      case 401:
        return 'Please log in to continue';
      case 403:
        return 'You do not have permission to perform this action';
      case 429:
        return 'Too many requests. Please wait a moment and try again';
      case 500:
        return 'Server error. Please try again later';
      default:
        return error.message || 'An unexpected error occurred';
    }
  }

  if (error.name === 'NetworkError') {
    return 'Network connection error. Please check your internet connection';
  }

  return 'An unexpected error occurred. Please try again';
};
```

This comprehensive guide provides everything needed to implement the complete Everchess dashboard system in React Native + Expo, maintaining all the functionality and visual appeal of the original web implementation while ensuring robust backend integration and real-time capabilities.
