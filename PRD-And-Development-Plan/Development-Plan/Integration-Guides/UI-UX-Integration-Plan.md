# Everchess UI/UX Integration Plan

## Overview

This document outlines the integration strategy for incorporating the complete UI/UX Foundation into the Everchess cross-platform application, including the comprehensive dashboard system with all interactive elements, animations, and functionality. The plan ensures seamless adaptation of the web-based design system to React Native while maintaining design consistency and optimal user experience across all platforms, covering the complete dashboard, tournament system, battlepass, missions, chess sets management, and all animation systems.

## Design System Foundation

### Core Design Principles

1. **Dark Theme First**: Primary dark theme with elegant contrast
2. **Immersive Experience**: 3D chess integration with polished UI
3. **Gamification Elements**: Progress bars, animations, and reward systems
4. **Cross-Platform Consistency**: Unified experience across web and mobile
5. **Accessibility**: WCAG 2.1 AA compliance across all components

### Color Palette

```typescript
// theme/colors.ts
export const colors = {
  // Primary Colors
  primary: '#5856D6',
  primaryDark: '#4A49C7',
  primaryLight: '#6B6AE0',
  
  // Background Colors
  background: '#000000',
  surface: '#1C1C1E',
  surfaceSecondary: '#2C2C2E',
  surfaceElevated: '#3A3A3C',
  
  // Text Colors
  textPrimary: '#FFFFFF',
  textSecondary: '#EBEBF5',
  textTertiary: '#EBEBF599',
  textDisabled: '#EBEBF54D',
  
  // Accent Colors
  success: '#30D158',
  warning: '#FF9F0A',
  error: '#FF453A',
  info: '#64D2FF',
  
  // Chess-Specific Colors
  chessLight: '#F0D9B5',
  chessDark: '#B58863',
  chessHighlight: '#FFE135',
  chessCapture: '#FF6B6B',
  
  // Rarity Colors
  common: '#8E8E93',
  uncommon: '#30D158',
  rare: '#007AFF',
  epic: '#AF52DE',
  legendary: '#FF9500',
  mythic: '#FF2D92',
};
```

### Typography System

```typescript
// theme/typography.ts
export const typography = {
  // Font Families
  fontFamily: {
    primary: 'Sora',
    secondary: 'Lora',
    system: 'System',
  },
  
  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // Font Weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};
```

## Component Architecture

### Base UI Components

#### Button Component
```typescript
// components/ui/Button.tsx
import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { colors, typography } from '../../theme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  style?: any;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  style,
}) => {
  const buttonStyles = [
    styles.base,
    styles[variant],
    styles[size],
    disabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`text${variant.charAt(0).toUpperCase() + variant.slice(1)}`],
    styles[`text${size.charAt(0).toUpperCase() + size.slice(1)}`],
  ];

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={variant === 'primary' ? colors.textPrimary : colors.primary} />
      ) : (
        <Text style={textStyles}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  primary: {
    backgroundColor: colors.primary,
  },
  secondary: {
    backgroundColor: colors.surface,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
  },
  sm: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    minHeight: 32,
  },
  md: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 44,
  },
  lg: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    minHeight: 52,
  },
  disabled: {
    opacity: 0.5,
  },
  text: {
    fontFamily: typography.fontFamily.primary,
    fontWeight: typography.fontWeight.semibold,
  },
  textPrimary: {
    color: colors.textPrimary,
  },
  textSecondary: {
    color: colors.textPrimary,
  },
  textOutline: {
    color: colors.primary,
  },
  textGhost: {
    color: colors.primary,
  },
  textSm: {
    fontSize: typography.fontSize.sm,
  },
  textMd: {
    fontSize: typography.fontSize.base,
  },
  textLg: {
    fontSize: typography.fontSize.lg,
  },
});
```

#### Card Component
```typescript
// components/ui/Card.tsx
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../theme';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  style?: any;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  style,
}) => {
  const cardStyles = [
    styles.base,
    styles[variant],
    styles[`padding${padding.charAt(0).toUpperCase() + padding.slice(1)}`],
    style,
  ];

  return <View style={cardStyles}>{children}</View>;
};

const styles = StyleSheet.create({
  base: {
    borderRadius: 12,
  },
  default: {
    backgroundColor: colors.surface,
  },
  elevated: {
    backgroundColor: colors.surfaceElevated,
    shadowColor: colors.background,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  outlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.surfaceSecondary,
  },
  paddingNone: {
    padding: 0,
  },
  paddingSm: {
    padding: 12,
  },
  paddingMd: {
    padding: 16,
  },
  paddingLg: {
    padding: 20,
  },
});
```

### Game-Specific Components

#### Chess Board Component
```typescript
// components/game/ChessBoard.tsx
import React, { useRef, useState } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Canvas } from '@react-three/fiber/native';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei/native';

interface ChessBoardProps {
  gameState: string; // FEN notation
  onMove: (from: string, to: string) => void;
  playerSide: 'white' | 'black';
  selectedSet: string;
}

export const ChessBoard: React.FC<ChessBoardProps> = ({
  gameState,
  onMove,
  playerSide,
  selectedSet,
}) => {
  const [selectedSquare, setSelectedSquare] = useState<string | null>(null);
  const [validMoves, setValidMoves] = useState<string[]>([]);

  return (
    <View style={styles.container}>
      <Canvas
        camera={{ position: [0, 8, 8], fov: 50 }}
        style={styles.canvas}
      >
        <ambientLight intensity={0.4} />
        <directionalLight position={[5, 10, 5]} intensity={0.8} castShadow />
        
        {/* Chess Board */}
        <BoardMesh />
        
        {/* Chess Pieces */}
        <PiecesGroup
          fen={gameState}
          selectedSquare={selectedSquare}
          validMoves={validMoves}
          onSquarePress={handleSquarePress}
          selectedSet={selectedSet}
        />
        
        {/* Environment */}
        <Environment preset="studio" />
        
        {/* Camera Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          maxPolarAngle={Math.PI / 2}
          minDistance={5}
          maxDistance={15}
        />
      </Canvas>
    </View>
  );

  function handleSquarePress(square: string) {
    if (selectedSquare) {
      if (validMoves.includes(square)) {
        onMove(selectedSquare, square);
        setSelectedSquare(null);
        setValidMoves([]);
      } else {
        setSelectedSquare(square);
        // Calculate valid moves for new selection
        // setValidMoves(calculateValidMoves(square, gameState));
      }
    } else {
      setSelectedSquare(square);
      // setValidMoves(calculateValidMoves(square, gameState));
    }
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  canvas: {
    flex: 1,
  },
});
```

### Screen Components

#### Dashboard Home Screen
```typescript
// screens/DashboardHomeScreen.tsx
import React from 'react';
import { ScrollView, View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { BattlepassProgress } from '../components/battlepass/BattlepassProgress';
import { MissionsList } from '../components/missions/MissionsList';
import { ChessSetPreview } from '../components/inventory/ChessSetPreview';
import { LeaderboardPreview } from '../components/ranking/LeaderboardPreview';
import { colors, typography } from '../theme';

export const DashboardHomeScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Welcome back, Player</Text>
          <Text style={styles.subtitle}>Ready for your next game?</Text>
        </View>

        {/* Game Mode Selection */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Play Chess</Text>
          <View style={styles.gameModesGrid}>
            <Button title="Ranked" variant="primary" style={styles.gameModeButton} />
            <Button title="Casual" variant="secondary" style={styles.gameModeButton} />
            <Button title="Tournament" variant="outline" style={styles.gameModeButton} />
            <Button title="Custom" variant="ghost" style={styles.gameModeButton} />
          </View>
        </Card>

        {/* Battlepass Progress */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Season Progress</Text>
          <BattlepassProgress />
        </Card>

        {/* Daily Missions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Missions</Text>
          <MissionsList type="daily" limit={3} />
        </Card>

        {/* Chess Sets Preview */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Your Chess Sets</Text>
          <ChessSetPreview limit={4} />
        </Card>

        {/* Leaderboard Preview */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Leaderboard</Text>
          <LeaderboardPreview limit={5} />
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 24,
  },
  title: {
    fontSize: typography.fontSize['3xl'],
    fontWeight: typography.fontWeight.bold,
    color: colors.textPrimary,
    fontFamily: typography.fontFamily.primary,
  },
  subtitle: {
    fontSize: typography.fontSize.lg,
    color: colors.textSecondary,
    marginTop: 4,
    fontFamily: typography.fontFamily.primary,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semibold,
    color: colors.textPrimary,
    marginBottom: 16,
    fontFamily: typography.fontFamily.primary,
  },
  gameModesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  gameModeButton: {
    flex: 1,
    minWidth: '45%',
  },
});
```

## Animation System

### Core Animations
```typescript
// animations/index.ts
import { Animated, Easing } from 'react-native';

export const animations = {
  // Fade animations
  fadeIn: (value: Animated.Value, duration = 300) => {
    return Animated.timing(value, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    });
  },

  fadeOut: (value: Animated.Value, duration = 300) => {
    return Animated.timing(value, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.quad),
      useNativeDriver: true,
    });
  },

  // Scale animations
  scaleIn: (value: Animated.Value, duration = 300) => {
    return Animated.timing(value, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.back(1.7)),
      useNativeDriver: true,
    });
  },

  // Slide animations
  slideInFromBottom: (value: Animated.Value, duration = 300) => {
    return Animated.timing(value, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.quad),
      useNativeDriver: true,
    });
  },

  // XP gain animation
  xpGain: (value: Animated.Value, duration = 1000) => {
    return Animated.sequence([
      Animated.timing(value, {
        toValue: 1.2,
        duration: duration * 0.3,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(value, {
        toValue: 1,
        duration: duration * 0.7,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]);
  },

  // Level up animation
  levelUp: (value: Animated.Value, duration = 2000) => {
    return Animated.sequence([
      Animated.timing(value, {
        toValue: 1.5,
        duration: duration * 0.2,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(value, {
        toValue: 1,
        duration: duration * 0.8,
        easing: Easing.elastic(1),
        useNativeDriver: true,
      }),
    ]);
  },
};
```

## Responsive Design Strategy

### Screen Size Breakpoints
```typescript
// utils/responsive.ts
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export const breakpoints = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

export const isTablet = width >= breakpoints.md;
export const isDesktop = width >= breakpoints.lg;

export const responsive = {
  width,
  height,
  isTablet,
  isDesktop,
  
  // Responsive values
  padding: isTablet ? 24 : 16,
  margin: isTablet ? 20 : 12,
  fontSize: (base: number) => isTablet ? base * 1.2 : base,
  
  // Grid system
  columns: isDesktop ? 4 : isTablet ? 3 : 2,
};
```

## Platform-Specific Adaptations

### iOS Adaptations
- Use native iOS navigation patterns
- Implement haptic feedback for chess moves
- Support Apple Sign In
- Optimize for iPhone and iPad screen sizes

### Android Adaptations
- Use Material Design navigation patterns
- Implement Android-specific animations
- Support Google Sign In
- Optimize for various Android screen sizes and densities

### Web PWA Adaptations
- Implement keyboard shortcuts for chess moves
- Support mouse hover states
- Optimize for desktop screen sizes
- Implement web-specific features (clipboard, notifications)

## Performance Optimization

### 3D Rendering Optimization
- Use Level of Detail (LOD) for chess pieces
- Implement object pooling for frequently used models
- Optimize textures for mobile devices
- Use efficient lighting setups

### UI Performance
- Implement FlatList for large data sets
- Use React.memo for expensive components
- Optimize image loading and caching
- Implement proper key props for lists

### Animation Performance
- Use native driver for all animations
- Avoid animating layout properties
- Implement gesture responders efficiently
- Use InteractionManager for heavy operations

## Testing Strategy

### Visual Regression Testing
- Screenshot testing for UI components
- Cross-platform visual consistency checks
- Responsive design validation

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation testing
- Color contrast validation
- Focus management testing

### Performance Testing
- 3D rendering performance benchmarks
- Animation smoothness validation
- Memory usage monitoring
- Battery usage optimization

## Backend Integration Patterns

### React Native + Expo API Integration Architecture

The UI/UX system integrates seamlessly with backend services through React Native optimized patterns:

```typescript
// React Native + Expo optimized API service
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

export class ReactNativeApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = Platform.select({
      ios: __DEV__ ? 'http://localhost:3000' : 'https://api.everchess.app',
      android: __DEV__ ? 'http://********:3000' : 'https://api.everchess.app',
      web: 'https://api.everchess.app'
    });
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      // Use SecureStore for sensitive data like auth tokens
      this.authToken = await SecureStore.getItemAsync('auth_token');
    } catch (error) {
      console.error('Failed to load auth token:', error);
    }
  }

  async setAuthToken(token: string) {
    this.authToken = token;
    try {
      await SecureStore.setItemAsync('auth_token', token);
    } catch (error) {
      console.error('Failed to store auth token:', error);
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Check network connectivity first
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new ApiError(0, 'No internet connection');
    }

    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': `Everchess/${Platform.OS}`,
      ...(this.authToken && { Authorization: `Bearer ${this.authToken}` }),
      ...options.headers,
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new ApiError(response.status, `API Error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new ApiError(0, 'Request timeout');
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error occurred');
    }
  }

  // CRUD operations optimized for React Native
  async get<T>(endpoint: string, useCache: boolean = false): Promise<T> {
    if (useCache) {
      const cacheKey = `api_cache_${endpoint}`;
      try {
        const cached = await AsyncStorage.getItem(cacheKey);
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          // Cache valid for 5 minutes
          if (Date.now() - timestamp < 300000) {
            return data;
          }
        }
      } catch (error) {
        console.warn('Cache read failed:', error);
      }
    }

    const data = await this.request<T>(endpoint);

    if (useCache) {
      try {
        await AsyncStorage.setItem(`api_cache_${endpoint}`, JSON.stringify({
          data,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.warn('Cache write failed:', error);
      }
    }

    return data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // React Native specific methods
  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('api_cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  async logout(): Promise<void> {
    this.authToken = null;
    try {
      await SecureStore.deleteItemAsync('auth_token');
      await this.clearCache();
    } catch (error) {
      console.error('Logout cleanup failed:', error);
    }
  }
}

export const apiService = new ReactNativeApiService();
```

### Real-time Data Integration

```typescript
// WebSocket integration for real-time UI updates
export class WebSocketService {
  private socket: WebSocket | null = null;
  private eventHandlers: Map<string, Function[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(url: string, token: string) {
    try {
      this.socket = new WebSocket(`${url}?token=${token}`);

      this.socket.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data.type, data.payload);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.socket.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect(url, token);
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  private handleMessage(type: string, payload: any) {
    const handlers = this.eventHandlers.get(type) || [];
    handlers.forEach(handler => {
      try {
        handler(payload);
      } catch (error) {
        console.error(`Error in WebSocket handler for ${type}:`, error);
      }
    });
  }

  private attemptReconnect(url: string, token: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

      setTimeout(() => {
        console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect(url, token);
      }, delay);
    }
  }

  on(eventType: string, handler: Function) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: Function) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  send(type: string, payload: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({ type, payload }));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}
```

### Data Synchronization Strategy

```typescript
// Optimistic updates with rollback capability
export class DataSyncManager {
  private pendingUpdates: Map<string, any> = new Map();
  private rollbackData: Map<string, any> = new Map();

  async optimisticUpdate<T>(
    key: string,
    updateFn: (current: T) => T,
    apiCall: () => Promise<T>,
    currentData: T
  ): Promise<T> {
    // Store original data for potential rollback
    this.rollbackData.set(key, currentData);

    // Apply optimistic update
    const optimisticData = updateFn(currentData);
    this.pendingUpdates.set(key, optimisticData);

    try {
      // Make API call
      const result = await apiCall();

      // Clear pending update on success
      this.pendingUpdates.delete(key);
      this.rollbackData.delete(key);

      return result;
    } catch (error) {
      // Rollback on error
      const originalData = this.rollbackData.get(key);
      this.pendingUpdates.delete(key);
      this.rollbackData.delete(key);

      throw error;
    }
  }

  isPending(key: string): boolean {
    return this.pendingUpdates.has(key);
  }

  getPendingData<T>(key: string): T | null {
    return this.pendingUpdates.get(key) || null;
  }
}
```

## Advanced Performance Optimization

### React Native Data Fetching Optimization

```typescript
// React Native optimized hook with background app state handling
import { useEffect, useState, useMemo, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';

export function useOptimizedDashboardData(userId: string) {
  const [profile, setProfile] = useState(null);
  const [battlepass, setBattlepass] = useState(null);
  const [missions, setMissions] = useState(null);
  const [chessSets, setChessSets] = useState(null);
  const [leaderboard, setLeaderboard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  const fetchDashboardData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      // Don't fetch if we just fetched recently (unless forced)
      const now = Date.now();
      if (!forceRefresh && now - lastFetch < 30000) { // 30 seconds
        return;
      }

      setLoading(true);
      setError(null);

      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        throw new Error('No internet connection');
      }

      // Use React Native API service with caching
      const [profileRes, battlepassRes, missionsRes, chessSetsRes, leaderboardRes] = await Promise.allSettled([
        apiService.get('/api/users/profile', true), // Use cache
        apiService.get('/api/battlepass/current', true),
        apiService.get('/api/missions', false), // Don't cache missions (real-time)
        apiService.get('/api/inventory/chess-sets/preview', true),
        apiService.get('/api/leaderboard/preview', true)
      ]);

      // Handle partial failures gracefully (critical for mobile)
      if (profileRes.status === 'fulfilled') {
        setProfile(profileRes.value);
      } else {
        console.warn('Profile fetch failed:', profileRes.reason);
      }

      if (battlepassRes.status === 'fulfilled') {
        setBattlepass(battlepassRes.value);
      } else {
        console.warn('Battlepass fetch failed:', battlepassRes.reason);
      }

      if (missionsRes.status === 'fulfilled') {
        setMissions(missionsRes.value);
      } else {
        console.warn('Missions fetch failed:', missionsRes.reason);
      }

      if (chessSetsRes.status === 'fulfilled') {
        setChessSets(chessSetsRes.value);
      } else {
        console.warn('Chess sets fetch failed:', chessSetsRes.reason);
      }

      if (leaderboardRes.status === 'fulfilled') {
        setLeaderboard(leaderboardRes.value);
      } else {
        console.warn('Leaderboard fetch failed:', leaderboardRes.reason);
      }

      setLastFetch(now);

    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [userId, lastFetch]);

  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      fetchDashboardData();
    }, [fetchDashboardData])
  );

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Refresh data when app becomes active
        fetchDashboardData();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [fetchDashboardData]);

  // Initial fetch
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Memoize derived data for performance
  const completedMissions = useMemo(() => {
    if (!missions) return { daily: 0, weekly: 0 };

    const dailyCompleted = missions.daily?.reduce((total, mission) => {
      const completedSteps = mission.steps?.filter(step => step.completed).length || 0;
      return total + completedSteps;
    }, 0) || 0;

    const weeklyCompleted = missions.weekly?.reduce((total, mission) => {
      const completedSteps = mission.steps?.filter(step => step.completed).length || 0;
      return total + completedSteps;
    }, 0) || 0;

    return { daily: dailyCompleted, weekly: weeklyCompleted };
  }, [missions]);

  return {
    profile,
    battlepass,
    missions,
    chessSets,
    leaderboard,
    completedMissions,
    loading,
    error,
    refresh: () => fetchDashboardData(true)
  };
}
```

### React Optimization Patterns

```typescript
// Memoized components for performance
export const MemoizedMissionItem = React.memo(({ mission, onComplete }) => {
  const handleComplete = useCallback((stepIndex: number) => {
    onComplete(mission.id, stepIndex);
  }, [mission.id, onComplete]);

  return (
    <div className="mission-item">
      <h3>{mission.title}</h3>
      {mission.steps.map((step, index) => (
        <MissionStep
          key={index}
          step={step}
          onComplete={() => handleComplete(index)}
        />
      ))}
    </div>
  );
});

// Virtualized list for large datasets
export const VirtualizedTournamentList = ({ tournaments }) => {
  const rowRenderer = useCallback(({ index, key, style }) => (
    <div key={key} style={style}>
      <TournamentItem tournament={tournaments[index]} />
    </div>
  ), [tournaments]);

  return (
    <AutoSizer>
      {({ height, width }) => (
        <List
          height={height}
          width={width}
          rowCount={tournaments.length}
          rowHeight={80}
          rowRenderer={rowRenderer}
        />
      )}
    </AutoSizer>
  );
};
```

### Caching Strategy Implementation

```typescript
// Advanced caching service
export class CacheService {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();

  set(key: string, data: any, ttlSeconds: number = 300): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    });
  }

  get<T>(key: string): T | null {
    const cached = this.cache.get(key);

    if (!cached) {
      return null;
    }

    // Check if cache has expired
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }
}

// Usage in API service
const cache = new CacheService();

export const fetchWithCache = async <T>(url: string, ttlSeconds: number = 300): Promise<T> => {
  const cacheKey = `api:${url}`;
  const cached = cache.get<T>(cacheKey);

  if (cached) {
    return cached;
  }

  const response = await fetchWithErrorHandling(url);
  const data = await response.json();

  cache.set(cacheKey, data, ttlSeconds);

  return data;
};
```

This UI/UX integration plan provides a comprehensive foundation for implementing the design system across the Everchess cross-platform application while maintaining consistency, performance, accessibility standards, and robust backend integration patterns.
