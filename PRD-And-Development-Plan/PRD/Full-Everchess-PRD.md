# UNIFIED EVERCHESS MVP PRD & DEVELOPMENT PLAN

## Executive Summary

Everchess is a next-generation chess platform that combines traditional chess gameplay with modern gaming features, social interactions, and Web3 capabilities. This unified Product Requirements Document (PRD) integrates comprehensive feature specifications with detailed technical implementation guidelines to create a complete roadmap for the Everchess Minimum Viable Product (MVP).

The platform aims to deliver a seamless, engaging, and secure chess experience across both web (Progressive Web App) and mobile (native app) environments.

As the sole founder, owner, and developer, you aim to complete and launch the MVP within a self-directed 90-day timeframe, with flexibility to adjust as needed. This document provides a comprehensive guide for development, ensuring the platform is built to last with scalability, security, and user engagement at its core.

This PRD synthesizes the best elements of previous planning documents, combining the technical depth and implementation specificity of the Codeguide approach with the user-centered design and feature completeness of the Grok-3 methodology. The result is a comprehensive blueprint for building a platform that is not only technically sound but also deeply engaging for users.

| **Project Overview** | **Everchess Platform** |
|----------------------|-------------------------|
| **Project Description:**    | Next-generation immersive 3D chess app and platform |
| **Target Platforms:** | Cross-platform (Web PWA, iOS, Android) via React Native + Expo |
| **Development Timeline:** | AI-driven iterative development (as fast as prompts can be implemented) |
| **Target Launch:** | Q3 2025 |

### Vision & Value Proposition

Everchess transforms traditional chess by integrating modern gaming elements, social features, and Web3 capabilities into an immersive 3D chess experience. The platform delivers significant value through:

- **Immersive 3D Gameplay**: Three Fiber-powered interactive chess experience with collectible NFT chess sets
- **Cross-Platform Accessibility**: Seamless play across web and mobile devices with consistent experience
- **Web3 Integration**: Ownership of unique chess sets as NFTs with staking and earning mechanisms
- **Social & Competitive Elements**: Friend system, tournaments, and progression that keeps players engaged

### Key Features

- **Complete Dashboard System**: Comprehensive dashboard with game mode selection, battlepass progression, mission system, chess sets management, tournaments, spectating, profile and ranking systems
- **Interactive Game Modes**: Dynamic time controls, expandable options, queue system, tournament creation and management
- **Advanced Progression**: XP leveling with animations, comprehensive battlepass with horizontal scrolling rewards, daily/weekly missions with step indicators
- **Social & Tournament Features**: Tournament creation, registration, live management, spectating system with chat, friend system, and leaderboards
- **Chess Sets & Market**: Collection display with rarity system, selection interface, market purchasing system
- **Animation Systems**: XP claim animations with particle effects, level up animations, battlepass reward animations
- **Core Chess Gameplay**: Standard chess rules with multiple game modes (Ranked, Wagers, Tournaments, Custom Games)
- **Web3 Integration**: NFT chess sets, staking mechanics, and secure wallet connections via Para Wallet SDK
- **Immersive 3D Experience**: Three Fiber-powered 3D chessboards and pieces with drag-and-drop functionality

### Target Users

1. **Casual Players**: Looking for quick games, progression, and social features
2. **Competitive Players**: Seeking ranked modes, tournaments, and skill-based matchmaking
3. **Chess Set Collectors**: Interested in acquiring and showcasing unique NFT chess sets

### Technical Highlights

- **Frontend**: React Native + Expo with Three Fiber for cross-platform support and 3D rendering
- **Backend**: Node.js + Express with Socket.IO for real-time gameplay
- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Web3**: Solana blockchain integration via Para Wallet SDK for NFTs and wallet connections
- **Authentication**: Para Wallet SDK for unified Web2/Web3 authentication
- **Hosting**: AWS infrastructure (EC2, S3, CloudFront) for scalability and global reach

### Market Differentiation

Everchess positions itself uniquely between traditional chess platforms (which lack modern gaming and web3 elements) and existing blockchain games (which often lack a PMF and scalable strategy). By focusing on delivering a premium chess experience, with Web3 as an enhancement rather than the core focus, Everchess appeals to both web3 users and traditional chess players looking for more engaging experiences.

### Implementation Approach

As the sole founder, owner, and developer, you'll implement Everchess through a five-phase development process:

1. **Foundation Setup**: Cross-platform environment, backend infrastructure, authentication
2. **Core Gameplay & UI**: 3D chessboard, game logic, matchmaking, post-game summaries
3. **Gamified Functionalities**: Progression, battlepass, missions, achievements, social features
4. **Key Integrations**: Web3 connectivity, NFT implementation, in-app store
5. **Testing, Security & Launch**: Comprehensive testing, optimization, deployment

This document provides a comprehensive roadmap for building a platform that is technically sound, engaging for users, and positioned for long-term growth.

---

## Table of Contents

1. [Project Vision & Objectives](#project-vision--objectives)
2. [User Personas](#user-personas)
3. [Feature Specifications](#feature-specifications)
4. [Technical Architecture](#technical-architecture)
5. [Implementation Plan](#implementation-plan)
6. [Security Guidelines](#security-guidelines)
7. [Development Standards](#development-standards)
8. [Scaling Strategy](#scaling-strategy)
9. [Appendices](#appendices)

---

## Project Vision & Objectives

### Vision Statement

Everchess aims to revolutionize the chess experience by combining traditional gameplay with modern gaming elements, social interactions, and blockchain technology. The platform will provide an immersive, engaging, and secure environment where players can compete, connect, and collect unique digital assets.

### Core Objectives

1. **Create an Engaging Chess Experience**
   - Deliver smooth, responsive gameplay across web and mobile platforms
   - Provide immersive 3D visuals with customizable chess sets and environments (see [NFT Chess Set Integration](#43-nft-chess-set-integration))
   - Support multiple game modes to cater to different player preferences (see [Game Modes](#23-game-modes-and-selection-ui))

2. **Foster a Vibrant Community**
   - Implement robust social features including friend systems and in-game chat
   - Create competitive elements through leaderboards and tournaments
   - Develop mission systems and progression mechanics for sustained engagement

3. **Integrate Web3 Capabilities**
   - Enable ownership of unique digital assets (NFT chess sets)
   - Implement secure wallet connections and transactions
   - Establish foundations for future blockchain features (staking, wagers)

4. **Ensure Platform Security and Scalability**
   - Implement comprehensive security measures for both Web2 and Web3 components
   - Design a scalable architecture that can grow with the user base
   - Create a robust backend capable of handling real-time gameplay and social interactions

5. **Deliver a Polished, Professional Product**
   - Maintain high standards for UI/UX design across all platforms
   - Ensure accessibility and usability for players of all skill levels
   - Create a stable, reliable platform with minimal downtime and bugs

### Success Metrics

- **User Engagement**: Average session time >15 minutes, >3 sessions per week per user
- **Retention**: 30-day retention rate >40%, 90-day retention rate >25%
- **Social Interaction**: >50% of users adding at least one friend, >30% using in-game chat
- **Progression**: >60% of users completing at least one daily mission per week
- **Web3 Adoption**: >15% of users connecting a wallet, >5% acquiring an NFT
- **Performance**: <100ms latency for move validation, <99.9% uptime
- **Security**: Zero critical vulnerabilities, <5 medium vulnerabilities at launch

### Strategic Alignment

Everchess aligns with key industry trends including:
- The growing popularity of chess (accelerated by streaming and online tournaments)
- The gamification of traditional games with progression systems and social features
- The maturation of Web3 technologies with focus on utility and user experience
- The expansion of cross-platform gaming experiences (mobile, web, desktop)

By positioning at the intersection of these trends, Everchess creates a unique value proposition that can attract and retain diverse player segments while establishing a foundation for long-term growth and innovation.

## User Personas

This section outlines the primary user types that Everchess is designed to serve, providing insights into their demographics, behaviors, goals, and pain points. Understanding these personas ensures the platform aligns with user needs and expectations, enhancing engagement and satisfaction.

### 1. Casual Chess Player

**Demographics:** 
- Ages 18-45, mixed genders
- Global audience with varying chess experience
- Primarily mobile users with occasional web access

**Behaviors:**
- Plays chess for enjoyment during leisure time
- Prefers quick, casual matches with friends over intense competition
- Values progression systems and achievable rewards
- Enjoys social interactions and mission-based incentives

**Goals:**
- Experience a relaxed, low-pressure chess environment
- Connect with friends through gameplay
- Progress through levels and complete missions for rewards
- Customize their gaming experience with collectible items

**Pain Points:**
- Feels overwhelmed by complicated interfaces or advanced strategies
- Frustrated by slow matchmaking or lack of social engagement
- Dislikes steep learning curves and elitist communities
- Struggles with traditional chess platforms that lack modern gaming elements

**How Everchess Helps:**
- Provides Custom Games and Free Tournaments for laid-back play
- Implements an intuitive, user-friendly interface with clear tutorials
- Includes a friend system for social play and missions for achievable rewards
- Offers progression systems that reward participation, not just winning

### 2. Competitive Chess Enthusiast

**Demographics:**
- Ages 16-40, predominantly male
- Global audience with moderate to advanced chess skills
- Uses both mobile and web platforms for optimal performance

**Behaviors:**
- Plays frequently, often in ranked matches or tournaments
- Focuses on skill improvement, leaderboard rankings, and mission completion
- Studies game history and analyzes performance
- Engages with social features to challenge friends and climb leaderboards

**Goals:**
- Compete in ranked matches and tournaments to hone skills
- Gain recognition through Elo ratings and tournament victories
- Track performance metrics and improve gameplay strategies
- Connect with other skilled players for challenging matches

**Pain Points:**
- Encounters cheaters or uneven matchmaking
- Lacks tools for analyzing games and improving
- Experiences inconsistent platform performance affecting gameplay
- Finds limited competitive options on existing platforms

**How Everchess Helps:**
- Employs strong anti-cheating measures like move validation and oversight
- Offers Ranked mode with skill-based matchmaking and detailed leaderboards
- Provides performance tracking and plans for post-MVP analysis tools
- Ensures low-latency, responsive gameplay for competitive integrity

### 3. Web3 Enthusiast

**Demographics:**
- Ages 18-35, tech-savvy
- Interested in blockchain, NFTs, and digital ownership
- Early adopters of new technologies and platforms

**Behaviors:**
- Participates in blockchain gaming and decentralized finance (DeFi)
- Collects and trades digital assets across platforms
- Values transparency, ownership, and interoperability
- Explores innovative applications of Web3 technology

**Goals:**
- Own unique digital assets with utility and potential value
- Participate in blockchain-integrated gaming experiences
- Earn rewards through gameplay and asset staking
- Connect with like-minded Web3 enthusiasts

**Pain Points:**
- Struggles with confusing or insecure wallet integrations
- Encounters high transaction fees or slow blockchain networks
- Experiences poor gameplay in many blockchain-based games
- Faces limited options for chess in the Web3 space

**How Everchess Helps:**
- Utilizes Solana for fast, low-cost transactions
- Offers secure, user-friendly wallet connections via Para Wallet SDK with seamless onboarding
- Integrates Web3 features without compromising gameplay quality
- Provides unique NFT chess sets with in-game utility and visual appeal
- Supports multiple authentication methods (email, social, wallet) through Para's unified system

### 4. Collector and Customizer

**Demographics:**
- Ages 14-30, mixed genders
- Global audience with varying chess experience
- Appreciates visual aesthetics and personalization

**Behaviors:**
- Loves personalizing their gaming experience
- Collects skins, avatars, and cosmetic items
- Completes challenges to unlock rare items
- Enjoys sharing achievements and collections with friends

**Goals:**
- Unlock rare or exclusive chess sets and skins
- Customize their gaming experience to reflect personal style
- Display achievements and collections on their profile
- Participate in limited-time events for special items

**Pain Points:**
- Finds limited customization options on traditional chess platforms
- Faces high costs or challenges in obtaining desired items
- Lacks ways to showcase collections and achievements
- Experiences repetitive visual elements in existing chess games

**How Everchess Helps:**
- Provides diverse skins via the battlepass, missions, and store
- Integrates NFT collections for unique, tradable chess sets
- Offers profile customization to showcase achievements and items
- Creates regular events and challenges for exclusive rewards

### 5. Social Gamer

**Demographics:**
- Ages 16-35, mixed genders
- Global audience with varying chess experience
- Values community and social interactions

**Behaviors:**
- Plays games to connect with friends and meet new people
- Engages in chat, challenges, and community events
- Values leaderboards and social recognition
- Participates in team activities and shared goals

**Goals:**
- Build a network of friends within the game
- Communicate and collaborate with others during gameplay
- Compete on leaderboards and complete missions with friends
- Participate in community events and tournaments

**Pain Points:**
- Lacks social features on traditional chess platforms
- Experiences toxic behavior or inadequate moderation
- Finds limited options for team play or shared activities
- Struggles to connect with friends across different skill levels

**How Everchess Helps:**
- Offers a robust friend system with status updates and game invitations
- Provides in-game chat with effective moderation tools
- Creates leaderboards that include friend comparisons
- Implements daily and weekly missions that encourage social play

### 6. Tournament Organizer (Future Persona)

**Demographics:**
- Ages 20-50, chess coaches, community leaders
- Organizes events for schools, clubs, or online communities
- Values structure, fairness, and community engagement

**Behaviors:**
- Organizes and runs chess events for various skill levels
- Seeks platforms that enable custom tournaments and ensure fair play
- Manages participant registrations and tournament brackets
- Promotes events and engages with the chess community

**Goals:**
- Create and manage tournaments with customizable rules
- Ensure fair play and appropriate matchmaking
- Track participant performance and distribute rewards
- Build and maintain an engaged community

**Pain Points:**
- Lacks tools for efficient tournament creation and management
- Struggles to enforce rules and prevent cheating
- Faces challenges in organizing events across time zones
- Finds limited options for customizing tournament structures

**How Everchess Helps (Post-MVP):**
- Will offer tournament creation tools with customizable settings
- Will include anti-cheating measures and admin oversight features
- Will provide scheduling tools for global participation
- Will implement flexible tournament structures and reward distribution

---

These personas guide the development of Everchess, ensuring that features, interfaces, and experiences align with the needs and expectations of diverse user groups. By addressing the specific goals and pain points of each persona, Everchess creates a platform that appeals to a wide audience while delivering targeted value to each segment.

## Feature Specifications

This section details the specific functionalities that the Everchess platform must deliver to meet user needs and achieve its objectives. Each feature includes both high-level descriptions and detailed technical implementation guidelines to ensure comprehensive development.

### 1. Core Gameplay

#### 1.1 Chess Rules Implementation

**Description:**
- Standard chess rules implementation with complete move validation
- Support for special moves (castling, en passant, pawn promotion)
- Game state tracking including check, checkmate, and stalemate detection
- Time controls with various presets (bullet, blitz, rapid, classical)

**Technical Implementation:**
- Utilize `chess.js` library for core rule validation and state management
- Implement custom wrapper for integrating with Socket.IO and Three Fiber
- Store game state in Supabase with the following schema:

```sql
CREATE TABLE game_states (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  white_player_id UUID REFERENCES users(id),
  black_player_id UUID REFERENCES users(id),
  current_fen VARCHAR(100) NOT NULL,
  move_history JSONB NOT NULL DEFAULT '[]',
  game_status VARCHAR(20) NOT NULL DEFAULT 'active',
  time_control JSONB NOT NULL,
  game_mode VARCHAR(20) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
POST /api/games/create
  - Creates a new game with specified parameters
  - Request: { gameMode, timeControl, opponentId? }
  - Response: { gameId, initialState }

POST /api/games/:gameId/move
  - Validates and applies a move to the game
  - Request: { from, to, promotion? }
  - Response: { valid, newState, capturedPiece? }

GET /api/games/:gameId
  - Retrieves the current state of a game
  - Response: { gameState, players, timeRemaining }
```

#### 1.2 Game Modes

**Description:**
- **Ranked Mode**: Competitive matches with Elo-based matchmaking
- **Wager Mode**: Matches where players stake in-app coins (EVC)
- **Tournament Mode**: Structured competitions with brackets and prizes
- **Custom Games**: User-defined matches with flexible settings

**Technical Implementation:**
- Implement mode-specific logic in separate modules for maintainability
- Create matchmaking service with the following components:

```javascript
// Ranked matchmaking algorithm
function findRankedMatch(userId, eloRating) {
  // Find players within ±100 Elo range
  // Prioritize waiting time and connection quality
  // Return matched player or add to waiting pool
}

// Wager matchmaking
function findWagerMatch(userId, wagerAmount) {
  // Find players with matching wager amounts
  // Verify both players have sufficient balance
  // Create escrow for wager amount
}
```

- Database schema for matchmaking:

```sql
CREATE TABLE matchmaking_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  game_mode VARCHAR(20) NOT NULL,
  elo_rating INTEGER,
  wager_amount INTEGER,
  entry_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  parameters JSONB
);
```

#### 1.3 3D Chessboard and Pieces

**Description:**
- Immersive 3D chessboard with realistic lighting and physics
- Multiple camera angles with smooth transitions
- Support for custom chess sets and board themes
- Responsive design adapting to different screen sizes

**Technical Implementation:**
- Implement using Three Fiber (React Three Fiber) for 3D rendering
- Optimize 3D models for mobile performance:
  - Low-poly models (<3000 polygons per piece)
  - Compressed textures (WebP format)
  - Level of detail (LOD) implementation for distant pieces
- Component structure:

```jsx
// Main 3D chessboard component
const ChessBoard = ({ gameState, onMove, playerSide }) => {
  // Three.js scene setup
  // Board and piece rendering
  // Interaction handlers
  return (
    <Canvas shadows>
      <ambientLight intensity={0.4} />
      <directionalLight position={[5, 10, 5]} castShadow />
      <BoardPlane />
      <PiecesGroup 
        fen={gameState.fen} 
        onPieceMove={handleMove}
        playerSide={playerSide}
      />
      <CameraControls />
    </Canvas>
  );
};
```

- Implement drag-and-drop controls for mobile and click-based controls for web
- Create asset loading system with caching for improved performance

#### 1.4 Move Validation and Game State Management

**Description:**
- Real-time move validation ensuring legal chess moves
- Game state synchronization between players
- History tracking for move replay and analysis
- Support for game interruptions and resumption

**Technical Implementation:**
- Server-side validation to prevent cheating:

```javascript
// Server-side move validation
function validateMove(gameId, playerId, from, to, promotion) {
  const game = getGameState(gameId);
  
  // Verify it's the player's turn
  if (game.currentTurn !== getPlayerColor(playerId)) {
    return { valid: false, reason: 'not_your_turn' };
  }
  
  // Validate move using chess.js
  const move = game.chess.move({ from, to, promotion });
  if (!move) {
    return { valid: false, reason: 'invalid_move' };
  }
  
  // Update game state in database
  updateGameState(gameId, game.chess.fen(), move);
  
  return { 
    valid: true, 
    newState: game.chess.fen(),
    move: move
  };
}
```

- Implement Socket.IO events for real-time updates:

```javascript
// Socket.IO event handlers
io.on('connection', (socket) => {
  // Join game room
  socket.on('join_game', (gameId) => {
    socket.join(`game:${gameId}`);
  });
  
  // Handle move
  socket.on('make_move', async (data) => {
    const result = await validateMove(
      data.gameId,
      socket.userId,
      data.from,
      data.to,
      data.promotion
    );
    
    if (result.valid) {
      // Broadcast move to both players
      io.to(`game:${data.gameId}`).emit('move_made', {
        from: data.from,
        to: data.to,
        promotion: data.promotion,
        newState: result.newState,
        move: result.move
      });
      
      // Check for game end conditions
      if (result.gameOver) {
        io.to(`game:${data.gameId}`).emit('game_over', {
          result: result.gameOverReason,
          winner: result.winner
        });
        
        // Update player stats and trigger rewards
        processGameCompletion(data.gameId);
      }
    } else {
      // Send error only to the player who attempted the move
      socket.emit('move_error', {
        reason: result.reason
      });
    }
  });
});
```

### 2. User Authentication and Profile Management

#### 2.1 Web2 Authentication

**Description:**
- Email and password registration and login
- Social login options (Google, Facebook)
- Password recovery and account verification
- Session management with secure tokens

**Technical Implementation:**
- Integrate Para Wallet SDK for unified authentication services
- Implement JWT-based authentication flow with Para's token management
- Create the following API endpoints:

```
POST /api/auth/register
  - Registers a new user
  - Request: { email, password, username }
  - Response: { userId, token, profile }

POST /api/auth/login
  - Authenticates a user
  - Request: { email, password } or { provider, token }
  - Response: { userId, token, profile }

POST /api/auth/forgot-password
  - Initiates password reset
  - Request: { email }
  - Response: { success }

POST /api/auth/reset-password
  - Resets password with token
  - Request: { token, newPassword }
  - Response: { success }
```

- Database schema for user authentication:

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  username VARCHAR(50) UNIQUE NOT NULL,
  auth_provider VARCHAR(20),
  auth_provider_id VARCHAR(255),
  email_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2.2 Web3 Authentication

**Description:**
- Solana wallet connection (Phantom, Solflare)
- Wallet signature verification
- Linking Web2 and Web3 accounts
- Transaction signing for blockchain actions

**Technical Implementation:**
- Integrate Solana wallet adapters
- Implement signature-based authentication:

```javascript
// Wallet authentication flow
async function authenticateWallet(publicKey) {
  // Generate a random message for signing
  const message = `Authenticate with Everchess: ${generateNonce()}`;
  
  // Request signature from wallet
  const signedMessage = await requestSignature(publicKey, message);
  
  // Verify signature on server
  const isValid = verifySignature(publicKey, message, signedMessage);
  
  if (isValid) {
    // Find or create user account
    const user = await findOrCreateUserByWallet(publicKey);
    
    // Generate JWT token
    const token = generateJWT(user.id);
    
    return { userId: user.id, token };
  }
  
  throw new Error('Invalid signature');
}
```

- Database schema for wallet connections:

```sql
CREATE TABLE wallet_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  wallet_address VARCHAR(255) NOT NULL,
  wallet_type VARCHAR(50) NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(wallet_address, wallet_type)
);
```

#### 2.3 Profile Management

**Description:**
- User profile creation and editing
- Avatar selection and customization
- Stats tracking and display
- Privacy settings and preferences

**Technical Implementation:**
- Create profile management components and services
- Implement the following API endpoints:

```
GET /api/users/profile
  - Retrieves the current user's profile
  - Response: { profile, stats, settings }

PUT /api/users/profile
  - Updates the user's profile
  - Request: { username?, avatar?, settings? }
  - Response: { profile }

GET /api/users/:userId/public
  - Retrieves public profile of another user
  - Response: { username, avatar, stats }
```

- Database schema for user profiles:

```sql
CREATE TABLE user_profiles (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  display_name VARCHAR(50),
  avatar_url VARCHAR(255),
  bio TEXT,
  country_code VARCHAR(2),
  language_preference VARCHAR(5),
  privacy_settings JSONB DEFAULT '{"profile": "public", "activity": "friends", "inventory": "public"}',
  ui_preferences JSONB DEFAULT '{"theme": "dark", "sound": true, "notifications": true}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Progression and Rewards System

#### 3.1 XP and Leveling System

**Description:**
- Experience points (XP) earned through gameplay and mission completion
- Level progression with increasing XP requirements
- Level-up rewards and unlockable content
- Visual feedback for XP gains and level advancement

**Technical Implementation:**
- XP earning structure:
  - +25 XP for wins
  - +15 XP for losses (participation reward)
  - +50 XP for each completed daily/weekly mission
  - +10 XP bonus for first game of the day

- Level progression formula:
  - Level 1: 50 XP
  - Subsequent levels: Previous level requirement + 5% increase
  - Cap at 500 XP per level (around level 47)

```javascript
// XP calculation for leveling
function calculateXpForLevel(level) {
  const baseXp = 50;
  const growthRate = 0.05;
  const maxXp = 500;
  
  let requiredXp = baseXp * Math.pow(1 + growthRate, level - 1);
  return Math.min(Math.round(requiredXp), maxXp);
}

// Calculate user level from total XP
function calculateLevelFromXp(totalXp) {
  let level = 1;
  let xpRequired = calculateXpForLevel(level);
  let remainingXp = totalXp;
  
  while (remainingXp >= xpRequired) {
    remainingXp -= xpRequired;
    level++;
    xpRequired = calculateXpForLevel(level);
  }
  
  return {
    level,
    currentXp: remainingXp,
    nextLevelXp: xpRequired,
    progress: remainingXp / xpRequired
  };
}
```

- Database schema for XP and levels:

```sql
CREATE TABLE user_progression (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  total_xp INTEGER NOT NULL DEFAULT 0,
  current_level INTEGER NOT NULL DEFAULT 1,
  last_xp_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  daily_xp_cap_reached BOOLEAN DEFAULT FALSE,
  daily_xp_reset_time TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '1 day',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE xp_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  amount INTEGER NOT NULL,
  source VARCHAR(50) NOT NULL,
  reference_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement real-time XP updates via Socket.IO and Supabase subscriptions
- Create visual feedback components for XP gains and level-ups

#### 3.2 Battlepass System

**Description:**
- Tiered reward system (50 tiers) with free and premium tracks
- Progression tied to XP earned during the season
- Rewards including skins, EVC, emotes, and profile customizations
- Seasonal refreshes with new themes and rewards

**Technical Implementation:**
- Battlepass progression formula:
  - Tier 1: 100 XP
  - Subsequent tiers: Previous tier requirement + 20% increase
  - Cap at 350 XP per tier (around tier 35)

```javascript
// XP calculation for battlepass tiers
function calculateXpForTier(tier) {
  const baseXp = 100;
  const growthRate = 0.20;
  const maxXp = 350;
  
  let requiredXp = baseXp * Math.pow(1 + growthRate, tier - 1);
  return Math.min(Math.round(requiredXp), maxXp);
}
```

- Database schema for battlepass:

```sql
CREATE TABLE battlepass_seasons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  premium_price INTEGER NOT NULL,
  total_tiers INTEGER NOT NULL DEFAULT 50,
  is_active BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE battlepass_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  season_id UUID REFERENCES battlepass_seasons(id),
  tier INTEGER NOT NULL,
  reward_type VARCHAR(50) NOT NULL,
  reward_id UUID NOT NULL,
  is_premium BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_battlepass (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  season_id UUID REFERENCES battlepass_seasons(id),
  has_premium BOOLEAN DEFAULT FALSE,
  current_tier INTEGER DEFAULT 0,
  current_xp INTEGER DEFAULT 0,
  claimed_rewards JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, season_id)
);
```

- Implement the following API endpoints:

```
GET /api/battlepass/current
  - Retrieves the current battlepass season and user progress
  - Response: { season, userProgress, rewards }

POST /api/battlepass/purchase
  - Purchases the premium battlepass
  - Request: { seasonId }
  - Response: { success, userBattlepass }

POST /api/battlepass/claim-reward
  - Claims a battlepass reward
  - Request: { seasonId, tier }
  - Response: { success, reward, newInventory }
```

- Create UI components for battlepass visualization and reward claiming

#### 3.3 Daily and Weekly Missions

**Description:**
- Daily missions: 1 Win, 3 Wins, 5 Wins, 1 Draw, 5 Games Played
- Weekly missions: 10 Wins, 15 Wins, 25 Wins, 3 Draws, 25 Games Played
- Each mission rewards 50 XP upon completion
- Mission tracking and completion notifications
- Mission reset timers (daily at midnight UTC, weekly on Monday)

**Technical Implementation:**
- Mission generation and tracking system:

```javascript
// Mission types and requirements
const MISSION_TYPES = {
  DAILY: [
    { type: 'wins', count: 1, xp: 50 },
    { type: 'wins', count: 3, xp: 50 },
    { type: 'wins', count: 5, xp: 50 },
    { type: 'draws', count: 1, xp: 50 },
    { type: 'games_played', count: 5, xp: 50 }
  ],
  WEEKLY: [
    { type: 'wins', count: 10, xp: 50 },
    { type: 'wins', count: 15, xp: 50 },
    { type: 'wins', count: 25, xp: 50 },
    { type: 'draws', count: 3, xp: 50 },
    { type: 'games_played', count: 25, xp: 50 }
  ]
};

// Generate missions for a user
async function generateMissions(userId, missionType) {
  const templates = MISSION_TYPES[missionType];
  const missions = templates.map(template => ({
    userId,
    type: template.type,
    required: template.count,
    progress: 0,
    completed: false,
    xpReward: template.xp,
    missionType
  }));
  
  return await insertMissions(userId, missions, missionType);
}
```

- Database schema for missions:

```sql
CREATE TABLE user_missions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  mission_type VARCHAR(10) NOT NULL, -- 'DAILY' or 'WEEKLY'
  objective_type VARCHAR(50) NOT NULL,
  required_count INTEGER NOT NULL,
  current_progress INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
  xp_reward INTEGER NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
GET /api/missions
  - Retrieves current daily and weekly missions
  - Response: { daily: [...missions], weekly: [...missions] }

POST /api/missions/:missionId/claim
  - Claims the reward for a completed mission
  - Response: { success, xpAwarded, newTotalXp }
```

- Create scheduled jobs for mission resets:

```javascript
// Daily mission reset (midnight UTC)
cron.schedule('0 0 * * *', async () => {
  await resetDailyMissions();
});

// Weekly mission reset (Monday midnight UTC)
cron.schedule('0 0 * * 1', async () => {
  await resetWeeklyMissions();
});
```

- Implement real-time mission progress updates during gameplay

#### 3.4 Achievement System

**Description:**
- Long-term achievements for significant milestones
- Categories including Gameplay, Collection, Social, and Mastery
- Tiered achievements (Bronze, Silver, Gold, Platinum)
- Rewards including badges, titles, and exclusive cosmetics

**Technical Implementation:**
- Achievement definition structure:

```javascript
// Achievement categories and definitions
const ACHIEVEMENTS = {
  GAMEPLAY: [
    {
      id: 'first_victory',
      name: 'First Victory',
      description: 'Win your first chess match',
      tiers: [{ count: 1, reward: { type: 'badge', id: 'first_win_badge' } }]
    },
    {
      id: 'winning_streak',
      name: 'Winning Streak',
      description: 'Win consecutive matches',
      tiers: [
        { count: 3, reward: { type: 'xp', amount: 100 } },
        { count: 5, reward: { type: 'title', id: 'streak_master' } },
        { count: 10, reward: { type: 'skin', id: 'streak_pieces' } }
      ]
    }
  ],
  COLLECTION: [
    // Collection achievements
  ],
  SOCIAL: [
    // Social achievements
  ],
  MASTERY: [
    // Mastery achievements
  ]
};
```

- Database schema for achievements:

```sql
CREATE TABLE achievements (
  id VARCHAR(50) PRIMARY KEY,
  category VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  icon_url VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE achievement_tiers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  achievement_id VARCHAR(50) REFERENCES achievements(id),
  tier INTEGER NOT NULL,
  requirement INTEGER NOT NULL,
  reward_type VARCHAR(50) NOT NULL,
  reward_id VARCHAR(50),
  reward_amount INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(achievement_id, tier)
);

CREATE TABLE user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  achievement_id VARCHAR(50) REFERENCES achievements(id),
  current_progress INTEGER NOT NULL DEFAULT 0,
  highest_tier_completed INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, achievement_id)
);
```

- Implement achievement tracking service:

```javascript
// Check and update achievements after relevant actions
async function checkAchievements(userId, action, metadata) {
  const relevantAchievements = findRelevantAchievements(action);
  
  for (const achievement of relevantAchievements) {
    // Update progress based on action
    const progress = await updateAchievementProgress(userId, achievement.id, action, metadata);
    
    // Check if any tiers were completed
    const completedTiers = checkCompletedTiers(achievement, progress);
    
    if (completedTiers.length > 0) {
      // Award rewards for completed tiers
      await awardAchievementRewards(userId, achievement.id, completedTiers);
      
      // Send notifications
      notifyAchievementCompletion(userId, achievement.id, completedTiers);
    }
  }
}
```

### 4. Social Features

#### 4.1 Friend System

**Description:**
- Friend requests and connections
- Online status and activity indicators
- Friend search and suggestions
- Friend list management and organization

**Technical Implementation:**
- Friend relationship states: PENDING, ACCEPTED, BLOCKED
- Database schema for friend relationships:

```sql
CREATE TABLE friend_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  friend_id UUID REFERENCES users(id),
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  initiated_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, friend_id)
);
```

- Implement the following API endpoints:

```
POST /api/friends/request
  - Sends a friend request
  - Request: { targetUserId }
  - Response: { success, requestId }

PUT /api/friends/request/:requestId
  - Accepts or rejects a friend request
  - Request: { action: 'accept' | 'reject' }
  - Response: { success, friendship }

GET /api/friends
  - Retrieves the user's friend list
  - Query: { status?: 'pending' | 'accepted' | 'all' }
  - Response: { friends: [...friendships] }

DELETE /api/friends/:friendId
  - Removes a friend
  - Response: { success }
```

- Implement real-time friend status updates using Socket.IO:

```javascript
// Socket.IO events for friend system
io.on('connection', (socket) => {
  // Update online status
  updateUserStatus(socket.userId, 'online');
  
  // Join personal room for direct messages
  socket.join(`user:${socket.userId}`);
  
  // Broadcast status to friends
  broadcastStatusToFriends(socket.userId, 'online');
  
  // Handle disconnect
  socket.on('disconnect', () => {
    updateUserStatus(socket.userId, 'offline');
    broadcastStatusToFriends(socket.userId, 'offline');
  });
});
```

#### 4.2 In-Game Chat

**Description:**
- Real-time chat during matches
- Emoji and quick message support
- Moderation tools (mute, report)
- Chat history for post-game review

**Technical Implementation:**
- Chat message types: TEXT, EMOJI, SYSTEM
- Database schema for chat:

```sql
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id),
  user_id UUID REFERENCES users(id),
  message_type VARCHAR(20) NOT NULL DEFAULT 'TEXT',
  content TEXT NOT NULL,
  is_moderated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE chat_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID REFERENCES chat_messages(id),
  reporter_id UUID REFERENCES users(id),
  reason VARCHAR(50) NOT NULL,
  details TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement content filtering middleware:

```javascript
// Chat content filtering
function filterChatMessage(content) {
  // Check against profanity list
  const containsProfanity = checkProfanityList(content);
  
  if (containsProfanity) {
    return {
      filtered: true,
      content: censorMessage(content),
      flagged: true
    };
  }
  
  return {
    filtered: false,
    content,
    flagged: false
  };
}
```

- Implement Socket.IO events for real-time chat:

```javascript
// Socket.IO events for chat
io.on('connection', (socket) => {
  // Join game chat
  socket.on('join_chat', (gameId) => {
    socket.join(`chat:${gameId}`);
  });
  
  // Send message
  socket.on('send_message', async (data) => {
    // Filter content
    const filteredResult = filterChatMessage(data.content);
    
    // Store message
    const message = await storeChatMessage(
      data.gameId,
      socket.userId,
      data.messageType,
      filteredResult.content,
      filteredResult.flagged
    );
    
    // Broadcast to game chat
    io.to(`chat:${data.gameId}`).emit('new_message', {
      id: message.id,
      gameId: data.gameId,
      userId: socket.userId,
      username: await getUsernameById(socket.userId),
      messageType: data.messageType,
      content: filteredResult.content,
      timestamp: message.created_at
    });
    
    // Flag for moderation if needed
    if (filteredResult.flagged) {
      flagMessageForModeration(message.id);
    }
  });
  
  // Report message
  socket.on('report_message', async (data) => {
    await createChatReport(data.messageId, socket.userId, data.reason, data.details);
    socket.emit('report_received', { success: true });
  });
});
```

#### 4.3 Leaderboards

**Description:**
- Global leaderboards for Elo ratings and tournament victories
- Friend-based leaderboards for direct comparison
- Seasonal rankings with rewards for top performers
- Filtering options by time period and metrics

**Technical Implementation:**
- Leaderboard types: GLOBAL_ELO, GLOBAL_WINS, FRIENDS_ELO, SEASONAL
- Database schema for leaderboards:

```sql
CREATE TABLE user_stats (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  elo_rating INTEGER NOT NULL DEFAULT 1200,
  games_played INTEGER NOT NULL DEFAULT 0,
  games_won INTEGER NOT NULL DEFAULT 0,
  games_lost INTEGER NOT NULL DEFAULT 0,
  games_drawn INTEGER NOT NULL DEFAULT 0,
  tournaments_joined INTEGER NOT NULL DEFAULT 0,
  tournaments_won INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE seasonal_stats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  season_id VARCHAR(20) NOT NULL,
  elo_rating INTEGER NOT NULL DEFAULT 1200,
  games_played INTEGER NOT NULL DEFAULT 0,
  games_won INTEGER NOT NULL DEFAULT 0,
  highest_rank INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, season_id)
);
```

- Implement the following API endpoints:

```
GET /api/leaderboards/global
  - Retrieves global leaderboard
  - Query: { type: 'elo' | 'wins', limit?: number, offset?: number }
  - Response: { rankings: [...users], userRank }

GET /api/leaderboards/friends
  - Retrieves friend leaderboard
  - Query: { type: 'elo' | 'wins' }
  - Response: { rankings: [...friends], userRank }

GET /api/leaderboards/seasonal/:seasonId
  - Retrieves seasonal leaderboard
  - Query: { limit?: number, offset?: number }
  - Response: { rankings: [...users], userRank, seasonInfo }
```

- Implement scheduled jobs for leaderboard updates:

```javascript
// Update global leaderboards (every 15 minutes)
cron.schedule('*/15 * * * *', async () => {
  await updateGlobalLeaderboards();
});

// Update seasonal leaderboards (every hour)
cron.schedule('0 * * * *', async () => {
  await updateSeasonalLeaderboards();
});
```

- Create caching mechanism for leaderboard data to improve performance

### 5. In-App Economy

#### 5.1 EVC (Everchess Coin) System

**Description:**
- In-app currency (EVC) for purchases and wagers
- Purchase options through app stores and web payment gateways
- Balance management and transaction history
- Conversion rates and package options

**Technical Implementation:**
- EVC package options:
  - 100 EVC = $1.00
  - 550 EVC = $5.00 (10% bonus)
  - 1,200 EVC = $10.00 (20% bonus)
  - 3,000 EVC = $25.00 (20% bonus)
  - 6,500 EVC = $50.00 (30% bonus)

- Database schema for EVC:

```sql
CREATE TABLE user_balance (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  evc_balance INTEGER NOT NULL DEFAULT 0,
  total_purchased INTEGER NOT NULL DEFAULT 0,
  total_spent INTEGER NOT NULL DEFAULT 0,
  total_earned INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE evc_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  amount INTEGER NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  reference_id UUID,
  reference_type VARCHAR(50),
  balance_after INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE purchase_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  package_id VARCHAR(50) NOT NULL,
  evc_amount INTEGER NOT NULL,
  price_usd NUMERIC(10, 2) NOT NULL,
  payment_provider VARCHAR(50) NOT NULL,
  payment_id VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
GET /api/economy/balance
  - Retrieves the user's EVC balance
  - Response: { balance, transactions }

POST /api/economy/purchase
  - Initiates an EVC purchase
  - Request: { packageId }
  - Response: { transactionId, redirectUrl? }

GET /api/economy/transactions
  - Retrieves transaction history
  - Query: { type?: string, limit?: number, offset?: number }
  - Response: { transactions, pagination }
```

- Implement payment processing service:

```javascript
// Process EVC purchase
async function processEvcPurchase(userId, packageId, paymentMethod) {
  // Get package details
  const package = getPackageById(packageId);
  
  // Create pending transaction
  const transaction = await createPurchaseTransaction(
    userId,
    packageId,
    package.evcAmount,
    package.priceUsd,
    paymentMethod
  );
  
  // Initialize payment based on method
  if (paymentMethod === 'app_store') {
    return initializeAppStorePurchase(transaction);
  } else if (paymentMethod === 'google_play') {
    return initializeGooglePlayPurchase(transaction);
  } else if (paymentMethod === 'stripe') {
    return initializeStripePurchase(transaction);
  }
  
  throw new Error('Unsupported payment method');
}

// Complete EVC purchase
async function completePurchase(transactionId, paymentId, status) {
  // Verify payment status
  if (status !== 'completed') {
    await updateTransactionStatus(transactionId, 'FAILED');
    return { success: false };
  }
  
  // Get transaction details
  const transaction = await getTransactionById(transactionId);
  
  // Update transaction status
  await updateTransactionStatus(transactionId, 'COMPLETED', paymentId);
  
  // Add EVC to user balance
  await addEvcToBalance(
    transaction.userId,
    transaction.evcAmount,
    'purchase',
    transactionId
  );
  
  return {
    success: true,
    evcAmount: transaction.evcAmount,
    newBalance: await getUserBalance(transaction.userId)
  };
}
```

#### 5.2 Store and Purchases

**Description:**
- In-app store for battlepasses, skins, tournament entries
- Purchase flow with confirmation and receipt
- Item inventory management
- Limited-time offers and promotions

**Technical Implementation:**
- Store item categories: BATTLEPASS, SKIN, TOURNAMENT_ENTRY, SPECIAL_OFFER
- Database schema for store and inventory:

```sql
CREATE TABLE store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_type VARCHAR(50) NOT NULL,
  reference_id UUID NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price_evc INTEGER NOT NULL,
  is_featured BOOLEAN DEFAULT FALSE,
  is_limited BOOLEAN DEFAULT FALSE,
  available_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  item_type VARCHAR(50) NOT NULL,
  item_id UUID NOT NULL,
  acquisition_type VARCHAR(50) NOT NULL,
  acquisition_reference UUID,
  is_equipped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type, item_id)
);

CREATE TABLE skin_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  rarity VARCHAR(20) NOT NULL,
  preview_url VARCHAR(255) NOT NULL,
  model_url VARCHAR(255) NOT NULL,
  applies_to VARCHAR(20) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
GET /api/store
  - Retrieves available store items
  - Query: { category?: string, featured?: boolean }
  - Response: { items, featured, limited }

POST /api/store/purchase
  - Purchases an item from the store
  - Request: { itemId }
  - Response: { success, item, newBalance, inventory }

GET /api/inventory
  - Retrieves the user's inventory
  - Query: { type?: string }
  - Response: { inventory, equipped }

POST /api/inventory/equip
  - Equips an item from inventory
  - Request: { inventoryId }
  - Response: { success, equipped }
```

- Implement purchase validation and processing:

```javascript
// Process store purchase
async function processStorePurchase(userId, itemId) {
  // Get item details
  const item = await getStoreItemById(itemId);
  
  // Check if item is available
  if (item.is_limited && new Date() > new Date(item.available_until)) {
    throw new Error('Item is no longer available');
  }
  
  // Check if user already owns the item
  const alreadyOwned = await checkUserOwnsItem(userId, item.item_type, item.reference_id);
  if (alreadyOwned) {
    throw new Error('Item already owned');
  }
  
  // Check if user has sufficient balance
  const userBalance = await getUserBalance(userId);
  if (userBalance < item.price_evc) {
    throw new Error('Insufficient balance');
  }
  
  // Deduct EVC from balance
  const transaction = await deductEvcFromBalance(
    userId,
    item.price_evc,
    'store_purchase',
    itemId
  );
  
  // Add item to inventory
  const inventoryItem = await addItemToInventory(
    userId,
    item.item_type,
    item.reference_id,
    'purchase',
    transaction.id
  );
  
  // Process specific item type actions
  if (item.item_type === 'BATTLEPASS') {
    await activateBattlepass(userId, item.reference_id);
  } else if (item.item_type === 'TOURNAMENT_ENTRY') {
    await registerForTournament(userId, item.reference_id);
  }
  
  return {
    success: true,
    item,
    inventoryItem,
    newBalance: await getUserBalance(userId)
  };
}
```

### 6. Web3 Integration

#### 6.1 NFT Chess Sets

**Description:**
- 10,000 unique Founders Chess Set NFTs
- Minting process and ownership verification
- In-game usage and visual display
- Metadata and rarity attributes

**Technical Implementation:**
- NFT metadata structure:

```json
{
  "name": "Founders Chess Set #123",
  "description": "Exclusive Everchess Founders Chess Set with unique attributes",
  "image": "https://assets.everchess.io/nfts/founders/123.png",
  "attributes": [
    { "trait_type": "Base", "value": "Marble" },
    { "trait_type": "King", "value": "Dragon" },
    { "trait_type": "Queen", "value": "Phoenix" },
    { "trait_type": "Rook", "value": "Castle" },
    { "trait_type": "Bishop", "value": "Wizard" },
    { "trait_type": "Knight", "value": "Pegasus" },
    { "trait_type": "Pawn", "value": "Soldier" },
    { "trait_type": "Rarity", "value": "Legendary" },
    { "trait_type": "Edition", "value": "Founders" }
  ],
  "properties": {
    "files": [
      {
        "uri": "https://assets.everchess.io/nfts/founders/123_model.glb",
        "type": "model/glb"
      }
    ],
    "category": "chess_set"
  }
}
```

- Implement Metaplex integration for NFT minting:

```javascript
// Mint Founders Chess Set NFT
async function mintFoundersNft(walletAddress, metadataUri) {
  try {
    // Connect to Solana
    const connection = new Connection(SOLANA_RPC_URL);
    
    // Initialize Metaplex
    const metaplex = new Metaplex(connection);
    metaplex.use(walletAdapterIdentity(wallet));
    
    // Create NFT
    const { nft } = await metaplex.nfts().create({
      uri: metadataUri,
      name: `Founders Chess Set #${getNextNftId()}`,
      sellerFeeBasisPoints: 500, // 5% royalty
      maxSupply: 1
    });
    
    // Record NFT in database
    await recordNftMinting(
      walletAddress,
      nft.address.toString(),
      nft.mint.address.toString(),
      metadataUri
    );
    
    return {
      success: true,
      nft: {
        address: nft.address.toString(),
        mint: nft.mint.address.toString(),
        metadata: metadataUri
      }
    };
  } catch (error) {
    console.error('NFT minting error:', error);
    throw new Error('Failed to mint NFT');
  }
}
```

- Database schema for NFT tracking:

```sql
CREATE TABLE nft_chess_sets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nft_id INTEGER NOT NULL,
  mint_address VARCHAR(255) UNIQUE NOT NULL,
  metadata_uri VARCHAR(255) NOT NULL,
  owner_wallet VARCHAR(255),
  owner_user_id UUID REFERENCES users(id),
  is_staked BOOLEAN DEFAULT FALSE,
  staking_start_time TIMESTAMP WITH TIME ZONE,
  attributes JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
GET /api/nft/founders
  - Retrieves available Founders Chess Sets
  - Response: { total, minted, available }

POST /api/nft/mint
  - Mints a new Founders Chess Set
  - Request: { walletAddress }
  - Response: { success, nft }

GET /api/nft/owned
  - Retrieves NFTs owned by the user
  - Response: { nfts }

POST /api/nft/equip
  - Equips an NFT chess set for gameplay
  - Request: { nftId }
  - Response: { success }
```

#### 6.2 Staking Mechanics

**Description:**
- NFT staking for XP boosts and EVC rewards
- Staking period options and reward rates
- Staking dashboard and status tracking
- Unstaking process and reward claims

**Technical Implementation:**
- Staking reward structure:
  - XP Boost: +10% XP from all sources while staked
  - Daily EVC: 5 EVC per day per staked NFT
  - Bonus rewards for longer staking periods

- Implement staking smart contract integration:

```javascript
// Stake NFT
async function stakeNft(userId, nftId, stakingPeriod) {
  try {
    // Verify NFT ownership
    const nft = await getNftById(nftId);
    if (nft.owner_user_id !== userId) {
      throw new Error('Not the owner of this NFT');
    }
    
    // Connect to Solana
    const connection = new Connection(SOLANA_RPC_URL);
    
    // Initialize wallet
    const wallet = await getUserWallet(userId);
    
    // Call staking contract
    const stakingResult = await callStakingContract(
      connection,
      wallet,
      nft.mint_address,
      stakingPeriod
    );
    
    // Update database
    await updateNftStakingStatus(
      nftId,
      true,
      new Date(),
      stakingPeriod,
      stakingResult.transactionId
    );
    
    // Activate XP boost for user
    await activateXpBoost(userId, nftId);
    
    return {
      success: true,
      stakingInfo: {
        nftId,
        startTime: new Date(),
        period: stakingPeriod,
        estimatedRewards: calculateEstimatedRewards(stakingPeriod)
      }
    };
  } catch (error) {
    console.error('Staking error:', error);
    throw new Error('Failed to stake NFT');
  }
}

// Unstake NFT
async function unstakeNft(userId, nftId) {
  try {
    // Verify NFT is staked by user
    const nft = await getNftById(nftId);
    if (nft.owner_user_id !== userId || !nft.is_staked) {
      throw new Error('NFT is not staked by this user');
    }
    
    // Connect to Solana
    const connection = new Connection(SOLANA_RPC_URL);
    
    // Initialize wallet
    const wallet = await getUserWallet(userId);
    
    // Call unstaking contract
    const unstakingResult = await callUnstakingContract(
      connection,
      wallet,
      nft.mint_address
    );
    
    // Calculate rewards
    const stakingDuration = calculateStakingDuration(nft.staking_start_time);
    const rewards = calculateStakingRewards(stakingDuration);
    
    // Award EVC rewards
    await addEvcToBalance(
      userId,
      rewards,
      'staking_reward',
      nftId
    );
    
    // Update database
    await updateNftStakingStatus(
      nftId,
      false,
      null,
      null,
      unstakingResult.transactionId
    );
    
    // Deactivate XP boost
    await deactivateXpBoost(userId, nftId);
    
    return {
      success: true,
      rewards,
      newBalance: await getUserBalance(userId)
    };
  } catch (error) {
    console.error('Unstaking error:', error);
    throw new Error('Failed to unstake NFT');
  }
}
```

- Database schema for staking:

```sql
CREATE TABLE staking_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  nft_id UUID REFERENCES nft_chess_sets(id),
  transaction_type VARCHAR(20) NOT NULL,
  transaction_hash VARCHAR(255) NOT NULL,
  staking_period INTEGER,
  rewards_amount INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE xp_boosts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  source_type VARCHAR(50) NOT NULL,
  source_id UUID NOT NULL,
  boost_percentage INTEGER NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
POST /api/staking/stake
  - Stakes an NFT
  - Request: { nftId, period }
  - Response: { success, stakingInfo }

POST /api/staking/unstake
  - Unstakes an NFT and claims rewards
  - Request: { nftId }
  - Response: { success, rewards, newBalance }

GET /api/staking/status
  - Retrieves staking status and rewards
  - Response: { stakedNfts, totalRewards, xpBoost }
```

#### 6.3 Wallet Integration

**Description:**
- Solana wallet connections (Phantom, Solflare)
- Wallet management and transaction signing
- Balance checking and transaction history
- Secure connection and disconnection flow

**Technical Implementation:**
- Implement wallet adapter integration:

```javascript
// Initialize wallet adapters
const walletAdapters = [
  new PhantomWalletAdapter(),
  new SolflareWalletAdapter(),
  new TorusWalletAdapter()
];

// Connect wallet
async function connectWallet(userId, walletType) {
  try {
    // Get adapter for wallet type
    const adapter = getWalletAdapter(walletType);
    
    // Connect to wallet
    await adapter.connect();
    
    // Verify ownership with signature
    const message = `Connect Everchess account: ${userId} at ${new Date().toISOString()}`;
    const signature = await adapter.signMessage(
      new TextEncoder().encode(message)
    );
    
    // Verify signature
    const isValid = await verifySignature(
      adapter.publicKey.toString(),
      message,
      signature
    );
    
    if (!isValid) {
      throw new Error('Invalid signature');
    }
    
    // Link wallet to user account
    await linkWalletToUser(
      userId,
      adapter.publicKey.toString(),
      walletType
    );
    
    return {
      success: true,
      wallet: {
        address: adapter.publicKey.toString(),
        type: walletType
      }
    };
  } catch (error) {
    console.error('Wallet connection error:', error);
    throw new Error('Failed to connect wallet');
  }
}
```

- Implement wallet transaction signing:

```javascript
// Sign transaction
async function signTransaction(userId, transaction, description) {
  try {
    // Get user's wallet
    const userWallet = await getUserWallet(userId);
    
    // Get adapter for wallet type
    const adapter = getWalletAdapter(userWallet.type);
    
    // Connect to wallet if not connected
    if (!adapter.connected) {
      await adapter.connect();
    }
    
    // Sign transaction
    const signedTransaction = await adapter.signTransaction(transaction);
    
    // Log transaction for security
    await logWalletTransaction(
      userId,
      userWallet.address,
      description,
      transaction.signature
    );
    
    return {
      success: true,
      signedTransaction
    };
  } catch (error) {
    console.error('Transaction signing error:', error);
    throw new Error('Failed to sign transaction');
  }
}
```

- Database schema for wallet connections:

```sql
CREATE TABLE wallet_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  wallet_address VARCHAR(255) NOT NULL,
  wallet_type VARCHAR(50) NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  last_connected TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(wallet_address)
);

CREATE TABLE wallet_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  wallet_address VARCHAR(255) NOT NULL,
  transaction_hash VARCHAR(255),
  description TEXT NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 7. Anti-Cheating System

#### 7.1 Move Validation

**Description:**
- Server-side move validation for all games
- Pattern detection for suspicious play
- Comparison with chess engine recommendations
- Logging and flagging of suspicious activities

**Technical Implementation:**
- Implement move validation service:

```javascript
// Validate move and check for suspicious patterns
async function validateAndCheckMove(gameId, userId, from, to, promotion) {
  // Basic move validation
  const validationResult = validateMove(gameId, userId, from, to, promotion);
  
  if (!validationResult.valid) {
    return validationResult;
  }
  
  // Get game history
  const gameHistory = await getGameHistory(gameId);
  
  // Check for suspicious patterns
  const suspiciousResult = checkSuspiciousPatterns(
    gameHistory,
    validationResult.move
  );
  
  // Log move for analysis
  await logMoveForAnalysis(
    gameId,
    userId,
    from,
    to,
    promotion,
    suspiciousResult.suspiciousScore
  );
  
  // Flag game if highly suspicious
  if (suspiciousResult.suspiciousScore > 0.9) {
    await flagGameForReview(
      gameId,
      'high_engine_correlation',
      suspiciousResult
    );
  }
  
  return {
    ...validationResult,
    suspicious: suspiciousResult.suspiciousScore > 0.7
  };
}

// Check for suspicious patterns
function checkSuspiciousPatterns(gameHistory, currentMove) {
  // Initialize Stockfish for comparison
  const stockfish = initializeStockfish();
  
  // Set position from game history
  stockfish.setPosition(gameHistory);
  
  // Get top engine moves
  const engineMoves = stockfish.getTopMoves(3);
  
  // Check if current move matches top engine move
  const matchesTopMove = engineMoves[0].move === formatMove(currentMove);
  
  // Calculate correlation with engine
  const moveStrength = calculateMoveStrength(currentMove, engineMoves);
  
  // Check for consistent high-correlation pattern
  const consistentPattern = checkConsistentPattern(gameHistory, moveStrength);
  
  // Calculate suspicious score (0-1)
  const suspiciousScore = calculateSuspiciousScore(
    matchesTopMove,
    moveStrength,
    consistentPattern
  );
  
  return {
    suspiciousScore,
    matchesTopMove,
    moveStrength,
    consistentPattern
  };
}
```

- Database schema for anti-cheating:

```sql
CREATE TABLE move_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id),
  user_id UUID REFERENCES users(id),
  move_number INTEGER NOT NULL,
  from_square VARCHAR(2) NOT NULL,
  to_square VARCHAR(2) NOT NULL,
  promotion VARCHAR(1),
  engine_correlation FLOAT,
  suspicious_score FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE flagged_games (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id),
  user_id UUID REFERENCES users(id),
  flag_reason VARCHAR(50) NOT NULL,
  evidence JSONB NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  reviewer_id UUID REFERENCES users(id),
  review_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 7.2 Manual Review Process

**Description:**
- Admin dashboard for reviewing flagged games
- Tools for analyzing suspicious patterns
- Actions for warning, suspending, or banning users
- Appeal process for disputed decisions

**Technical Implementation:**
- Implement admin review interface and API:

```javascript
// Review flagged game
async function reviewFlaggedGame(reviewerId, flaggedGameId, decision, notes) {
  // Verify reviewer is admin
  const isAdmin = await checkUserIsAdmin(reviewerId);
  if (!isAdmin) {
    throw new Error('Unauthorized');
  }
  
  // Get flagged game details
  const flaggedGame = await getFlaggedGameById(flaggedGameId);
  
  // Update flagged game status
  await updateFlaggedGameStatus(
    flaggedGameId,
    decision,
    reviewerId,
    notes
  );
  
  // Take action based on decision
  if (decision === 'confirmed') {
    // Apply penalty to user
    await applyCheatPenalty(
      flaggedGame.user_id,
      flaggedGame.flag_reason,
      flaggedGame.game_id
    );
    
    // Notify user
    await sendCheatNotification(
      flaggedGame.user_id,
      flaggedGame.game_id,
      notes
    );
  } else if (decision === 'dismissed') {
    // Clear flag from user record
    await clearCheatFlag(
      flaggedGame.user_id,
      flaggedGame.game_id
    );
  }
  
  return {
    success: true,
    decision,
    gameId: flaggedGame.game_id,
    userId: flaggedGame.user_id
  };
}

// Apply cheat penalty
async function applyCheatPenalty(userId, reason, gameId) {
  // Get user's violation history
  const violations = await getUserViolations(userId);
  
  // Determine penalty based on violation count
  let penalty;
  if (violations.length === 0) {
    penalty = 'warning';
  } else if (violations.length === 1) {
    penalty = 'temporary_suspension';
  } else {
    penalty = 'permanent_ban';
  }
  
  // Record violation
  await recordViolation(
    userId,
    reason,
    gameId,
    penalty
  );
  
  // Apply penalty
  if (penalty === 'warning') {
    await addWarningToUser(userId, reason);
  } else if (penalty === 'temporary_suspension') {
    await suspendUser(userId, 7); // 7-day suspension
  } else if (penalty === 'permanent_ban') {
    await banUser(userId);
  }
  
  return {
    success: true,
    penalty,
    userId
  };
}
```

- Database schema for user violations:

```sql
CREATE TABLE user_violations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  violation_type VARCHAR(50) NOT NULL,
  reference_id UUID,
  penalty VARCHAR(50) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

- Implement the following API endpoints:

```
GET /api/admin/flagged-games
  - Retrieves flagged games for review
  - Query: { status?: string, limit?: number, offset?: number }
  - Response: { flaggedGames, pagination }

POST /api/admin/review
  - Reviews a flagged game
  - Request: { flaggedGameId, decision, notes }
  - Response: { success, decision }

GET /api/admin/user-violations
  - Retrieves user violations
  - Query: { userId?: string, active?: boolean }
  - Response: { violations }
```

## Technical Architecture

This section outlines the technical architecture of the Everchess platform, detailing the technology stack, system components, and how they interact to create a cohesive, scalable, and performant application.

### System Architecture Overview

```
┌──────────────────────────────────────────────────────────────────────┐
│                         CLIENT APPLICATIONS                           │
│  ┌───────────────────────┐       ┌───────────────────────────────┐   │
│  │  Mobile Applications  │       │  Progressive Web App (PWA)    │   │
│  │  (iOS & Android)      │       │  (Browser)                    │   │
│  └───────────┬───────────┘       └────────────┬──────────────────┘   │
│              │                                │                      │
│              │     React Native + Expo SDK    │                      │
│              └──────────────┬─────────────────┘                      │
│                             │                                        │
│  ┌───────────────────────────────────────────────────────────────┐   │
│  │                    CROSS-PLATFORM UI LAYER                     │   │
│  │  ┌────────────┐   ┌─────────────┐    ┌───────────────────┐    │   │
│  │  │ Three.js/  │   │   Redux     │    │  React            │    │   │
│  │  │ Three Fiber│   │   Store     │    │  Navigation       │    │   │
│  │  └─────┬──────┘   └──────┬──────┘    └─────────┬─────────┘    │   │
│  │        │                 │                     │               │   │
│  │        │                 │                     │               │   │
│  │  ┌─────┴──────┐   ┌──────┴──────┐    ┌─────────┴─────────┐    │   │
│  │  │ 3D Chess   │   │ Application │    │  User Interface   │    │   │
│  │  │ Rendering  │   │ State      │    │  Components        │    │   │
│  │  └────────────┘   └─────────────┘    └───────────────────┘    │   │
│  └───────────────────────────┬───────────────────────────────────┘   │
│                              │                                        │
│  ┌───────────────────────────┴───────────────────────────────────┐   │
│  │                      SERVICE LAYER                             │   │
│  │  ┌────────────┐   ┌─────────────┐    ┌───────────────────┐    │   │
│  │  │ Socket.IO  │   │ Supabase    │    │ Para Wallet SDK   │    │   │
│  │  │ Client     │   │ Client      │    │ (Auth & Web3)     │    │   │
│  │  └────────────┘   └─────────────┘    └───────────────────┘    │   │
│  └───────────────────────────────────────────────────────────────┘   │
└──────────────────────────────┬───────────────────────────────────────┘
                               │
                               │ HTTPS/WSS
                               │
┌──────────────────────────────┴───────────────────────────────────────┐
│                         SERVER INFRASTRUCTURE                         │
│                                                                       │
│  ┌────────────────────────────────────────────────────┐               │
│  │                     AWS EC2                        │               │
│  │  ┌─────────────┐  ┌─────────────┐  ┌───────────┐  │               │
│  │  │ Node.js +   │  │  Socket.IO  │  │ Express   │  │               │
│  │  │ Express API │  │  Server     │  │ Middleware│  │               │
│  │  └─────┬───────┘  └──────┬──────┘  └─────┬─────┘  │               │
│  │        │                 │               │        │               │
│  │        └─────────────────┼───────────────┘        │               │
│  │                          │                        │               │
│  └─────────────────────────┬┴────────────────────────┘               │
│                            │                                         │
│  ┌─────────────────────────┴─────────────────────────┐               │
│  │                  DATA STORAGE                     │               │
│  │  ┌─────────────────┐      ┌─────────────────┐    │               │
│  │  │   Supabase      │      │    AWS S3       │    │               │
│  │  │  (PostgreSQL)   │      │  + CloudFront   │    │               │
│  │  │                 │      │                 │    │               │
│  │  │ - User profiles │      │ - NFT 3D models│    │               │
│  │  │ - Game states   │      │ - Static assets│    │               │
│  │  │ - Leaderboards  │      │ - User uploads │    │               │
│  │  │ - Inventories   │      │ - Media files  │    │               │
│  │  └─────────────────┘      └─────────────────┘    │               │
│  └───────────────────────────────────────────────────┘               │
│                                                                       │
│  ┌───────────────────────────────────────────────────┐               │
│  │                 BLOCKCHAIN INTEGRATION            │               │
│  │  ┌─────────────────┐      ┌─────────────────┐    │               │
│  │  │  Solana Node    │      │  Magic Eden     │    │               │
│  │  │  Connection     │      │  Marketplace    │    │               │
│  │  │                 │      │  Integration    │    │               │
│  │  └─────────────────┘      └─────────────────┘    │               │
│  └───────────────────────────────────────────────────┘               │
│                                                                       │
│  ┌───────────────────────────────────────────────────┐               │
│  │                 MONITORING & ANALYTICS            │               │
│  │  ┌─────────────────┐      ┌─────────────────┐    │               │
│  │  │     Sentry      │      │  AWS CloudWatch │    │               │
│  │  │  Error Tracking │      │   Monitoring    │    │               │
│  │  └─────────────────┘      └─────────────────┘    │               │
│  └───────────────────────────────────────────────────┘               │
└───────────────────────────────────────────────────────────────────────┘
```

**Key Data Flows:**

1. **Game Data Flow**:
   - Client renders 3D chess via Three Fiber
   - Moves validated by client-side Chess.js
   - Validated moves sent to server via Socket.IO
   - Server validates and broadcasts to opponent
   - Game state persisted in Supabase

2. **NFT Chess Set Flow**:
   - 3D models stored in AWS S3 and distributed via CloudFront
   - Models organized as "player" and "opponent" pieces rather than colors
   - Metadata stored on Solana blockchain via Metaplex standard
   - Client loads NFT ownership data via Para Wallet SDK
   - NFTs rendered in-game using Three Fiber and GLTFLoader

3. **Authentication Flow**:
   - Web2 users authenticate via Para Wallet SDK (email, social)
   - Web3 users connect wallet via Para Wallet SDK
   - Unified user identity managed by Para Wallet SDK
   - JWT tokens secure API requests

This architecture supports cross-platform deployment (iOS, Android, Web) from a unified codebase while delivering high-performance 3D rendering and real-time multiplayer capabilities.

### 1. Technology Stack and Implementation Priorities

#### 1.1 Cross-Platform Frontend (React Native + Expo)

**Core Technologies:**
- **React Native**: Core framework for cross-platform development
  - Documentation: [React Native](https://reactnative.dev/docs/getting-started)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: Maintain 60 FPS on target devices (iPhone XS+, Android 8.0+)
  - Technical Rationale: Allows single codebase to target web and mobile platforms with native performance
- **Expo**: Development toolchain for simplified deployment and testing across web and mobile
  - Documentation: [Expo](https://docs.expo.dev/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Technical Rationale: Significantly reduces DevOps overhead for solo development, enables PWA capabilities, and simplifies app store submissions
  - Configuration Strategy: Use EAS Build for bare workflow capabilities while maintaining managed workflow benefits
- **React Three Fiber**: React renderer for Three.js, enabling 3D chess visualization
  - Documentation: [React Three Fiber](https://r3f.docs.pmnd.rs/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Chess pieces must load in < 1.5s, maintain 50+ FPS during animations
  - Technical Rationale: Provides declarative approach to 3D rendering integrated with React component lifecycle
- **Redux Toolkit**: State management for complex application state
  - Documentation: [Redux Toolkit](https://redux-toolkit.js.org/introduction/getting-started)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: State updates must complete in < 50ms, store size < 5MB
  - Technical Rationale: Provides predictable state management with TypeScript integration and simplified immutability handling
  - Implementation Notes: Use slice pattern with entity normalization for optimized state access
- **Socket.IO Client**: Real-time communication with the backend
  - Documentation: [Socket.IO Client](https://socket.io/docs/v4/client-api/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Message round-trip < 250ms, reconnection within 3s
  - Technical Rationale: Provides reliable WebSocket communication with fallback options and robust room-based multiplayer
  - Security Consideration: Implement JWT token validation for socket connections
- **React Navigation**: Navigation management across screens and app sections
  - Documentation: [React Navigation](https://reactnavigation.org/docs/getting-started)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: Screen transitions < 300ms, memory usage < 30MB
  - Technical Rationale: Provides native-feeling navigation on both mobile and web platforms, with deep linking support
- **Three.js**: 3D library for rendering chess boards and pieces
  - Documentation: [Three.js](https://threejs.org/docs/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Initial scene load < 2s, animation framerate > 45 FPS on target devices
  - Technical Rationale: Industry standard for WebGL rendering with extensive community support and examples
  - Implementation Notes: Use gLTF format for 3D models to optimize loading times; implement level-of-detail settings
- **SWR**: React Hooks for data fetching and caching
  - Documentation: [SWR](https://swr.vercel.app/docs/getting-started)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Data fetching must complete in < 500ms, cache invalidation within 1s
  - Technical Rationale: Provides simple and efficient data fetching with built-in caching and re-fetching

**Platform-Specific Optimizations:**
- **Web Optimizations**:
  - **Next.js**: React framework for server-side rendering and static site generation
    - Documentation: [Next.js](https://nextjs.org/docs)
  - **Tailwind CSS**: Utility-first CSS framework for responsive design
    - Documentation: [Tailwind CSS](https://tailwindcss.com/docs)
  - **Framer Motion**: Animation library for smooth transitions
    - Documentation: [Framer Motion](https://www.framer.com/motion/)
- **Mobile Optimizations**:
  - **React Native Reanimated**: Advanced animations for fluid mobile interactions
    - Documentation: [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)
  - **Async Storage**: Local storage for user preferences and caching
    - Documentation: [Async Storage](https://react-native-async-storage.github.io/async-storage/docs/usage)

#### 1.2 Backend Technologies

**API Server:**
- **Node.js**: JavaScript runtime for the server environment
  - Documentation: [Node.js](https://nodejs.org/en/docs/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: API response time < 200ms for 95% of requests, server startup < 5s
  - Technical Rationale: JavaScript consistency across frontend and backend; large ecosystem of packages
  - Implementation Notes: Use LTS version (18.x+); configure for production with clustering
- **Express**: Web framework for building RESTful APIs
  - Documentation: [Express](https://expressjs.com/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: Route resolution < 5ms, middleware chain execution < 50ms
  - Technical Rationale: Lightweight, flexible framework with extensive middleware support
  - Security Consideration: Implement rate limiting, CORS, Helmet for header security
- **Socket.IO**: Real-time bidirectional communication
  - Documentation: [Socket.IO](https://socket.io/docs/v4/server-api/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Handle 1000+ concurrent connections with < 500ms latency
  - Technical Rationale: Provides reliable real-time communication with fallback mechanisms
  - Implementation Notes: Organize by namespaces for game types; implement room-based gameplay
- **TypeScript**: Type-safe JavaScript for improved developer experience
  - Documentation: [TypeScript](https://www.typescriptlang.org/docs/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Technical Rationale: Reduces runtime errors; improves code maintainability and team onboarding
  - Implementation Notes: Use strict mode; maintain interface-first development approach
- **Passport.js**: Authentication middleware for Web2 authentication
  - Documentation: [Passport.js](https://www.passportjs.org/docs/)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Technical Rationale: Flexible authentication with support for multiple strategies
  - Security Consideration: Implement rate limiting on authentication routes
- **JWT**: JSON Web Tokens for secure authentication
  - Documentation: [JWT](https://jwt.io/introduction/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: Token validation < 10ms, token generation < 50ms
  - Technical Rationale: Stateless authentication mechanism that works well with microservices
  - Security Consideration: Use short expiration times with refresh token rotation
- **Zod**: Schema validation for API requests and responses
  - Documentation: [Zod](https://zod.dev/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Validation execution < 20ms for complex schemas
  - Technical Rationale: TypeScript integration with runtime validation; self-documenting schemas

**Database and Storage:**
- **Supabase (PostgreSQL)**: Primary database with real-time capabilities
  - Documentation: [Supabase](https://supabase.com/docs)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: Query execution < 100ms, subscription updates < 200ms
  - Technical Rationale: Combines PostgreSQL with real-time capabilities; includes auth and storage solutions
  - Implementation Notes: Implement row-level security (RLS) for all tables; utilize Postgres functions for complex operations
  - Database Design: Implement entity relationship model with proper indexing and normalization
- **AWS S3**: Storage for static assets, 3D models, and user uploads
  - Documentation: [AWS S3](https://docs.aws.amazon.com/s3/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Asset upload < 5s, download < 1s (with CDN)
  - Technical Rationale: Scalable, reliable storage with CDN integration capabilities
  - Implementation Notes: Organize assets in logical bucket structure; implement signed URLs for secure uploads
  - Cost Optimization: Implement lifecycle policies to move infrequently accessed files to lower-cost tiers
- **AWS CloudFront**: Content delivery network for global asset distribution
  - Documentation: [AWS CloudFront](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Global asset delivery < 200ms
  - Technical Rationale: Reduces latency for 3D assets and improves global performance
  - Implementation Notes: Configure origin access identity for S3 security; set appropriate cache TTLs
- **Redis**: In-memory data store for caching and session management
  - Documentation: [Redis](https://redis.io/documentation)
  - Implementation Priority: **P2** (Gamified Functionalities, Phase 3)
  - Performance Target: Cache access < 5ms, cache hit ratio > 85%
  - Technical Rationale: Improves performance for frequently accessed data, leaderboards, and rate limiting
  - Implementation Notes: Implement proper key expiration policies; use for leaderboard sorting and matchmaking
- **Amazon SES**: Email delivery service for transactional emails
  - Documentation: [Amazon SES](https://docs.aws.amazon.com/ses/)
  - Implementation Priority: **P2** (Gamified Functionalities, Phase 3)
  - Performance Target: Email delivery < 30s
  - Technical Rationale: Reliable email delivery with high deliverability rates
  - Implementation Notes: Create templating system for emails; implement tracking and analytics

#### 1.3 Web3 Integration

**Blockchain Connectivity:**
- **Solana Web3.js**: JavaScript API for interacting with the Solana blockchain
  - Documentation: [Solana Web3.js](https://docs.solana.com/developing/clients/javascript-api)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Performance Target: Transaction signing < 2s, account data retrieval < 500ms
  - Technical Rationale: Provides direct access to Solana blockchain with TypeScript support
  - Security Consideration: Implement client-side transaction review and approval flow
  - Implementation Notes: Use connection pooling; implement robust error handling for RPC failures
- **Para Wallet SDK**: Unified Web2/Web3 authentication and wallet management
  - Documentation: [Para Wallet SDK](https://docs.getpara.com)
  - Implementation Priority: **P1** (Foundation Setup, Phase 1)
  - Performance Target: Authentication < 3s, wallet connection < 5s
  - Technical Rationale: Provides unified authentication for email, social, and wallet sign-in methods
  - Security Consideration: Built-in secure token management and session handling
  - Implementation Notes: Supports progressive enhancement from Web2 to Web3; comprehensive error handling
- **Metaplex**: NFT standards and integration for Solana
  - Documentation: [Metaplex](https://docs.metaplex.com/)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Performance Target: NFT metadata retrieval < 1s, minting flow < 20s
  - Technical Rationale: Industry standard for NFT implementation on Solana with rich metadata support
  - Implementation Notes: Custom collection structure for chess sets with piece-specific metadata
  - Storage Strategy: Store 3D models off-chain in S3 with IPFS backup for metadata resilience
- **SPL Token Standard**: Token implementation on Solana
  - Documentation: [SPL Token](https://spl.solana.com/token)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Technical Rationale: Required for ownership tracking of NFT chess sets and in-game currency
  - Security Consideration: Implement transaction confirmation and signing safeguards
  - Implementation Notes: Use token accounts for user inventory management

**NFT Implementation Strategy:**

- **Ownership Model**: Players own complete themed chess sets as single NFTs rather than individual pieces
  - Technical Rationale: Reduces transaction cost and management complexity
  - Implementation Note: Use metadata "properties" field to store individual piece model URIs

- **Chess Set Structure**: Each NFT contains 12 pieces (6 piece types for each side)
  - Implementation Approach: Organize as "player" and "opponent" sides rather than traditional colors
  - Technical Advantage: Allows for non-traditional themed sets without color restrictions
  
- **Asset Delivery**: Multi-tiered approach to ensure reliable asset loading
  - Primary: AWS CloudFront CDN for fast global delivery
  - Backup: Direct S3 access with fallback loading system
  - Emergency: Default piece models when custom assets fail to load
  
- **Performance Optimization**: 
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Strategy: Progressive loading with LOD (Level of Detail) implementation
  - Technical Approach: Load low-poly versions first, then upgrade to high-detail models
  - Caching: Implement aggressive browser/local caching of frequently used assets

#### 1.4 Infrastructure and DevOps

**Hosting and Deployment:**
- **AWS EC2**: Primary server hosting
  - Documentation: [AWS EC2](https://docs.aws.amazon.com/ec2/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: 99.95% uptime, < 500ms API response time under load
  - Technical Rationale: Scalable, reliable hosting with extensive monitoring capabilities
  - Implementation Notes: Use t3.medium instances initially; implement auto-scaling for production
  - Security Consideration: Implement VPC with proper security groups; limit SSH access
- **AWS CloudFront**: Content delivery network
  - Documentation: [AWS CloudFront](https://docs.aws.amazon.com/cloudfront/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Performance Target: Global asset delivery < 200ms, cache hit ratio > 90%
  - Technical Rationale: Reduces latency for global users and offloads traffic from origin servers
  - Implementation Notes: Set appropriate cache headers; implement versioning for asset updates
- **Cloudflare**: Domain management and security
  - Documentation: [Cloudflare](https://developers.cloudflare.com/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Technical Rationale: Provides DDoS protection, CDN capabilities, and DNS management
  - Security Consideration: Implement rate limiting and WAF rules for API endpoints
- **GitHub Actions**: Continuous integration and deployment
  - Documentation: [GitHub Actions](https://docs.github.com/en/actions)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Performance Target: CI pipeline execution < 10 minutes, automated testing coverage > 80%
  - Technical Rationale: Automates testing and deployment processes to ensure quality
  - Implementation Notes: Implement staged deployments with rollback capabilities
- **Docker**: Containerization for consistent environments
  - Documentation: [Docker](https://docs.docker.com/)
  - Implementation Priority: **P1** (Core Gameplay, Phase 2)
  - Technical Rationale: Ensures consistency between development and production environments
  - Implementation Notes: Use multi-stage builds to minimize image size; implement container health checks

**Monitoring and Analytics:**
- **Sentry**: Error tracking and performance monitoring
  - Documentation: [Sentry](https://docs.sentry.io/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Technical Rationale: Provides real-time error tracking across all platforms with detailed context
  - Implementation Notes: Configure breadcrumbs for user actions; implement performance monitoring
  - Performance Target: Error reporting latency < 2s, performance impact < 1% CPU overhead
- **AWS CloudWatch**: Infrastructure monitoring
  - Documentation: [AWS CloudWatch](https://docs.aws.amazon.com/cloudwatch/)
  - Implementation Priority: **P0** (Foundation, Phase 1)
  - Technical Rationale: Native AWS integration for comprehensive infrastructure monitoring
  - Implementation Notes: Create custom dashboards for key metrics; configure alerts for critical thresholds
  - Security Consideration: Use IAM roles with least privilege for monitoring agents
- **Custom Analytics Pipeline**: User behavior and engagement tracking
  - Implementation Priority: **P2** (Gamified Functionalities, Phase 3)
  - Technical Rationale: Allows complete control over user data and privacy compliance
  - Implementation Notes: Store events in Supabase; implement batch processing for aggregation
  - Privacy Consideration: Implement data anonymization and compliance with GDPR/CCPA
  - Performance Target: Event tracking latency < 500ms, minimal impact on client performance

**Mobile App Store Optimizations:**
- **TestFlight**: Beta testing for iOS applications
  - Documentation: [TestFlight](https://developer.apple.com/testflight/)
  - Implementation Priority: **P4** (Final Deployment, Phase 5)
  - Technical Rationale: Enables controlled beta testing with real iOS users before App Store submission
  - Implementation Notes: Create distinct beta testing phases with specific feedback objectives
  - Timeline Consideration: Start beta testing 4-6 weeks before planned App Store submission
- **Google Play Console**: Developer console for Android applications
  - Documentation: [Google Play Console](https://developer.android.com/distribute/console)
  - Implementation Priority: **P4** (Final Deployment, Phase 5)
  - Technical Rationale: Required for Android app distribution with beta testing capabilities
  - Implementation Notes: Utilize staged rollouts for production releases; implement in-app review prompts
- **App Store Review Guidelines Compliance**:
  - Documentation: [App Store Guidelines](https://developer.apple.com/app-store/review/guidelines/)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Technical Rationale: Critical to ensure successful App Store submission without rejection delays
  - Implementation Notes: Position NFT features as optional; implement Apple IAP for all in-app purchases
  - Risk Mitigation: Structure Web3 features as optional enhancement rather than core functionality
- **Google Play Policies Compliance**:
  - Documentation: [Google Play Policies](https://play.google.com/about/developer-content-policy/)
  - Implementation Priority: **P3** (Key Integrations, Phase 4)
  - Technical Rationale: Ensures compliance with Google's requirements for apps with digital goods
  - Implementation Notes: Implement Google Play Billing for non-NFT digital goods; provide clear disclosure for NFT features
  - Security Consideration: Implement Google Play App Signing for enhanced security

### 2. System Architecture

#### 2.1 High-Level Architecture

The Everchess platform follows a microservices architecture with the following core components:

1. **Client Applications**:
   - Mobile app (iOS/Android)
   - Web app (desktop/mobile browsers)

2. **API Gateway**:
   - Request routing and load balancing
   - Authentication and authorization
   - Rate limiting and request validation

3. **Service Layer**:
   - Game Service: Chess game logic and state management
   - User Service: Authentication and profile management
   - Social Service: Friends, chat, and social features
   - Economy Service: EVC transactions and inventory
   - Blockchain Service: Web3 wallet integration and NFT management

4. **Data Layer**:
   - PostgreSQL: Primary relational database
   - Redis: Caching and pub/sub messaging
   - Object Storage: Assets and media

5. **External Integrations**:
   - Solana Blockchain
   - Payment Processors
   - Analytics Services

#### 2.2 Component Diagram

```
┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │
│  Mobile Client  │     │   Web Client    │
│  (React Native) │     │    (Next.js)    │
│                 │     │                 │
└────────┬────────┘     └────────┬────────┘
         │                       │
         │                       │
         │                       │
┌────────▼───────────────────────▼────────┐
│                                         │
│              API Gateway                │
│                                         │
└─┬─────────┬─────────┬─────────┬─────────┘
  │         │         │         │
  │         │         │         │
┌─▼───────┐ │ ┌───────▼─┐ ┌─────▼───┐ ┌─────▼─────┐
│         │ │ │         │ │         │ │           │
│  Game   │ │ │  User   │ │ Social  │ │ Economy/  │
│ Service │ │ │ Service │ │ Service │ │ Blockchain│
│         │ │ │         │ │         │ │ Service   │
└─┬───────┘ │ └───┬─────┘ └────┬────┘ └─────┬─────┘
  │         │     │            │            │
  │         │     │            │            │
┌─▼─────────▼─────▼────────────▼────────────▼─────┐
│                                                 │
│                  Data Layer                     │
│  (PostgreSQL, Redis, Object Storage, Solana)    │
│                                                 │
└─────────────────────────────────────────────────┘
```

#### 2.3 Data Flow

1. **Game Flow**:
   - Client initiates a game request through the API Gateway
   - Game Service creates a new game instance and stores it in PostgreSQL
   - Socket.IO establishes a real-time connection between clients
   - Game moves are validated by the Game Service and broadcast to both players
   - Game state is persisted in PostgreSQL and cached in Redis for performance
   - Game completion triggers updates to user stats and rewards

2. **Authentication Flow**:
   - User registers or logs in through the User Service
   - JWT token is generated and returned to the client
   - Token is included in subsequent API requests for authorization
   - Web3 authentication requires wallet signature verification
   - User sessions are tracked in Redis for quick validation

3. **Social Flow**:
   - Friend requests are processed by the Social Service
   - Real-time notifications are sent via Socket.IO
   - Chat messages are filtered, stored, and broadcast to recipients
   - Leaderboards are calculated periodically and cached in Redis

4. **Economy Flow**:
   - Store purchases deduct EVC from user balance
   - Inventory items are added to user inventory
   - Transactions are logged for audit purposes
   - NFT interactions are processed through the Blockchain Service
   - Solana transactions are signed and submitted to the blockchain

### 3. Database Schema

#### 3.1 Core Tables

**Users and Authentication:**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  auth_provider VARCHAR(20),
  auth_provider_id VARCHAR(255),
  email_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_profiles (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  display_name VARCHAR(50),
  avatar_url VARCHAR(255),
  bio TEXT,
  country_code VARCHAR(2),
  elo_rating INTEGER DEFAULT 1200,
  games_played INTEGER DEFAULT 0,
  games_won INTEGER DEFAULT 0,
  games_lost INTEGER DEFAULT 0,
  games_drawn INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE wallet_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  wallet_address VARCHAR(255) UNIQUE NOT NULL,
  wallet_type VARCHAR(50) NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Game Management:**
```sql
CREATE TABLE game_states (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  white_player_id UUID REFERENCES users(id),
  black_player_id UUID REFERENCES users(id),
  current_fen VARCHAR(100) NOT NULL,
  move_history JSONB NOT NULL DEFAULT '[]',
  game_status VARCHAR(20) NOT NULL DEFAULT 'active',
  time_control JSONB NOT NULL,
  game_mode VARCHAR(20) NOT NULL,
  wager_amount INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE game_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id),
  winner_id UUID REFERENCES users(id),
  result_type VARCHAR(20) NOT NULL,
  white_elo_change INTEGER,
  black_elo_change INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE matchmaking_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  game_mode VARCHAR(20) NOT NULL,
  elo_rating INTEGER,
  wager_amount INTEGER,
  entry_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  parameters JSONB
);
```

**Social Features:**
```sql
CREATE TABLE friend_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  friend_id UUID REFERENCES users(id),
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  initiated_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, friend_id)
);

CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id),
  user_id UUID REFERENCES users(id),
  message_type VARCHAR(20) NOT NULL DEFAULT 'TEXT',
  content TEXT NOT NULL,
  is_moderated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Economy and Inventory:**
```sql
CREATE TABLE user_balance (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  evc_balance INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE evc_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  amount INTEGER NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  reference_id UUID,
  reference_type VARCHAR(50),
  balance_after INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  item_type VARCHAR(50) NOT NULL,
  item_id UUID NOT NULL,
  acquisition_type VARCHAR(50) NOT NULL,
  acquisition_reference UUID,
  is_equipped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type, item_id)
);
```

**Progression System:**
```sql
CREATE TABLE user_progression (
  user_id UUID PRIMARY KEY REFERENCES users(id),
  total_xp INTEGER NOT NULL DEFAULT 0,
  current_level INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_missions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  mission_type VARCHAR(10) NOT NULL,
  objective_type VARCHAR(50) NOT NULL,
  required_count INTEGER NOT NULL,
  current_progress INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
  xp_reward INTEGER NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE battlepass_seasons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  premium_price INTEGER NOT NULL,
  total_tiers INTEGER NOT NULL DEFAULT 50,
  is_active BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_battlepass (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  season_id UUID REFERENCES battlepass_seasons(id),
  has_premium BOOLEAN DEFAULT FALSE,
  current_tier INTEGER DEFAULT 0,
  current_xp INTEGER DEFAULT 0,
  claimed_rewards JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, season_id)
);
```

**NFT and Blockchain:**
```sql
CREATE TABLE nft_chess_sets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nft_id INTEGER NOT NULL,
  mint_address VARCHAR(255) UNIQUE NOT NULL,
  metadata_uri VARCHAR(255) NOT NULL,
  owner_wallet VARCHAR(255),
  owner_user_id UUID REFERENCES users(id),
  is_staked BOOLEAN DEFAULT FALSE,
  staking_start_time TIMESTAMP WITH TIME ZONE,
  attributes JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE blockchain_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  transaction_hash VARCHAR(255) NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  reference_id UUID,
  reference_type VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. API Endpoints

#### 4.1 Authentication API

```
POST /api/auth/register
  - Registers a new user
  - Request: { email, password, username }
  - Response: { userId, token, profile }

POST /api/auth/login
  - Authenticates a user
  - Request: { email, password } or { provider, token }
  - Response: { userId, token, profile }

POST /api/auth/wallet/connect
  - Connects a wallet to a user account
  - Request: { walletAddress, signature, message }
  - Response: { success, wallet }

POST /api/auth/logout
  - Logs out a user
  - Response: { success }
```

#### 4.2 User API

```
GET /api/users/profile
  - Retrieves the current user's profile
  - Response: { profile, stats, settings }

PUT /api/users/profile
  - Updates the user's profile
  - Request: { username?, avatar?, settings? }
  - Response: { profile }

GET /api/users/:userId/public
  - Retrieves public profile of another user
  - Response: { username, avatar, stats }
```

#### 4.3 Game API

```
POST /api/games/create
  - Creates a new game
  - Request: { gameMode, timeControl, opponentId? }
  - Response: { gameId, initialState }

POST /api/games/matchmaking
  - Enters matchmaking queue
  - Request: { gameMode, parameters? }
  - Response: { queueId }

POST /api/games/:gameId/move
  - Makes a move in a game
  - Request: { from, to, promotion? }
  - Response: { valid, newState, capturedPiece? }

GET /api/games/:gameId
  - Retrieves game state
  - Response: { gameState, players, timeRemaining }

POST /api/games/:gameId/resign
  - Resigns from a game
  - Response: { success }
```

#### 4.4 Social API

```
POST /api/friends/request
  - Sends a friend request
  - Request: { targetUserId }
  - Response: { success, requestId }

PUT /api/friends/request/:requestId
  - Accepts or rejects a friend request
  - Request: { action: 'accept' | 'reject' }
  - Response: { success, friendship }

GET /api/friends
  - Retrieves friend list
  - Response: { friends }

GET /api/leaderboards/global
  - Retrieves global leaderboard
  - Query: { type: 'elo' | 'wins', limit?, offset? }
  - Response: { rankings, userRank }
```

#### 4.5 Economy API

```
GET /api/economy/balance
  - Retrieves EVC balance
  - Response: { balance, transactions }

POST /api/economy/purchase
  - Purchases EVC
  - Request: { packageId, paymentMethod }
  - Response: { transactionId, redirectUrl? }

GET /api/store
  - Retrieves store items
  - Response: { items, featured }

POST /api/store/purchase
  - Purchases an item from the store
  - Request: { itemId }
  - Response: { success, item, newBalance }
```

#### 4.6 Progression API

```
GET /api/progression
  - Retrieves user progression
  - Response: { level, xp, nextLevelXp }

GET /api/missions
  - Retrieves missions
  - Response: { daily, weekly }

POST /api/missions/:missionId/claim
  - Claims mission reward
  - Response: { success, xpAwarded }

GET /api/battlepass/current
  - Retrieves current battlepass
  - Response: { season, progress, rewards }
```

#### 4.7 NFT API

```
GET /api/nft/owned
  - Retrieves owned NFTs
  - Response: { nfts }

POST /api/nft/mint
  - Mints a new NFT
  - Request: { walletAddress }
  - Response: { success, nft }

POST /api/staking/stake
  - Stakes an NFT
  - Request: { nftId, period }
  - Response: { success, stakingInfo }

POST /api/staking/unstake
  - Unstakes an NFT
  - Request: { nftId }
  - Response: { success, rewards }
```

### 5. Security Considerations

#### 5.1 Authentication Security

- JWT tokens with appropriate expiration times
- Refresh token rotation for extended sessions
- HTTPS for all API communications
- Secure storage of sensitive information (passwords hashed with bcrypt)
- Rate limiting for authentication endpoints
- Account lockout after multiple failed attempts

#### 5.2 API Security

- Input validation using Zod schemas
- CORS configuration to prevent unauthorized access
- API rate limiting to prevent abuse
- Request signing for sensitive operations
- Content Security Policy (CSP) headers

#### 5.3 Blockchain Security

- Client-side transaction signing
- Server-side signature verification
- Transaction logging and monitoring
- Escrow system for wagers and transactions
- Secure wallet connection and disconnection

#### 5.4 Data Protection

- Database encryption for sensitive data
- Regular backups with point-in-time recovery
- Audit logging for sensitive operations
- GDPR compliance for user data
- Data minimization principles

## Implementation Plan

### Technical Development Summary

The Everchess platform will be developed across five distinct phases, each focusing on specific aspects of the platform's functionality. This phased approach ensures systematic progress while maintaining focus on delivering a high-quality product.

#### Phase 1: Landing Page & Foundation Setup

**Focus**: Creating compelling landing page and establishing core infrastructure through AI-driven development.

**Key Tasks**:
1. Set up React Native + Expo project for cross-platform development
2. Configure PWA features
3. Initialize backend with Node.js + Express and Socket.IO
4. Set up Supabase with essential tables and schema
5. Enable real-time subscriptions and row-level security
6. Integrate Para Wallet SDK for unified authentication
7. Install Solana libraries for blockchain integration
8. Set up version control with Git
9. Configure AWS for hosting and asset management
10. Set up CI/CD pipeline

**Deliverables**: A functional cross-platform development environment, project structure with TypeScript configuration, connected backend with database and real-time capabilities, authentication system, and deployment workflows.

#### Phase 2: Authentication & User Management

**Focus**: Implementing Para Wallet SDK authentication and user management systems.

**Key Tasks**:
1. Render a 3D chessboard and pieces using Three Fiber
2. Implement chess game logic with Chess.js
3. Develop game mode selection UI
4. Build matchmaking system
5. Create user profile screen
6. Implement post-game summary screen
7. Add basic navigation system
8. Ensure responsive design

**Deliverables**: A fully functional 3D chess game with proper rules and visualization, multiple game modes with appropriate matchmaking, user profiles, post-game summaries, and responsive UI across web and mobile platforms.

#### Phase 3: Complete Dashboard System & Core Chess Experience

**Focus**: Implementing comprehensive dashboard system and core chess gameplay mechanics.

**Key Tasks**:
1. Implement XP leveling system
2. Create seasonal Battlepass
3. Develop daily and weekly missions
4. Build achievements system
5. Create leaderboards
6. Implement friends system
7. Design clubs/teams functionality
8. Integrate in-game currency (EVC)

**Deliverables**: A complete progression system with XP leveling, Battlepass, missions, achievements, competitive leaderboards, social features, and virtual currency for rewards and progression.

#### Phase 4: Key Integrations (4-6 weeks)

**Focus**: Implementing critical integrations for authentication, inventory management, and Web3 functionality.

**Key Tasks**:
1. Implement sign-in authentication (Para Wallet SDK)
2. Create dual inventory system
3. Integrate NFT and digital skins for 3D chess sets
4. Implement web2 digital items
5. Develop in-app store
6. Create 3D asset selection system
7. Build NFT staking system
8. Implement Web3 profile section
9. Prepare infrastructure for USDC rewards

**Deliverables**: A seamless authentication system, unified inventory for both web2 and web3 items, NFT integration for gameplay, in-app store, staking capabilities, enhanced user profiles, and infrastructure for future reward systems.

#### Phase 5: Testing, Security, Deployment, and Monitoring (3-5 weeks)

**Focus**: Ensuring the platform is production-ready through comprehensive testing, security implementation, deployment processes, and monitoring systems.

**Key Tasks**:
1. Implement comprehensive testing strategies
2. Establish robust security protocols
3. Create efficient deployment processes
4. Set up monitoring systems
5. Develop user feedback mechanisms
6. Create technical documentation
7. Optimize performance

**Deliverables**: A comprehensive test suite, robust security measures, streamlined deployment processes, effective monitoring systems, user feedback channels, clear documentation, and optimized performance across different devices.

#### Timeline Summary

- **Total Development Time**: 16-23 weeks (approximately 4-6 months)
- **Critical Path**: Phase 1 → Phase 2 → Phase 3 → Phase 4 → Phase 5
- **Key Milestones**:
  - Foundation completion (end of Phase 1)
  - Playable game (end of Phase 2)
  - Social and progression features (end of Phase 3)
  - Web3 integration (end of Phase 4)
  - Production-ready platform (end of Phase 5)

This phased approach allows for iterative development, with each phase building upon the previous one while maintaining focus on delivering a high-quality product that meets both user needs and technical requirements.


This section provides a detailed breakdown of each phase from the Technical Development Summary, outlining specific tasks, objectives, and deliverables to guide implementation.

### 1. Phase 1: Foundation Setup (Weeks 1-3)

#### 1.1 Cross-Platform Development Environment

**Objectives:**
- Set up React Native + Expo development environment for cross-platform support
- Configure TypeScript for type safety and code quality
- Establish project structure and organization

**Tasks:**
- Initialize React Native project with Expo and TypeScript configuration
- Set up ESLint, Prettier, and Husky for code quality
- Configure directory structure for screens, components, and services
- Set up basic navigation framework

**Deliverables:**
- Initialized React Native + Expo project with TypeScript
- Code quality tools and configurations
- Project structure documentation

#### 1.2 PWA Configuration

**Objectives:**
- Configure Progressive Web App capabilities
- Implement service workers for offline access
- Set up web manifest and icons

**Tasks:**
- Configure web manifest with app metadata
- Implement service workers for caching and offline access
- Set up push notification capabilities for web
- Configure adaptive icons and splash screens

**Deliverables:**
- Functional PWA configuration
- Service workers for offline functionality
- Complete web manifest and assets

#### 1.3 Backend Infrastructure

**Objectives:**
- Set up Node.js + Express backend
- Implement Socket.IO for real-time communication
- Create API structure and middleware

**Tasks:**
- Initialize Node.js project with Express and TypeScript
- Configure Socket.IO for real-time game events
- Set up middleware for authentication, logging, and error handling
- Implement basic API endpoints

**Deliverables:**
- Functional Express.js API with Socket.IO integration
- Middleware for common functionality
- API documentation

#### 1.4 Database Setup with Supabase

**Objectives:**
- Configure Supabase project and connection
- Design and implement database schema
- Set up essential tables for core functionality

**Tasks:**
- Create Supabase project and configure connection
- Design schema for users, games, and core functionality
- Implement migrations for table creation
- Create database access layer in the application

**Deliverables:**
- Configured Supabase project
- Database schema with essential tables
- Migration scripts
- Database access layer

#### 1.5 Authentication and Security

**Objectives:**
- Integrate Para Wallet SDK for unified authentication
- Set up row-level security in Supabase
- Configure real-time subscriptions with security

**Tasks:**
- Implement Para Wallet SDK integration for email, social, and Web3 authentication
- Configure row-level security policies in Supabase
- Set up real-time subscription security
- Create authentication flows in the application

**Deliverables:**
- Multi-method authentication system
- Secure database access policies
- Real-time subscriptions with proper security

#### 1.6 Deployment and Version Control

**Objectives:**
- Set up version control with Git
- Configure AWS for hosting and assets
- Establish CI/CD pipeline

**Tasks:**
- Create GitHub repository with branch protection
- Set up AWS S3 for static assets and CloudFront for CDN
- Configure AWS EC2 for backend hosting
- Implement GitHub Actions for CI/CD

**Deliverables:**
- Git repository with collaboration workflow
- AWS infrastructure for hosting
- Functional CI/CD pipeline
- Deployment documentation

### 2. Phase 2: Core Gameplay and UI (Weeks 4-7)

#### 2.1 3D Chess Implementation

**Objectives:**
- Implement 3D chessboard and pieces with Three.js and React Three Fiber
- Create optimized 3D assets for various devices
- Implement camera controls and perspectives

**Tasks:**
- Set up Three.js and React Three Fiber in the project
- Create 3D chessboard with proper materials and lighting
- Implement 3D chess pieces with optimized geometries
- Add camera controls for different perspectives

**Deliverables:**
- Visually appealing 3D chessboard
- Optimized 3D chess pieces
- Smooth camera controls
- Performance optimizations for mobile devices

#### 2.2 Chess Game Logic

**Objectives:**
- Implement chess rules and move validation with Chess.js
- Create game state management system
- Implement special moves (castling, en passant, promotion)

**Tasks:**
- Integrate Chess.js library for rule validation
- Create game state management with reducers
- Implement special move handling
- Add move history and notation

**Deliverables:**
- Complete chess rules implementation
- Game state management system
- Special moves functionality
- Move history with notation

#### 2.3 Game Modes and Selection UI

**Objectives:**
- Develop interfaces for different game modes
- Implement game mode selection screens
- Create time control options

**Tasks:**
- Design and implement game mode selection UI
- Create screens for Ranked, Wagers, Tournaments, and Custom Games
- Implement time control selection
- Add game creation flow

**Deliverables:**
- Game mode selection interface
- Mode-specific screens and options
- Time control selection UI
- Game creation workflow

#### 2.4 Matchmaking System

**Objectives:**
- Implement Elo-based matchmaking for Ranked play
- Create lobby system for Custom Games
- Implement queue system with real-time updates

**Tasks:**
- Create matchmaking algorithm with Elo rating
- Implement queue management with Socket.IO
- Develop lobby system for custom games
- Add match acceptance and game initialization

**Deliverables:**
- Functional matchmaking system
- Custom game lobby system
- Queue management with real-time updates
- Match creation and initialization

#### 2.5 User Interface and Profiles

**Objectives:**
- Create user profile screens with statistics
- Implement responsive UI for both web and mobile
- Develop post-game summary screens

**Tasks:**
- Design and implement profile screens with player stats
- Create post-game summary with results and rewards
- Implement responsive layouts for different devices
- Add navigation between screens

**Deliverables:**
- User profile screens with statistics
- Post-game summary interface
- Responsive layouts for all screen sizes
- Intuitive navigation system

### 3. Phase 3: Gamified Functionalities (Weeks 8-12)

#### 3.1 XP and Leveling System

**Objectives:**
- Implement XP earning and level progression
- Create visual feedback for XP gains
- Add level-up rewards and benefits

**Tasks:**
- Design XP earning rules for different activities
- Implement level progression system with thresholds
- Create visual effects for XP gains and level-ups
- Add reward distribution for level milestones

**Deliverables:**
- Complete XP and leveling system
- Visual feedback for progression
- Level-up rewards system
- XP transaction logging

#### 3.2 Seasonal Battlepass

**Objectives:**
- Create battlepass system with progression tiers
- Implement free and premium reward tracks
- Add season management and transitions

**Tasks:**
- Design battlepass tier structure and rewards
- Implement progression tracking and tier unlocking
- Create premium upgrade purchase flow
- Add season management with reset logic

**Deliverables:**
- Functional battlepass with tiers
- Free and premium reward tracks
- Season management system
- Premium upgrade functionality

#### 3.3 Missions and Achievements

**Objectives:**
- Implement daily and weekly missions
- Create achievement system with tiers
- Add mission tracking and rewards

**Tasks:**
- Design mission templates and generation system
- Implement mission tracking and completion detection
- Create achievement system with long-term goals
- Add reward claiming and notification system

**Deliverables:**
- Daily and weekly mission system
- Achievement system with progress tracking
- Mission reset and generation logic
- Reward distribution system

#### 3.4 Leaderboards and Competition

**Objectives:**
- Create leaderboards for various metrics
- Implement ranking systems
- Add competitive seasons

**Tasks:**
- Design leaderboard system for different metrics
- Implement ranking calculation and updates
- Create seasonal competitive structure
- Add leaderboard UI with filtering and sorting

**Deliverables:**
- Multiple leaderboard categories
- Ranking system with updates
- Seasonal competition framework
- Leaderboard interface

#### 3.5 Social Features

**Objectives:**
- Implement friends system
- Create social interactions
- Add team-based features

**Tasks:**
- Implement friend requests and friend list management
- Create friend status indicators and challenges
- Develop clubs/teams functionality with shared goals
- Add social activity feeds

**Deliverables:**
- Friend system with requests and management
- Friend status and challenge system
- Clubs/teams with shared objectives
- Social activity tracking

#### 3.6 In-Game Currency

**Objectives:**
- Implement EVC currency system
- Create currency earning and spending flows
- Add transaction history

**Tasks:**
- Design currency system with earning methods
- Implement balance management and transactions
- Create transaction history and logs
- Add currency UI elements throughout the app

**Deliverables:**
- Functional EVC currency system
- Currency earning mechanisms
- Transaction history and logging
- Currency UI components

### 4. Phase 4: Key Integrations (Weeks 13-18)

#### 4.1 Authentication Integration

**Objectives:**
- Enhance authentication with Para Wallet SDK
- Support email, social, and Web3 sign-in methods
- Create unified user identity system

**Tasks:**
- Implement enhanced Para Wallet SDK integration
- Create seamless authentication flows for different methods
- Develop account linking capabilities
- Add profile customization options

**Deliverables:**
- Seamless multi-method authentication
- Account linking system
- Enhanced profile customization
- Secure session management

#### 4.2 Inventory Management

**Objectives:**
- Create unified inventory system for Web2 and Web3 items
- Implement item categorization and filtering
- Add item previews and details

**Tasks:**
- Design inventory data structure for different item types
- Implement inventory UI with categories and filters
- Add item preview functionality
- Create item detail screens

**Deliverables:**
- Unified inventory system
- Item categorization and filtering
- Item previews and detail views
- Inventory management functions

#### 4.3 NFT Chess Set Integration

**Objectives:**
- Implement NFT chess set integration with player/opponent paradigm
- Add ownership verification and asset loading
- Create in-game usage of NFT assets with fallback mechanisms

**Design Philosophy:**
- Each NFT represents a complete themed chess set defined by theme/style rather than traditional colors
- Instead of "white" and "black" pieces, all sets use "player" and "opponent" designations
- Each set includes all 12 pieces (6 unique piece types for each side)
- Sets maintain clear visual distinction between sides while adhering to consistent theme aesthetics

**NFT Metadata Structure:**
```json
{
  "name": "Everchess [Theme] Set #[Number]",
  "symbol": "ECHESS",
  "description": "Limited edition [Theme]-themed 3D chess set for Everchess",
  "image": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/thumbnail.png",
  "animation_url": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/preview.glb",
  "attributes": [
    {"trait_type": "Theme", "value": "[Theme]"},
    {"trait_type": "Rarity", "value": "[Rarity]"},
    {"trait_type": "Edition", "value": "[Number]/[Total]"}
  ],
  "properties": {
    "files": [
      {
        "type": "image/png",
        "uri": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/thumbnail.png"
      },
      {
        "type": "model/gltf-binary",
        "uri": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/preview.glb"
      }
    ],
    "pieces": {
      "player": {
        "pawn": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-pawn.glb",
        "rook": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-rook.glb",
        "knight": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-knight.glb",
        "bishop": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-bishop.glb",
        "queen": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-queen.glb",
        "king": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-king.glb"
      },
      "opponent": {
        "pawn": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-pawn.glb",
        "rook": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-rook.glb",
        "knight": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-knight.glb",
        "bishop": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-bishop.glb",
        "queen": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-queen.glb",
        "king": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-king.glb"
      }
    },
    "theme": {
      "name": "[Theme]",
      "description": "[Theme Description]",
      "designer": "[Designer Name]",
      "materials": ["[Material 1]", "[Material 2]", "[Material 3]"]
    }
  }
}
```

**Asset Loading Strategy:**
- 3D models stored on AWS S3 with CloudFront CDN for global distribution
- Models loaded using Three Fiber's GLTFLoader with optimized handling
- Progressive loading with low-poly placeholders during initial load
- Asset preloading during matchmaking and menu browsing
- Advanced caching to prevent redundant downloads of frequently used sets
- Fallback mechanism that uses default chess set if NFT content fails to load

**Tasks:**
- Implement NFT metadata structure with Metaplex standards
- Create AWS S3 storage structure with proper CORS and content types
- Develop asset loading system with Three Fiber optimizations
- Implement ownership verification via Solana wallet connection
- Add in-game selection and usage of owned NFTs
- Create fallback mechanisms for loading failures

**Deliverables:**
- Complete NFT chess set integration with player/opponent paradigm
- Solana-based ownership verification system
- Optimized 3D model loading with caching and fallbacks
- In-game NFT selection UI with preview functionality
- NFT chess set library with asset management dashboard

#### 4.4 Digital Items Implementation

**Objectives:**
- Create Web2 digital items (emotes, profile items, environments)
- Implement item acquisition and usage
- Add customization options

**Tasks:**
- Design various digital item types
- Implement acquisition methods (purchase, rewards)
- Create item equipping and usage functionality
- Add customization UI

**Deliverables:**
- Various digital item types
- Item acquisition methods
- Equipping and usage functionality
- Customization interfaces

#### 4.5 In-App Store

**Objectives:**
- Create comprehensive store interface
- Implement purchase flows for different item types
- Add store sections and categories

**Tasks:**
- Design store UI with sections and categories
- Implement purchase flows for EVC, items, and battlepass
- Create promotional features (sales, featured items)
- Add confirmation and receipt system

**Deliverables:**
- Comprehensive store interface
- Purchase flows for different items
- Promotional features
- Purchase confirmation system

#### 4.6 NFT Staking and Web3 Features

**Objectives:**
- Implement NFT staking functionality
- Create staking rewards system
- Add Web3 profile sections

**Tasks:**
- Design staking system with different periods
- Implement staking contract integration
- Create rewards distribution
- Add Web3 profile section with wallet info and earnings

**Deliverables:**
- NFT staking functionality
- Staking rewards system
- Web3 profile section
- Staking management UI

### 5. Phase 5: Testing, Security, Deployment, and Monitoring (Weeks 19-23)

#### 5.1 Comprehensive Testing

**Objectives:**
- Implement testing strategies for all components
- Create automated tests for critical functionality
- Conduct user testing

**Tasks:**
- Implement unit tests for core components
- Create integration tests for system flows
- Develop end-to-end tests for user journeys
- Conduct user testing sessions

**Deliverables:**
- Comprehensive test suite
- Automated testing pipeline
- User testing results and feedback
- Quality assurance documentation

#### 5.2 Security Implementation

**Objectives:**
- Enhance application security
- Implement authentication safeguards
- Create data protection measures

**Tasks:**
- Conduct security audit of all components
- Implement additional authentication safeguards
- Create data encryption for sensitive information
- Add transaction security for blockchain operations

**Deliverables:**
- Security audit report
- Enhanced authentication security
- Data protection measures
- Secure blockchain transactions

#### 5.3 Deployment Process

**Objectives:**
- Finalize deployment processes for all platforms
- Implement staged release strategy
- Create rollback procedures

**Tasks:**
- Refine CI/CD pipelines for different platforms
- Create environment-specific configurations
- Implement release management process
- Add monitoring for deployment health

**Deliverables:**
- Finalized deployment processes
- Environment configurations
- Release management procedure
- Rollback mechanisms

#### 5.4 Monitoring and Analytics

**Objectives:**
- Implement comprehensive monitoring
- Create analytics dashboards
- Add performance tracking

**Tasks:**
- Set up application monitoring with metrics
- Implement user behavior analytics
- Create performance tracking for critical operations
- Add blockchain transaction monitoring

**Deliverables:**
- Monitoring system with alerts
- Analytics dashboards
- Performance tracking reports
- Transaction monitoring

#### 5.5 User Feedback and Optimization

**Objectives:**
- Implement feedback mechanisms
- Create optimization based on metrics
- Add user satisfaction tracking

**Tasks:**
- Implement in-app feedback tools
- Create user surveys and satisfaction tracking
- Design bug reporting system
- Add feature request tracking

**Deliverables:**
- In-app feedback mechanisms
- User survey system
- Bug reporting tools
- Feature request tracking

#### 5.6 Documentation and Preparation

**Objectives:**
- Create comprehensive documentation
- Prepare for public release
- Finalize marketing materials

**Tasks:**
- Create technical documentation
- Develop user guides and tutorials
- Prepare promotional materials
- Finalize app store listings

**Deliverables:**
- Technical documentation
- User guides and tutorials
- Promotional materials
- App store listings

### 6. Post-MVP Features (Future Development)

#### 6.1 Tournament System

**Planned Features:**
- Custom tournament creation with various formats
- Bracket management and progression
- Prize distribution system
- Spectator mode for live tournaments
- Tournament history and statistics

#### 6.2 Advanced Social Features

**Planned Features:**
- Enhanced clubs and teams with hierarchies
- Social feeds with activity sharing
- Game sharing and replays
- Advanced chat options with voice support
- Community events and challenges

#### 6.3 Enhanced Analytics and Game Analysis

**Planned Features:**
- Detailed game analysis tools
- Move evaluation and suggestions
- Performance metrics and trends
- Improvement recommendations
- Replay analysis with annotations

#### 6.4 Additional Game Modes

**Planned Features:**
- Chess variants (Chess960, Atomic Chess, etc.)
- Puzzle mode with daily challenges
- Training mode with focused exercises
- AI opponents with varying difficulty
- Custom rule creation

## Security Guidelines

This section outlines the security measures and best practices that will be implemented in the Everchess platform to protect user data, prevent unauthorized access, and ensure the integrity of gameplay and transactions.

### 1. Authentication and Authorization

#### 1.1 User Authentication

**Web2 Authentication:**
- Password requirements: Minimum 8 characters, including uppercase, lowercase, numbers, and special characters
- Password hashing using bcrypt with appropriate salt rounds
- Rate limiting for login attempts (5 attempts per 15 minutes)
- Account lockout after multiple failed attempts with email notification
- Two-factor authentication (2FA) support via email or authenticator apps
- Session management with secure, HttpOnly cookies
- JWT tokens with appropriate expiration (1 hour access token, 7-day refresh token)
- Refresh token rotation to prevent token reuse

**Web3 Authentication:**
- Message signing for wallet authentication
- Nonce-based challenge-response to prevent replay attacks
- Signature verification on the server side
- Wallet address validation and checksum verification
- Linking Web2 and Web3 accounts with secure verification
- Session invalidation upon wallet disconnection

#### 1.2 Authorization Framework

**Role-Based Access Control (RBAC):**
- User roles: Player, Moderator, Admin
- Permission-based access to resources and actions
- Principle of least privilege for all system components
- Regular permission audits and reviews

**API Authorization:**
- JWT validation for all protected endpoints
- Scope-based permissions for API access
- Resource ownership verification
- Request signing for sensitive operations

### 2. Data Protection

#### 2.1 Data Encryption

**In Transit:**
- HTTPS/TLS 1.3 for all communications
- Certificate pinning for mobile applications
- Strong cipher suites with forward secrecy
- HTTP Strict Transport Security (HSTS)

**At Rest:**
- Database encryption for sensitive fields
- Encryption of user credentials and payment information
- Secure key management using AWS KMS
- Regular rotation of encryption keys

#### 2.2 Data Handling

**Personal Data:**
- Minimization of collected personal data
- Clear data retention policies
- Secure data deletion processes
- GDPR and CCPA compliance
- Privacy policy with clear data usage explanations

**Payment Information:**
- PCI DSS compliance for payment processing
- Tokenization of payment methods
- No storage of complete credit card numbers
- Secure handling of payment provider callbacks

### 3. Application Security

#### 3.1 API Security

**Input Validation:**
- Schema validation for all API requests using Zod
- Sanitization of user inputs to prevent injection attacks
- Type checking and boundary validation
- Rejection of malformed requests with appropriate error messages

**Rate Limiting:**
- Tiered rate limits based on endpoint sensitivity
- IP-based and user-based rate limiting
- Graduated response to rate limit violations
- Clear rate limit headers in responses

**CORS and Headers:**
- Strict CORS policy with whitelisted origins
- Content Security Policy (CSP) headers
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- Referrer-Policy: strict-origin-when-cross-origin

#### 3.2 Mobile Application Security

**Code Protection:**
- Code obfuscation for production builds
- Prevention of reverse engineering
- Secure storage of API keys and secrets
- Tamper detection mechanisms

**Local Storage:**
- Encryption of sensitive data in AsyncStorage
- Secure handling of authentication tokens
- Clearing sensitive data on logout
- Biometric authentication for accessing stored credentials

### 4. Blockchain Security

#### 4.1 Smart Contract Security

**Contract Development:**
- Formal verification of smart contracts
- Adherence to secure coding standards
- Comprehensive test coverage
- Independent security audits before deployment

**Transaction Security:**
- Client-side transaction signing
- Server-side signature verification
- Transaction simulation before submission
- Gas limit and fee optimization

#### 4.2 Wallet Integration

**Connection Security:**
- Secure wallet connection protocols
- Clear user consent for all wallet operations
- Disconnection handling and session cleanup
- Support for hardware wallets for enhanced security

**Asset Protection:**
- Escrow system for wagers and transactions
- Multi-signature requirements for high-value operations
- Transaction monitoring for suspicious activity
- Recovery mechanisms for failed transactions

### 5. Infrastructure Security

#### 5.1 Cloud Security

**AWS Security:**
- VPC configuration with private subnets
- Security groups with least privilege access
- IAM roles and policies with minimal permissions
- Regular security assessments and penetration testing

**Kubernetes Security:**
- Pod security policies
- Network policies for pod-to-pod communication
- Secret management using AWS Secrets Manager
- Regular security scanning of container images

#### 5.2 Monitoring and Incident Response

**Security Monitoring:**
- Real-time monitoring for suspicious activities
- Anomaly detection for unusual patterns
- Log aggregation and analysis
- Alerts for security events

**Incident Response:**
- Documented incident response procedures
- Defined roles and responsibilities
- Communication templates for security incidents
- Regular drills and simulations

### 6. Game Integrity and Anti-Cheating

#### 6.1 Move Validation

**Server-Side Validation:**
- All moves validated on the server
- Comparison with chess engine recommendations
- Pattern detection for suspicious play
- Time analysis for move consistency

**Client Protection:**
- Secure communication for move submission
- Prevention of client-side manipulation
- Integrity checks for game state
- Detection of automated play or bots

#### 6.2 Reporting and Moderation

**User Reporting:**
- In-game reporting mechanism for suspicious behavior
- Evidence collection for reported incidents
- Reviewer assignment and case tracking
- Feedback to reporters on outcomes

**Moderation Tools:**
- Admin dashboard for reviewing flagged games
- Tools for analyzing play patterns
- Action options: warning, suspension, ban
- Appeal process for disputed decisions

### 7. Security Testing and Compliance

#### 7.1 Security Testing

**Automated Testing:**
- Static Application Security Testing (SAST)
- Dynamic Application Security Testing (DAST)
- Dependency scanning for vulnerabilities
- Regular security regression testing

**Manual Testing:**
- Penetration testing by security professionals
- Code reviews with security focus
- Social engineering assessments
- Red team exercises

#### 7.2 Compliance and Certification

**Industry Standards:**
- OWASP Top 10 compliance
- GDPR and CCPA compliance
- Mobile application security standards
- Gaming industry best practices

**Security Documentation:**
- Security architecture documentation
- Threat modeling documentation
- Risk assessment and mitigation plans
- Security policies and procedures

### 8. Security Roadmap

#### 8.1 MVP Security Features

**Phase 1 (Launch):**
- Basic authentication and authorization
- Data encryption in transit and at rest
- Input validation and sanitization
- Rate limiting and basic DDoS protection
- Server-side move validation
- Basic monitoring and alerting

#### 8.2 Post-MVP Security Enhancements

**Phase 2 (3 Months Post-Launch):**
- Two-factor authentication
- Enhanced anti-cheating measures
- Advanced anomaly detection
- Expanded security monitoring
- Additional penetration testing

**Phase 3 (6 Months Post-Launch):**
- Hardware wallet support
- Advanced threat protection
- Automated security response
- Enhanced compliance features
- Security bug bounty program

## Risk Assessment and Mitigation

This section identifies potential risks to the Everchess platform development and operational success, along with specific mitigation strategies for each risk category.

### 1. Technical Risks

#### 1.1 Three Fiber Performance on Low-End Devices

**Risk Description:**
Rendering 3D chess pieces and environments with Three Fiber may cause performance issues on lower-end mobile devices, leading to poor user experience and potential customer loss.

**Impact Level:** High
**Probability:** Medium

**Mitigation Strategies:**
- Implement level-of-detail (LOD) system that automatically reduces model complexity on lower-end devices
- Create performance profiles with optimized assets for different device capabilities
- Develop fallback 2D mode for devices that cannot handle 3D rendering
- Implement aggressive asset compression and optimization pipeline
- Add performance monitoring telemetry to identify problematic devices and configurations

**Success Criteria:**
- Target minimum 30 FPS on entry-level devices (iPhone SE, budget Android phones)
- Maximum initial load time of 5 seconds on 4G connections
- Memory usage below 300MB for graphics rendering

#### 1.2 Real-time Multiplayer Synchronization

**Risk Description:**
Lag, disconnections, or synchronization issues during gameplay could frustrate users and damage platform reputation.

**Impact Level:** High
**Probability:** Medium

**Mitigation Strategies:**
- Implement predictive move rendering on client-side
- Create reconnection mechanism with state recovery
- Develop graceful degradation for poor network conditions
- Add regional server deployment to minimize latency
- Implement comprehensive connection quality monitoring

**Success Criteria:**
- Maximum move transmission latency of 300ms
- 99.9% successful game completion rate
- Automatic recovery from up to 10-second connection disruptions

#### 1.3 Web3 Wallet Connection Issues

**Risk Description:**
Web3 wallet connections may fail, timeout, or experience blockchain network congestion, causing frustration for NFT collectors.

**Impact Level:** Medium
**Probability:** High

**Mitigation Strategies:**
- Implement wallet connection caching to reduce reconnection frequency
- Create alternate authentication methods as fallbacks
- Add clear error messaging with troubleshooting steps
- Develop offline mode for viewing owned NFT chess sets
- Create proxy verification system that minimizes blockchain queries

**Success Criteria:**
- Wallet connection success rate above 95%
- Maximum connection time under 3 seconds
- Users retain access to owned assets even during blockchain congestion

### 2. Market and Business Risks

#### 2.1 Mobile App Store Compliance

**Risk Description:**
App store policies regarding Web3 integration, NFTs, and in-app purchases may lead to rejection or removal of the Everchess app.

**Impact Level:** Critical
**Probability:** Medium

**Mitigation Strategies:**
- Create dual implementation approach with Web3 features only in web version if necessary
- Develop clear separation between standard features and Web3 features
- Maintain updated knowledge of app store policies and restrictions
- Establish relationships with app store review teams
- Create contingency plans for rapid compliance adjustments

**Success Criteria:**
- Successful approval on first submission to app stores
- Compliance with all relevant app store policies without compromise to core features
- No unexpected delays to launch timeline due to app store issues

#### 2.2 NFT Market Volatility

**Risk Description:**
Fluctuations in the broader NFT market could affect user interest and valuation of Everchess NFT chess sets.

**Impact Level:** Medium
**Probability:** High

**Mitigation Strategies:**
- Emphasize utility and in-game benefits of NFT ownership beyond speculative value
- Develop a sustainable economic model not dependent on continuous growth
- Create game-first approach where Web3 elements enhance but don't dominate
- Build non-Web3 revenue streams to balance business model
- Maintain flexible NFT implementation that can adapt to market conditions

**Success Criteria:**
- Retention of NFT owners above 85% during market downturns
- Stable or growing engagement metrics regardless of NFT market conditions
- Balanced revenue mix between Web3 and traditional sources

### 3. Schedule and Resource Risks

#### 3.1 Development Timeline Slippage

**Risk Description:**
Solo development of a complex platform may lead to schedule delays and feature compromises.

**Impact Level:** High
**Probability:** High

**Mitigation Strategies:**
- Implement phased development approach with clear MVP definition
- Prioritize features based on user value and technical dependencies
- Build flexible architecture that allows incremental feature additions
- Establish clear scope boundaries and manage feature creep
- Create contingency time buffers for complex technical challenges

**Success Criteria:**
- Critical path features completed within 10% of estimated timeline
- No more than 20% timeline adjustment for complete MVP delivery
- Regular milestone achievements with functional demos

#### 3.2 Technical Debt Accumulation

**Risk Description:**
Rapid development cycles may lead to technical debt that slows future development and causes stability issues.

**Impact Level:** Medium
**Probability:** High

**Mitigation Strategies:**
- Establish clear code quality standards from project inception
- Schedule regular refactoring sprints to address emerging technical debt
- Implement comprehensive automated testing suite
- Document architectural decisions and technical tradeoffs
- Regularly review and update documentation

**Success Criteria:**
- Test coverage maintained above 70% for critical components
- All known bugs and technical debt items tracked in issue system
- Architecture documentation updated at least bi-weekly

### Risk Management Process

**Risk Monitoring:**
- Weekly review of risk registry with updated impact and probability assessments
- Metrics tracking for key risk indicators
- Regular technical performance assessments

**Risk Response:**
- Trigger points defined for each major risk category
- Escalation procedures for critical risks
- Contingency plans with resource allocations

**Risk Communication:**
- Clear communication channels for risk status updates
- Transparent reporting on risk mitigation progress
- Feedback mechanisms for identifying new risks

## Implementation Timeline and Milestones

This section provides a visual representation of the Everchess development timeline, highlighting key milestones, dependencies, and critical paths.

### Development Phases Timeline

```
WEEKS:  |0       |4       |8       |12      |16      |20      |24      |28
        |‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾
Phase 1 |████████████████|
        |                |      
Phase 2 |                |████████████████|
        |                                  |
Phase 3 |                                  |████████████████████|
        |                                                      |
Phase 4 |                                  |████████████████████████|
        |                                                              |
Phase 5 |                                                      |██████████|
        |                                                              |
        |                                                              |
MVP     |                                                              |◆
Release |                                                              |
```

### Critical Path and Dependencies

```
[Foundation Setup] ─────────┐
                             ▼
[Core Game Logic] ──┬─────► [3D UI] ─────────┐
                    │                         ▼
[Matchmaking] ──────┘                 [Progression System] ───┐
                                                             ▼
[Authentication] ────────────────────────────► [Web3 Integration] ─┐
                                                                  ▼
[Social Features] ────────────────────────────────────────► [NFT Chess Sets] ─┐
                                                                              ▼
                                                                     [MVP DEPLOYMENT]
```

### Key Milestones

| Milestone | Timeline | Dependencies | Deliverables |
|-----------|----------|--------------|---------------|
| **M1: Development Environment** | Week 1-2 | None | Project repo, AWS setup, CI/CD pipeline |
| **M2: Core Game Mechanics** | Week 6-7 | M1 | Chess rules, 3D board, basic moves |
| **M3: Multiplayer MVP** | Week 10 | M2 | Real-time gameplay, matchmaking |
| **M4: Auth and Accounts** | Week 12 | M1 | Authentication, profiles, leaderboards |
| **M5: Progression Systems** | Week 16 | M3, M4 | XP, missions, battlepass |
| **M6: Web3 Foundation** | Week 18 | M4 | Wallet connection, NFT groundwork |
| **M7: NFT Chess Sets** | Week 20-22 | M2, M6 | NFT minting, ownership, rendering |
| **M8: Public Beta** | Week 24 | M3, M5, M7 | Limited audience testing |
| **M9: MVP Launch** | Week 26-28 | All | Production deployment |

### Mobile App Store Submission Timeline

```
WEEKS:  |18      |20      |22      |24      |26      |28
        |‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾‾‾‾|‾‾‾‾‾
App     |        |████████| Store   |        |        |
Prep    |        |        | Review  |        |        |
        |        |        |         |        |        |
App     |        |        |███████| iOS App |        |
Store   |        |        |        | Approval|        |
Submit  |        |        |        |         |        |
        |        |        |        |         |        |
Google  |        |        |████| Play Store |        |
Play    |        |        |    | Approval   |        |
Submit  |        |        |    |            |        |
        |        |        |    |            |        |
Web     |        |        |    |            |██████| 
PWA     |        |        |    |            |Deployed|
Deploy  |        |        |    |            |        |
```

### Timeline Flexibility and Contingency

This timeline includes approximately 20% contingency buffer distributed across critical path items. The most significant buffer allocations are for:

1. **Three Fiber Rendering Optimization**: 2-week buffer (Phase 2)
2. **Web3 Integration**: 1.5-week buffer (Phase 4)
3. **Mobile App Store Approval**: 2-week buffer (Phase 5)

The minimum viable path to launch is 18 weeks, with 26-28 weeks representing the expected timeline with contingencies. Individual phase durations may adjust based on development velocity, but the critical path and dependencies will remain consistent.

## Success Metrics and KPIs

This section outlines the key performance indicators (KPIs) and success metrics that will be used to evaluate the Everchess platform's performance and guide future development decisions.

### 1. Technical Performance Metrics

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|----------|
| **App Load Time** | < 3 seconds | Client telemetry | Real-time |
| **Game Initialization** | < 5 seconds | Client telemetry | Real-time |
| **Frame Rate** | > 45 FPS average | Performance monitoring | Real-time |
| **API Response Time** | < 200ms (95th percentile) | Server monitoring | Hourly |
| **Socket Latency** | < 300ms average | Connection monitoring | Real-time |
| **Error Rate** | < 0.5% of operations | Error tracking | Daily |
| **Crash Rate** | < 0.1% of sessions | Crash reporting | Daily |
| **Database Query Performance** | < 100ms (95th percentile) | Query monitoring | Hourly |
| **Asset Load Time** | < 2 seconds (3D models) | Resource timing | Real-time |
| **Network Bandwidth** | < 5MB per game | Traffic monitoring | Weekly |

### 2. User Engagement Metrics

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|----------|
| **Daily Active Users (DAU)** | > 1,000 within 30 days | User analytics | Daily |
| **Monthly Active Users (MAU)** | > 5,000 within 90 days | User analytics | Monthly |
| **Session Duration** | > 15 minutes average | Session tracking | Daily |
| **Sessions Per User** | > 5 sessions per week | User analytics | Weekly |
| **Retention Rate** | > 40% D1, > 25% D7, > 15% D30 | Cohort analysis | Daily/Weekly |
| **Game Completion Rate** | > 90% of started games | Game analytics | Daily |
| **Feature Adoption** | > 60% use of core features | Feature tracking | Weekly |
| **Social Engagement** | > 30% friend connections | Social analytics | Weekly |
| **Progression Depth** | > Level 10 average within 30 days | User progression | Weekly |
| **Match Frequency** | > 3 games per active day | Game analytics | Daily |

### 3. Business and Revenue Metrics

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|----------|
| **NFT Mint Rate** | > 100 chess sets in first 30 days | Blockchain tracking | Daily |
| **NFT Ownership** | > 20% of active users | Wallet tracking | Weekly |
| **Transaction Volume** | > $5,000 in first 90 days | Financial tracking | Daily |
| **Revenue Per User** | > $5 average revenue per paying user | Financial analytics | Monthly |
| **Conversion Rate** | > 5% of MAU to paying users | Conversion tracking | Monthly |
| **Staking Participation** | > 30% of NFT owners | Staking analytics | Weekly |
| **Cost Per Acquisition** | < $2 per new user | Marketing analytics | Weekly |
| **Customer Acquisition Cost (CAC)** | < $10 per paying user | Marketing ROI | Monthly |
| **Lifetime Value (LTV)** | > $25 per paying user | LTV modeling | Quarterly |
| **LTV:CAC Ratio** | > 3:1 | Financial analytics | Quarterly |

### 4. User Experience Metrics

| Metric | Target | Measurement Method | Frequency |
|--------|--------|-------------------|----------|
| **App Store Rating** | > 4.5 stars average | App store analytics | Weekly |
| **User Satisfaction** | > 85% positive feedback | In-app surveys | Monthly |
| **Support Ticket Volume** | < 1% of MAU creating tickets | Support system | Weekly |
| **First-Time User Completion** | > 80% tutorial completion | Onboarding analytics | Daily |
| **Matchmaking Time** | < 30 seconds average wait | Matchmaking analytics | Real-time |
| **Matchmaking Satisfaction** | > 90% fair match rating | Post-match surveys | Weekly |
| **UI Interaction Success** | > 95% intended action completion | UI analytics | Weekly |
| **Feature Discoverability** | > 70% feature discovery rate | Feature analytics | Monthly |

### Success Criteria Evaluation

#### Launch Phase Success Criteria (First 30 Days)
- Technical performance metrics within 90% of targets
- At least 1,000 DAU achieved
- Minimum 40% D1 retention achieved
- Zero critical security incidents
- App store ratings above 4.0

#### Growth Phase Success Criteria (90 Days)
- All technical performance metrics meeting targets
- At least 5,000 MAU achieved
- Retention metrics meeting all targets
- Positive revenue trajectory established
- Engaged NFT community forming

#### Measurement and Reporting

- **Real-time Dashboard**: Technical metrics and core user engagement
- **Daily Reports**: Key performance changes and anomaly detection
- **Weekly Reviews**: Comprehensive KPI review with trend analysis
- **Monthly Deep Dives**: In-depth analysis of all metrics with actionable insights

These metrics will guide ongoing development priorities and help identify areas requiring optimization or additional features. All metrics will be tracked from day one of public launch, with baseline measurements established during beta testing.

## Development Standards

This section outlines the coding standards, architectural principles, development practices, and design standards that will guide the implementation of the Everchess platform, ensuring code quality, maintainability, scalability, and visual consistency.

### 1. UI Design Standards

#### 1.1 Typography

**Primary Font: Sora**
- Used for: Headers, navigation elements, buttons, and interactive elements
- Characteristics: Modern, clean, geometric sans-serif with excellent readability
- Implementation: `font-family: 'Sora', sans-serif;`
- Weights used: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)

**Secondary Font: Lora**
- Used for: Body text, descriptions, and long-form content
- Characteristics: Elegant serif with balanced contrast and readability
- Implementation: `font-family: 'Lora', serif;`
- Weights used: 400 (regular), 500 (medium), 700 (bold)

**Font Integration:**
- Web: Import via Google Fonts or self-host for performance
- Mobile: Include in the app bundle using React Native's font linking

**Accessibility Considerations:**
- Minimum text size of 14px for body content
- Maintain WCAG 2.1 AA compliance for text contrast
- Support dynamic font sizing for accessibility settings

### 2. Code Organization and Structure

#### 1.1 Project Structure

**Cross-Platform Application (Frontend):**
```
everchess-app/
├── src/
│   ├── assets/            # Images, fonts, and static resources
│   ├── components/        # Reusable UI components
│   │   ├── common/        # Generic components (buttons, inputs, etc.)
│   │   ├── game/          # Game-specific components
│   │   ├── profile/       # Profile-related components
│   │   └── social/        # Social feature components
│   ├── hooks/             # Custom React hooks
│   ├── navigation/        # Navigation configuration
│   ├── screens/           # Screen components
│   │   ├── auth/          # Authentication screens
│   │   ├── game/          # Game-related screens
│   │   ├── profile/       # Profile screens
│   │   ├── social/        # Social screens
│   │   └── store/         # Store and economy screens
│   ├── services/          # API and service integrations
│   │   ├── api/           # API clients and endpoints
│   │   ├── game/          # Game logic services
│   │   ├── auth/          # Authentication services
│   │   └── web3/          # Blockchain services
│   ├── store/             # Redux store configuration
│   │   ├── slices/        # Redux Toolkit slices
│   │   ├── selectors/     # Reselect selectors
│   │   └── middleware/    # Custom middleware
│   ├── utils/             # Utility functions
│   ├── constants/         # Constants and configuration
│   └── types/             # TypeScript type definitions
├── assets/                # Expo assets
├── app.json               # Expo configuration
├── App.tsx                # Entry point
├── babel.config.js        # Babel configuration
├── tsconfig.json          # TypeScript configuration
└── package.json           # Dependencies and scripts
```

**Cross-Platform Application (Backend):**
```
everchess-api/
├── src/
│   ├── config/            # Configuration files
│   │   ├── database.ts    # Database connection configuration
│   │   ├── socket.ts      # Socket.IO configuration
│   │   └── env.ts         # Environment configuration
│   ├── controllers/       # Route controllers
│   │   ├── auth/          # Authentication controllers
│   │   ├── game/          # Game-related controllers
│   │   ├── user/          # User profile controllers
│   │   └── web3/          # Blockchain interaction controllers
│   ├── middleware/        # Express middleware
│   │   ├── auth.ts        # Authentication middleware
│   │   ├── validation.ts  # Request validation middleware
│   │   └── error.ts       # Error handling middleware
│   ├── models/            # Data models and types
│   ├── routes/            # API route definitions
│   │   ├── auth.ts        # Authentication routes
│   │   ├── game.ts        # Game-related routes
│   │   ├── user.ts        # User profile routes
│   │   └── web3.ts        # Blockchain routes
│   ├── services/          # Business logic services
│   │   ├── auth.ts        # Authentication service
│   │   ├── game.ts        # Game logic service
│   │   ├── matchmaking.ts # Matchmaking service
│   │   └── blockchain.ts  # Blockchain service
│   ├── socket/            # Socket.IO event handlers
│   │   ├── game.ts        # Game-related socket events
│   │   ├── chat.ts        # Chat socket events
│   │   └── notifications.ts # Notification socket events
│   ├── utils/             # Utility functions
│   └── app.ts             # Express application setup
├── prisma/                # Prisma ORM schema and migrations
├── .env                   # Environment variables
├── tsconfig.json          # TypeScript configuration
├── package.json           # Dependencies and scripts
└── jest.config.js         # Test configuration
```

## Technical Documentation Standards

All development work will adhere to strict documentation standards to ensure code quality, consistency, and maintainability. Documentation will be maintained in the following areas:

### Official Documentation References

The following official documentation should be referenced for best practices and implementation guidelines:

#### Core Technology Stack

**Frontend Development:**
- React Native: [React Native Documentation](https://reactnative.dev/docs/getting-started)
- Expo: [Expo Documentation](https://docs.expo.dev/)
- React Three Fiber: [React Three Fiber Documentation](https://r3f.docs.pmnd.rs/)
- Redux Toolkit: [Redux Toolkit Documentation](https://redux-toolkit.js.org/introduction/getting-started)
- React Navigation: [React Navigation Documentation](https://reactnavigation.org/docs/getting-started)

**Backend Development:**
- Node.js: [Node.js Documentation](https://nodejs.org/en/docs/)
- Express: [Express Documentation](https://expressjs.com/)
- Socket.IO: [Socket.IO Documentation](https://socket.io/docs/v4/server-api/)
- TypeScript: [TypeScript Documentation](https://www.typescriptlang.org/docs/)

**Data Storage and Management:**
- Supabase: [Supabase Documentation](https://supabase.com/docs)
- AWS S3: [AWS S3 Documentation](https://docs.aws.amazon.com/s3/)

**Web3 Integration:**
- Solana Web3.js: [Solana Web3.js Documentation](https://solana-labs.github.io/solana-web3.js/)
- Para Wallet SDK: [Para Wallet SDK Documentation](https://docs.getpara.com)

**Deployment and DevOps:**
- TestFlight: [TestFlight Documentation](https://developer.apple.com/testflight/)
- Google Play Console: [Google Play Console Documentation](https://developer.android.com/distribute/console)
- App Store Guidelines: [App Store Guidelines](https://developer.apple.com/app-store/review/guidelines/)

### Code Documentation Requirements

1. **Inline Documentation:**
   - All functions, methods, and components must have JSDoc-style comments
   - Complex algorithms must include explanatory comments
   - Edge cases and potential issues should be documented

2. **README Files:**
   - Each major module should have a README.md explaining its purpose and usage
   - Setup instructions should be clear and comprehensive
   - Include examples for common use cases

3. **API Documentation:**
   - All API endpoints must be documented with request/response formats
   - Authentication requirements must be clearly stated
   - Error codes and responses should be fully documented

4. **Architecture Documentation:**
   - System architecture diagrams should be maintained and updated
   - Data flow diagrams for complex operations
   - Entity relationship diagrams for database design

#### 1.2 Module Organization

**Feature-Based Organization:**
- Group related functionality by feature rather than type
- Each feature module should be self-contained with minimal dependencies
- Clear boundaries between feature modules
- Shared functionality in common modules

**Dependency Direction:**
- Follow the dependency rule: outer layers depend on inner layers
- Core domain logic should have no dependencies on UI or infrastructure
- Infrastructure code depends on application code, not vice versa
- Use dependency injection for external services

### 2. Coding Standards

#### 2.1 TypeScript Standards

**Type Safety:**
- Use strict TypeScript configuration
- Avoid `any` type except when absolutely necessary
- Define interfaces for all data structures
- Use union types for discriminated unions
- Leverage generics for reusable components

**Naming Conventions:**
- Use PascalCase for types, interfaces, and classes
- Use camelCase for variables, functions, and methods
- Use UPPER_CASE for constants
- Use descriptive names that convey purpose
- Prefix interfaces with `I` (e.g., `IUserProfile`)

**Code Formatting:**
- Use Prettier for consistent formatting
- 2-space indentation
- 100 character line length limit
- Semicolons required
- Single quotes for strings

**Example:**
```typescript
// Good example
interface IUserProfile {
  id: string;
  username: string;
  displayName?: string;
  eloRating: number;
  lastActive: Date;
}

// Function with proper typing
function calculateEloChange(
  playerRating: number,
  opponentRating: number,
  result: 'win' | 'loss' | 'draw'
): number {
  const expectedScore = 1 / (1 + Math.pow(10, (opponentRating - playerRating) / 400));
  let actualScore: number;
  
  switch (result) {
    case 'win':
      actualScore = 1;
      break;
    case 'loss':
      actualScore = 0;
      break;
    case 'draw':
      actualScore = 0.5;
      break;
  }
  
  const kFactor = playerRating < 2100 ? 32 : 24;
  return Math.round(kFactor * (actualScore - expectedScore));
}
```

#### 2.2 React and React Native Standards

**Component Structure:**
- Use functional components with hooks
- One component per file
- Group related components in directories
- Use named exports for components
- Include prop type definitions

**State Management:**
- Use Redux for global state
- Use React Context for theme and localization
- Use local state for component-specific state
- Normalize Redux store structure
- Use Redux Toolkit for Redux logic

**Component Example:**
```tsx
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { Button } from '../common/Button';
import { selectUserProfile } from '../../store/selectors/userSelectors';
import { updateProfile } from '../../store/slices/userSlice';
import { IProfileUpdateParams } from '../../types/user';

interface ProfileHeaderProps {
  onEditPress: () => void;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({ onEditPress }) => {
  const profile = useSelector(selectUserProfile);
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    // Component logic here
  }, [profile]);
  
  const handleStatusUpdate = async (status: string) => {
    setIsLoading(true);
    try {
      const params: IProfileUpdateParams = { status };
      await dispatch(updateProfile(params)).unwrap();
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.username}>{profile.username}</Text>
      <Text style={styles.eloRating}>ELO: {profile.eloRating}</Text>
      <Button 
        title="Edit Profile" 
        onPress={onEditPress} 
        isLoading={isLoading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  username: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  eloRating: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
});
```

#### 2.3 Node.js and Express Standards

**API Structure:**
- RESTful API design principles
- Consistent endpoint naming
- Versioned API routes
- Controller-service-repository pattern
- Input validation middleware

**Error Handling:**
- Centralized error handling middleware
- Consistent error response format
- Appropriate HTTP status codes
- Detailed error messages in development
- Sanitized error messages in production

**Example Controller:**
```typescript
import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { GameService } from '../../services/game/gameService';
import { ApiError } from '../../utils/apiError';

// Input validation schema
const createGameSchema = z.object({
  gameMode: z.enum(['ranked', 'casual', 'wager']),
  timeControl: z.object({
    initial: z.number().min(60).max(3600),
    increment: z.number().min(0).max(60),
  }),
  opponentId: z.string().uuid().optional(),
});

export class GameController {
  private gameService: GameService;
  
  constructor(gameService: GameService) {
    this.gameService = gameService;
  }
  
  createGame = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate input
      const validatedData = createGameSchema.parse(req.body);
      
      // Get user ID from authenticated request
      const userId = req.user.id;
      
      // Call service
      const game = await this.gameService.createGame(
        userId,
        validatedData.gameMode,
        validatedData.timeControl,
        validatedData.opponentId
      );
      
      // Return response
      return res.status(201).json({
        success: true,
        data: {
          gameId: game.id,
          initialState: game.initialState,
        },
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return next(new ApiError(400, 'Invalid input data', error.errors));
      }
      return next(error);
    }
  };
}
```

### 3. Testing Standards

#### 3.1 Testing Approach

**Test Pyramid:**
- Unit tests: 70% coverage
- Integration tests: 20% coverage
- E2E tests: 10% coverage
- Aim for 80% overall test coverage

**Test Types:**
- Unit tests for individual functions and components
- Integration tests for service interactions
- API tests for endpoint functionality
- UI tests for critical user flows
- Performance tests for critical paths

#### 3.2 Test Implementation

**Unit Testing:**
- Use Jest for JavaScript/TypeScript testing
- React Testing Library for component testing
- Mock external dependencies
- Focus on behavior, not implementation
- One assertion per test when possible

**Example Unit Test:**
```typescript
import { calculateEloChange } from '../src/services/game/eloService';

describe('ELO Service', () => {
  describe('calculateEloChange', () => {
    it('should calculate positive ELO change for a win against higher-rated player', () => {
      // Arrange
      const playerRating = 1500;
      const opponentRating = 1700;
      const result = 'win';
      
      // Act
      const eloChange = calculateEloChange(playerRating, opponentRating, result);
      
      // Assert
      expect(eloChange).toBeGreaterThan(20);
    });
    
    it('should calculate negative ELO change for a loss against lower-rated player', () => {
      // Arrange
      const playerRating = 1700;
      const opponentRating = 1500;
      const result = 'loss';
      
      // Act
      const eloChange = calculateEloChange(playerRating, opponentRating, result);
      
      // Assert
      expect(eloChange).toBeLessThan(-20);
    });
    
    it('should calculate small ELO change for a draw between equally rated players', () => {
      // Arrange
      const playerRating = 1600;
      const opponentRating = 1600;
      const result = 'draw';
      
      // Act
      const eloChange = calculateEloChange(playerRating, opponentRating, result);
      
      // Assert
      expect(eloChange).toBe(0);
    });
  });
});
```

**Integration Testing:**
```typescript
import request from 'supertest';
import { app } from '../src/app';
import { createTestUser, generateAuthToken } from './helpers/authHelpers';

describe('Game API', () => {
  let authToken: string;
  let userId: string;
  
  beforeAll(async () => {
    // Create test user and get auth token
    const user = await createTestUser();
    userId = user.id;
    authToken = generateAuthToken(user);
  });
  
  describe('POST /api/games/create', () => {
    it('should create a new game', async () => {
      // Arrange
      const payload = {
        gameMode: 'ranked',
        timeControl: {
          initial: 600,
          increment: 5,
        },
      };
      
      // Act
      const response = await request(app)
        .post('/api/games/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(payload);
      
      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('gameId');
      expect(response.body.data).toHaveProperty('initialState');
    });
    
    it('should return 400 for invalid game mode', async () => {
      // Arrange
      const payload = {
        gameMode: 'invalid_mode',
        timeControl: {
          initial: 600,
          increment: 5,
        },
      };
      
      // Act
      const response = await request(app)
        .post('/api/games/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(payload);
      
      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});
```

### 4. Documentation Standards

#### 4.1 Code Documentation

**Comments and Documentation:**
- Use JSDoc for function and class documentation
- Document complex algorithms and business rules
- Avoid commenting obvious code
- Keep comments up to date with code changes
- Document public APIs thoroughly

**Example JSDoc:**
```typescript
/**
 * Calculates the ELO rating change after a game.
 * 
 * Uses the standard ELO formula with K-factor adjustment based on player rating.
 * K-factor is 32 for ratings below 2100, and 24 for ratings 2100 and above.
 * 
 * @param playerRating - The current ELO rating of the player
 * @param opponentRating - The current ELO rating of the opponent
 * @param result - The game result ('win', 'loss', or 'draw')
 * @returns The ELO rating change (positive or negative integer)
 * 
 * @example
 * // Calculate ELO change for a win against a higher-rated player
 * const eloChange = calculateEloChange(1500, 1700, 'win');
 * // eloChange will be approximately +24
 */
function calculateEloChange(
  playerRating: number,
  opponentRating: number,
  result: 'win' | 'loss' | 'draw'
): number {
  // Implementation...
}
```

#### 4.2 API Documentation

**API Documentation:**
- Use OpenAPI/Swagger for API documentation
- Document all endpoints, parameters, and responses
- Include example requests and responses
- Document error codes and messages
- Keep documentation in sync with implementation

**Example OpenAPI:**
```yaml
paths:
  /api/games/create:
    post:
      summary: Create a new game
      description: Creates a new chess game with specified parameters
      tags:
        - Games
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - gameMode
                - timeControl
              properties:
                gameMode:
                  type: string
                  enum: [ranked, casual, wager]
                  description: The type of game to create
                timeControl:
                  type: object
                  required:
                    - initial
                    - increment
                  properties:
                    initial:
                      type: integer
                      minimum: 60
                      maximum: 3600
                      description: Initial time in seconds
                    increment:
                      type: integer
                      minimum: 0
                      maximum: 60
                      description: Time increment in seconds
                opponentId:
                  type: string
                  format: uuid
                  description: Optional specific opponent ID
      responses:
        '201':
          description: Game created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      gameId:
                        type: string
                        format: uuid
                      initialState:
                        type: object
        '400':
          description: Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
```

### 5. Performance Standards

#### 5.1 Mobile Performance

**Target Metrics:**
- First Meaningful Paint: < 2 seconds
- Time to Interactive: < 3 seconds
- JavaScript bundle size: < 5MB
- Frame rate: 60 FPS for animations
- Memory usage: < 200MB

**Optimization Techniques:**
- Code splitting and lazy loading
- Asset optimization (images, 3D models)
- Memoization of expensive computations
- Virtualized lists for long scrolling content
- Optimized animations using native driver

#### 5.2 API Performance

**Target Metrics:**
- API response time: < 200ms for 95% of requests
- Throughput: 100+ requests per second per instance
- Error rate: < 0.1% of requests
- Cache hit ratio: > 80% for cacheable endpoints

**Optimization Techniques:**
- Database query optimization
- Appropriate indexing strategy
- Response caching with Redis
- Connection pooling
- Horizontal scaling for high-traffic endpoints

### 6. Version Control and CI/CD

#### 6.1 Git Workflow

**Branching Strategy:**
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature branches
- `bugfix/*`: Bug fix branches
- `release/*`: Release preparation branches
- `hotfix/*`: Production hotfix branches

**Commit Standards:**
- Conventional Commits format
- Descriptive commit messages
- Reference issue numbers in commits
- Keep commits focused and atomic
- Squash commits before merging

**Example Commit Messages:**
```
feat(auth): implement wallet-based authentication
fix(game): resolve issue with pawn promotion
chore(deps): update dependencies
docs(api): update API documentation
test(elo): add tests for ELO calculation
```

#### 6.2 CI/CD Pipeline

**Continuous Integration:**
- Run on every pull request
- Lint code for style issues
- Run unit and integration tests
- Check test coverage
- Static code analysis

**Continuous Deployment:**
- Automatic deployment to development environment
- Manual approval for staging and production
- Canary deployments for production
- Automated rollback on failure
- Post-deployment verification tests

**Example GitHub Actions Workflow:**
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Run linting
        run: npm run lint

  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
      - name: Upload coverage
        uses: codecov/codecov-action@v2

  build:
    needs: [lint, test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v2
        with:
          name: build
          path: build/

  deploy-dev:
    if: github.ref == 'refs/heads/develop'
    needs: [build]
    runs-on: ubuntu-latest
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build
          path: build/
      - name: Deploy to development
        run: ./deploy.sh development
```

### 7. Accessibility and Internationalization

#### 7.1 Accessibility Standards

**WCAG Compliance:**
- Target WCAG 2.1 Level AA compliance
- Proper semantic HTML structure
- Keyboard navigation support
- Screen reader compatibility
- Sufficient color contrast

**Implementation Guidelines:**
- Use accessible React Native components
- Implement proper focus management
- Provide text alternatives for non-text content
- Test with screen readers and accessibility tools
- Include accessibility in QA process

#### 7.2 Internationalization

**i18n Approach:**
- Use React-i18next for translations
- Implement right-to-left (RTL) layout support
- Externalize all user-facing strings
- Support for date, time, and number formatting
- Language detection and selection

**Implementation Guidelines:**
- Avoid string concatenation
- Use ICU message format for pluralization
- Implement language switching without app restart
- Test with pseudo-localization
- Include translation context for translators

## Mobile App Store Optimizations

**Note:** Detailed mobile app store optimizations are presented in a dedicated section after the appendices to enhance app store approval likelihood, user testing procedures, and platform-specific optimizations.

## Appendices

### Appendix A: Database Schema Details

**Note:** Additional tables for beta testing and error logging have been added as part of the Mobile App Store Optimizations. These tables are documented in the Mobile App Store Optimizations section.

This appendix provides comprehensive details on the database schema for the Everchess platform, including table relationships, indexes, and constraints.

#### A.1 Core Schema Diagram

```
┌───────────────┐       ┌────────────────┐       ┌────────────────┐
│    users      │       │ user_profiles  │       │wallet_connections│
├───────────────┤       ├────────────────┤       ├────────────────┤
│ id            │───┐   │ user_id        │◄──────│ user_id        │
│ email         │   └──►│ display_name   │       │ wallet_address │
│ username      │       │ avatar_url     │       │ wallet_type    │
│ password_hash │       │ bio            │       │ is_primary     │
│ auth_provider │       │ country_code   │       └────────────────┘
└───────────────┘       │ elo_rating     │
                        └────────────────┘
                                │
                                ▼
┌───────────────┐       ┌────────────────┐       ┌────────────────┐
│ game_states   │       │ user_balance   │       │user_progression│
├───────────────┤       ├────────────────┤       ├────────────────┤
│ id            │       │ user_id        │       │ user_id        │
│ white_player_id│◄─────│ evc_balance    │       │ total_xp       │
│ black_player_id│◄─┐   │ created_at     │       │ current_level  │
│ current_fen   │   │   └────────────────┘       └────────────────┘
│ move_history  │   │                                    │
│ game_status   │   │                                    ▼
└───────────────┘   │   ┌────────────────┐       ┌────────────────┐
        │           │   │ user_inventory │       │ user_missions  │
        ▼           │   ├────────────────┤       ├────────────────┤
┌───────────────┐   │   │ id             │       │ id             │
│ game_results  │   │   │ user_id        │       │ user_id        │
├───────────────┤   │   │ item_type      │       │ mission_type   │
│ id            │   │   │ item_id        │       │ objective_type │
│ game_id       │   │   │ is_equipped    │       │ required_count │
│ winner_id     │───┘   └────────────────┘       │ current_progress│
│ result_type   │                                └────────────────┘
└───────────────┘
```

#### A.2 Table Definitions

##### Users and Authentication

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  auth_provider VARCHAR(20),
  auth_provider_id VARCHAR(255),
  email_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_auth_provider_id ON users(auth_provider, auth_provider_id);

-- User profiles table
CREATE TABLE user_profiles (
  user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  display_name VARCHAR(50),
  avatar_url VARCHAR(255),
  bio TEXT,
  country_code VARCHAR(2),
  elo_rating INTEGER DEFAULT 1200,
  games_played INTEGER DEFAULT 0,
  games_won INTEGER DEFAULT 0,
  games_lost INTEGER DEFAULT 0,
  games_drawn INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_profiles_elo ON user_profiles(elo_rating DESC);

-- Wallet connections table
CREATE TABLE wallet_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  wallet_address VARCHAR(255) UNIQUE NOT NULL,
  wallet_type VARCHAR(50) NOT NULL,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_wallet_connections_user_id ON wallet_connections(user_id);
CREATE INDEX idx_wallet_connections_address ON wallet_connections(wallet_address);

-- User sessions table
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  refresh_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_sessions_token ON user_sessions(refresh_token);
CREATE INDEX idx_user_sessions_expiry ON user_sessions(expires_at);
```

##### Game Management

```sql
-- Game states table
CREATE TABLE game_states (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  white_player_id UUID REFERENCES users(id),
  black_player_id UUID REFERENCES users(id),
  current_fen VARCHAR(100) NOT NULL,
  move_history JSONB NOT NULL DEFAULT '[]',
  game_status VARCHAR(20) NOT NULL DEFAULT 'active',
  time_control JSONB NOT NULL,
  game_mode VARCHAR(20) NOT NULL,
  wager_amount INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_game_states_white_player ON game_states(white_player_id);
CREATE INDEX idx_game_states_black_player ON game_states(black_player_id);
CREATE INDEX idx_game_states_status ON game_states(game_status);
CREATE INDEX idx_game_states_created_at ON game_states(created_at DESC);

-- Game results table
CREATE TABLE game_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id) ON DELETE CASCADE,
  winner_id UUID REFERENCES users(id),
  result_type VARCHAR(20) NOT NULL,
  white_elo_change INTEGER,
  black_elo_change INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_game_results_game_id ON game_results(game_id);
CREATE INDEX idx_game_results_winner_id ON game_results(winner_id);

-- Matchmaking queue table
CREATE TABLE matchmaking_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  game_mode VARCHAR(20) NOT NULL,
  elo_rating INTEGER,
  wager_amount INTEGER,
  entry_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  parameters JSONB
);

CREATE INDEX idx_matchmaking_queue_mode_elo ON matchmaking_queue(game_mode, elo_rating);
CREATE INDEX idx_matchmaking_queue_entry_time ON matchmaking_queue(entry_time);

-- Move analysis table
CREATE TABLE move_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  move_number INTEGER NOT NULL,
  from_square VARCHAR(2) NOT NULL,
  to_square VARCHAR(2) NOT NULL,
  promotion VARCHAR(1),
  engine_correlation FLOAT,
  suspicious_score FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_move_analysis_game_id ON move_analysis(game_id);
CREATE INDEX idx_move_analysis_user_id ON move_analysis(user_id);
CREATE INDEX idx_move_analysis_suspicious ON move_analysis(suspicious_score DESC);
```

##### Social Features

```sql
-- Friend relationships table
CREATE TABLE friend_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  friend_id UUID REFERENCES users(id) ON DELETE CASCADE,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  initiated_by UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, friend_id)
);

CREATE INDEX idx_friend_relationships_user_id ON friend_relationships(user_id);
CREATE INDEX idx_friend_relationships_friend_id ON friend_relationships(friend_id);
CREATE INDEX idx_friend_relationships_status ON friend_relationships(status);

-- Chat messages table
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  game_id UUID REFERENCES game_states(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  message_type VARCHAR(20) NOT NULL DEFAULT 'TEXT',
  content TEXT NOT NULL,
  is_moderated BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_chat_messages_game_id ON chat_messages(game_id);
CREATE INDEX idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

-- Chat reports table
CREATE TABLE chat_reports (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID REFERENCES chat_messages(id) ON DELETE CASCADE,
  reporter_id UUID REFERENCES users(id),
  reason VARCHAR(50) NOT NULL,
  details TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_chat_reports_message_id ON chat_reports(message_id);
CREATE INDEX idx_chat_reports_status ON chat_reports(status);
```

##### Economy and Inventory

```sql
-- User balance table
CREATE TABLE user_balance (
  user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  evc_balance INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- EVC transactions table
CREATE TABLE evc_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  reference_id UUID,
  reference_type VARCHAR(50),
  balance_after INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_evc_transactions_user_id ON evc_transactions(user_id);
CREATE INDEX idx_evc_transactions_created_at ON evc_transactions(created_at DESC);
CREATE INDEX idx_evc_transactions_type ON evc_transactions(transaction_type);

-- Purchase transactions table
CREATE TABLE purchase_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  package_id VARCHAR(50) NOT NULL,
  evc_amount INTEGER NOT NULL,
  price_usd NUMERIC(10, 2) NOT NULL,
  payment_provider VARCHAR(50) NOT NULL,
  payment_id VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_purchase_transactions_user_id ON purchase_transactions(user_id);
CREATE INDEX idx_purchase_transactions_status ON purchase_transactions(status);

-- Store items table
CREATE TABLE store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_type VARCHAR(50) NOT NULL,
  reference_id UUID NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price_evc INTEGER NOT NULL,
  is_featured BOOLEAN DEFAULT FALSE,
  is_limited BOOLEAN DEFAULT FALSE,
  available_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_store_items_type ON store_items(item_type);
CREATE INDEX idx_store_items_featured ON store_items(is_featured);
CREATE INDEX idx_store_items_available ON store_items(available_until);

-- User inventory table
CREATE TABLE user_inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  item_type VARCHAR(50) NOT NULL,
  item_id UUID NOT NULL,
  acquisition_type VARCHAR(50) NOT NULL,
  acquisition_reference UUID,
  is_equipped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type, item_id)
);

CREATE INDEX idx_user_inventory_user_id ON user_inventory(user_id);
CREATE INDEX idx_user_inventory_equipped ON user_inventory(user_id, item_type) WHERE is_equipped = TRUE;
```

##### Progression System

```sql
-- User progression table
CREATE TABLE user_progression (
  user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  total_xp INTEGER NOT NULL DEFAULT 0,
  current_level INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- XP transactions table
CREATE TABLE xp_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  source VARCHAR(50) NOT NULL,
  reference_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_xp_transactions_user_id ON xp_transactions(user_id);
CREATE INDEX idx_xp_transactions_created_at ON xp_transactions(created_at DESC);

-- User missions table
CREATE TABLE user_missions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  mission_type VARCHAR(10) NOT NULL,
  objective_type VARCHAR(50) NOT NULL,
  required_count INTEGER NOT NULL,
  current_progress INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
  xp_reward INTEGER NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_missions_user_id ON user_missions(user_id);
CREATE INDEX idx_user_missions_expires_at ON user_missions(expires_at);
CREATE INDEX idx_user_missions_completed ON user_missions(user_id, is_completed, is_claimed);

-- Battlepass seasons table
CREATE TABLE battlepass_seasons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  premium_price INTEGER NOT NULL,
  total_tiers INTEGER NOT NULL DEFAULT 50,
  is_active BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_battlepass_seasons_active ON battlepass_seasons(is_active);
CREATE INDEX idx_battlepass_seasons_dates ON battlepass_seasons(start_date, end_date);

-- Battlepass rewards table
CREATE TABLE battlepass_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  season_id UUID REFERENCES battlepass_seasons(id) ON DELETE CASCADE,
  tier INTEGER NOT NULL,
  reward_type VARCHAR(50) NOT NULL,
  reward_id UUID NOT NULL,
  is_premium BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_battlepass_rewards_season_id ON battlepass_rewards(season_id);
CREATE INDEX idx_battlepass_rewards_tier ON battlepass_rewards(season_id, tier);

-- User battlepass table
CREATE TABLE user_battlepass (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  season_id UUID REFERENCES battlepass_seasons(id) ON DELETE CASCADE,
  has_premium BOOLEAN DEFAULT FALSE,
  current_tier INTEGER DEFAULT 0,
  current_xp INTEGER DEFAULT 0,
  claimed_rewards JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, season_id)
);

CREATE INDEX idx_user_battlepass_user_id ON user_battlepass(user_id);
CREATE INDEX idx_user_battlepass_season_id ON user_battlepass(season_id);
```

##### NFT and Blockchain

```sql
-- NFT chess sets table
CREATE TABLE nft_chess_sets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  nft_id INTEGER NOT NULL,
  mint_address VARCHAR(255) UNIQUE NOT NULL,
  metadata_uri VARCHAR(255) NOT NULL,
  owner_wallet VARCHAR(255),
  owner_user_id UUID REFERENCES users(id),
  is_staked BOOLEAN DEFAULT FALSE,
  staking_start_time TIMESTAMP WITH TIME ZONE,
  attributes JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_nft_chess_sets_owner_wallet ON nft_chess_sets(owner_wallet);
CREATE INDEX idx_nft_chess_sets_owner_user_id ON nft_chess_sets(owner_user_id);
CREATE INDEX idx_nft_chess_sets_staked ON nft_chess_sets(is_staked);

-- Staking transactions table
CREATE TABLE staking_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  nft_id UUID REFERENCES nft_chess_sets(id) ON DELETE CASCADE,
  transaction_type VARCHAR(20) NOT NULL,
  transaction_hash VARCHAR(255) NOT NULL,
  staking_period INTEGER,
  rewards_amount INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_staking_transactions_user_id ON staking_transactions(user_id);
CREATE INDEX idx_staking_transactions_nft_id ON staking_transactions(nft_id);

-- Blockchain transactions table
CREATE TABLE blockchain_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  transaction_hash VARCHAR(255) NOT NULL,
  transaction_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  reference_id UUID,
  reference_type VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_blockchain_transactions_user_id ON blockchain_transactions(user_id);
CREATE INDEX idx_blockchain_transactions_hash ON blockchain_transactions(transaction_hash);
CREATE INDEX idx_blockchain_transactions_status ON blockchain_transactions(status);
```

### Appendix B: API Endpoints Reference

This appendix provides a comprehensive reference of all API endpoints in the Everchess platform, including request parameters, response formats, and authentication requirements.

#### B.1 Authentication API

##### B.1.1 User Registration

```
POST /api/auth/register
```

**Description:** Register a new user account

**Authentication Required:** No

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "chessmaster",
  "password": "SecurePassword123!"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "chessmaster",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid input data
- 409 Conflict: Username or email already exists

##### B.1.2 User Login

```
POST /api/auth/login
```

**Description:** Authenticate a user and get access token

**Authentication Required:** No

**Request Body:**
```json
{
  "username": "chessmaster",
  "password": "SecurePassword123!"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "chessmaster",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid credentials
- 401 Unauthorized: Account locked or inactive

##### B.1.3 Wallet Authentication

```
POST /api/auth/wallet/challenge
```

**Description:** Get a challenge message for wallet authentication

**Authentication Required:** No

**Request Body:**
```json
{
  "walletAddress": "5FHwkrdxkRXbfJkRWqGdo8VZ88WNdxvJxzXPPPPPPPP"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "message": "Sign this message to authenticate with Everchess: NONCE_12345",
    "nonce": "NONCE_12345"
  }
}
```

```
POST /api/auth/wallet/verify
```

**Description:** Verify wallet signature and authenticate

**Authentication Required:** No

**Request Body:**
```json
{
  "walletAddress": "5FHwkrdxkRXbfJkRWqGdo8VZ88WNdxvJxzXPPPPPPPP",
  "signature": "0x1a2b3c4d...",
  "nonce": "NONCE_12345"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "chessmaster",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "isNewUser": false
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid signature
- 401 Unauthorized: Invalid nonce or expired challenge

##### B.1.4 Token Refresh

```
POST /api/auth/refresh
```

**Description:** Get a new access token using refresh token

**Authentication Required:** No

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**Error Responses:**
- 401 Unauthorized: Invalid or expired refresh token

#### B.2 User API

##### B.2.1 Get User Profile

```
GET /api/users/profile
```

**Description:** Get the current user's profile

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "chessmaster",
    "displayName": "Chess Master",
    "avatarUrl": "https://everchess.io/avatars/default.png",
    "bio": "Chess enthusiast from New York",
    "countryCode": "US",
    "eloRating": 1250,
    "level": 5,
    "totalXp": 2500,
    "gamesPlayed": 25,
    "gamesWon": 15,
    "gamesLost": 8,
    "gamesDrawn": 2,
    "createdAt": "2023-01-15T12:00:00Z"
  }
}
```

##### B.2.2 Update User Profile

```
PUT /api/users/profile
```

**Description:** Update the current user's profile

**Authentication Required:** Yes

**Request Body:**
```json
{
  "displayName": "Chess Grandmaster",
  "bio": "Chess enthusiast from New York City",
  "countryCode": "US",
  "avatarUrl": "https://everchess.io/avatars/custom.png"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "displayName": "Chess Grandmaster",
    "bio": "Chess enthusiast from New York City",
    "countryCode": "US",
    "avatarUrl": "https://everchess.io/avatars/custom.png",
    "updatedAt": "2023-01-20T14:30:00Z"
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid input data

##### B.2.3 Get User Stats

```
GET /api/users/stats
```

**Description:** Get detailed statistics for the current user

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "eloRating": 1250,
    "highestElo": 1300,
    "gamesPlayed": 25,
    "winRate": 0.6,
    "averageGameLength": 35,
    "favoriteOpenings": [
      {
        "name": "Sicilian Defense",
        "count": 8,
        "winRate": 0.75
      },
      {
        "name": "Queen's Gambit",
        "count": 5,
        "winRate": 0.6
      }
    ],
    "recentPerformance": {
      "last10Games": [
        {"result": "win", "opponentElo": 1230, "eloChange": 8},
        {"result": "loss", "opponentElo": 1300, "eloChange": -10}
      ]
    }
  }
}
```

##### B.2.4 Get User by ID

```
GET /api/users/:userId
```

**Description:** Get public profile information for a specific user

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "chessmaster",
    "displayName": "Chess Master",
    "avatarUrl": "https://everchess.io/avatars/default.png",
    "countryCode": "US",
    "eloRating": 1250,
    "level": 5,
    "gamesPlayed": 25,
    "winRate": 0.6,
    "isFriend": false,
    "friendStatus": null
  }
}
```

**Error Responses:**
- 404 Not Found: User not found

#### B.3 Game API

##### B.3.1 Create Game

```
POST /api/games/create
```

**Description:** Create a new chess game

**Authentication Required:** Yes

**Request Body:**
```json
{
  "gameMode": "ranked",
  "timeControl": {
    "initial": 600,
    "increment": 5
  },
  "opponentId": "550e8400-e29b-41d4-a716-************"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "whitePlayerId": "550e8400-e29b-41d4-a716-************",
    "blackPlayerId": "660f9500-f30c-52e5-b827-557766550000",
    "initialState": {
      "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "timeControl": {
        "initial": 600,
        "increment": 5
      },
      "gameMode": "ranked"
    },
    "createdAt": "2023-01-25T18:30:00Z"
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid input data
- 404 Not Found: Opponent not found

##### B.3.2 Join Matchmaking Queue

```
POST /api/games/matchmaking/join
```

**Description:** Join the matchmaking queue to find an opponent

**Authentication Required:** Yes

**Request Body:**
```json
{
  "gameMode": "ranked",
  "timeControl": {
    "initial": 600,
    "increment": 5
  },
  "eloRange": 100
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "queueId": "8e9f0a1b-c2d3-4e5f-a6b7-************",
    "estimatedWaitTime": 30,
    "queuePosition": 3,
    "parameters": {
      "gameMode": "ranked",
      "timeControl": {
        "initial": 600,
        "increment": 5
      },
      "eloRange": 100
    }
  }
}
```

##### B.3.3 Leave Matchmaking Queue

```
DELETE /api/games/matchmaking/leave
```

**Description:** Leave the matchmaking queue

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "message": "Successfully left the matchmaking queue"
  }
}
```

##### B.3.4 Get Game State

```
GET /api/games/:gameId
```

**Description:** Get the current state of a game

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "whitePlayer": {
      "userId": "550e8400-e29b-41d4-a716-************",
      "username": "chessmaster",
      "eloRating": 1250
    },
    "blackPlayer": {
      "userId": "660f9500-f30c-52e5-b827-557766550000",
      "username": "chesswizard",
      "eloRating": 1280
    },
    "currentFen": "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
    "moveHistory": [
      {
        "from": "e2",
        "to": "e4",
        "piece": "p",
        "color": "w",
        "timestamp": "2023-01-25T18:31:00Z"
      },
      {
        "from": "e7",
        "to": "e5",
        "piece": "p",
        "color": "b",
        "timestamp": "2023-01-25T18:31:30Z"
      }
    ],
    "gameStatus": "active",
    "timeControl": {
      "initial": 600,
      "increment": 5
    },
    "timeLeft": {
      "white": 595,
      "black": 595
    },
    "gameMode": "ranked",
    "isPlayerTurn": true,
    "lastMoveAt": "2023-01-25T18:31:30Z"
  }
}
```

**Error Responses:**
- 404 Not Found: Game not found
- 403 Forbidden: Not a participant in the game

##### B.3.5 Make Move

```
POST /api/games/:gameId/move
```

**Description:** Make a move in a chess game

**Authentication Required:** Yes

**Request Body:**
```json
{
  "from": "d2",
  "to": "d4",
  "promotion": null
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "moveAccepted": true,
    "newFen": "rnbqkbnr/pppp1ppp/8/4p3/3PP3/8/PPP2PPP/RNBQKBNR b KQkq d3 0 2",
    "isCheck": false,
    "isCheckmate": false,
    "isDraw": false,
    "timeLeft": {
      "white": 597,
      "black": 595
    }
  }
}
```

**Error Responses:**
- 400 Bad Request: Invalid move
- 403 Forbidden: Not your turn
- 404 Not Found: Game not found

##### B.3.6 Resign Game

```
POST /api/games/:gameId/resign
```

**Description:** Resign from a game

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "result": "resignation",
    "winner": "660f9500-f30c-52e5-b827-557766550000",
    "eloChange": -12
  }
}
```

**Error Responses:**
- 404 Not Found: Game not found
- 403 Forbidden: Not a participant in the game
- 400 Bad Request: Game already finished

##### B.3.7 Offer Draw

```
POST /api/games/:gameId/offer-draw
```

**Description:** Offer a draw to the opponent

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "drawOffered": true,
    "expiresAt": "2023-01-25T18:35:00Z"
  }
}
```

**Error Responses:**
- 404 Not Found: Game not found
- 403 Forbidden: Not a participant in the game
- 400 Bad Request: Draw already offered or game finished

##### B.3.8 Respond to Draw Offer

```
POST /api/games/:gameId/respond-draw
```

**Description:** Accept or decline a draw offer

**Authentication Required:** Yes

**Request Body:**
```json
{
  "accept": true
}
```

**Response (200 OK) - If Accepted:**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "result": "draw",
    "eloChange": 2
  }
}
```

**Response (200 OK) - If Declined:**
```json
{
  "success": true,
  "data": {
    "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
    "drawDeclined": true
  }
}
```

**Error Responses:**
- 404 Not Found: Game not found or no draw offer
- 403 Forbidden: Not the recipient of the draw offer

##### B.3.9 Get Recent Games

```
GET /api/games/recent
```

**Description:** Get a list of recent games for the current user

**Authentication Required:** Yes

**Query Parameters:**
- `limit`: Maximum number of games to return (default: 10)
- `offset`: Number of games to skip (default: 0)
- `status`: Filter by game status (active, completed, all)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "games": [
      {
        "gameId": "7f8d9e0a-b1c2-4d3e-a4f5-************",
        "opponent": {
          "userId": "660f9500-f30c-52e5-b827-557766550000",
          "username": "chesswizard",
          "eloRating": 1280
        },
        "playerColor": "white",
        "result": "win",
        "eloChange": 10,
        "gameMode": "ranked",
        "createdAt": "2023-01-25T18:30:00Z",
        "completedAt": "2023-01-25T19:15:00Z"
      }
    ],
    "total": 25,
    "limit": 10,
    "offset": 0
  }
}
```

#### B.4 Economy API

##### B.4.1 Get User Balance

```
GET /api/economy/balance
```

**Description:** Get the current user's EVC balance

**Authentication Required:** Yes

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "evcBalance": 1500,
    "lastUpdated": "2023-01-30T12:00:00Z"
  }
}
```

##### B.4.2 Get Transaction History

```
GET /api/economy/transactions
```

**Description:** Get the transaction history for the current user

**Authentication Required:** Yes

**Query Parameters:**
- `limit`: Maximum number of transactions to return (default: 20)
- `offset`: Number of transactions to skip (default: 0)
- `type`: Filter by transaction type (purchase, reward, wager, etc.)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "transactionId": "9a0b1c2d-3e4f-5a6b-7c8d-************",
        "amount": 500,
        "transactionType": "purchase",
        "referenceId": "order-123456",
        "balanceAfter": 1500,
        "createdAt": "2023-01-30T10:15:00Z"
      },
      {
        "transactionId": "a1b2c3d4-e5f6-7a8b-9c0d-************",
        "amount": 50,
        "transactionType": "reward",
        "referenceId": "mission-daily-1",
        "balanceAfter": 1000,
        "createdAt": "2023-01-29T14:30:00Z"
      }
    ],
    "total": 15,
    "limit": 20,
    "offset": 0
  }
}
```

##### B.4.3 Get Store Items

```
GET /api/economy/store
```

**Description:** Get available items in the store

**Authentication Required:** Yes

**Query Parameters:**
- `category`: Filter by item category (chesssets, avatars, emotes, etc.)
- `featured`: Filter featured items only (true/false)
- `limit`: Maximum number of items to return (default: 20)
- `offset`: Number of items to skip (default: 0)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "itemId": "b2c3d4e5-f6a7-8b9c-0d1e-************",
        "itemType": "chessset",
        "name": "Royal Gold Set",
        "description": "Luxurious gold-plated chess pieces",
        "priceEvc": 2000,
        "imageUrl": "https://everchess.io/store/chesssets/royal-gold.png",
        "isFeatured": true,
        "isLimited": true,
        "availableUntil": "2023-03-01T00:00:00Z"
      },
      {
        "itemId": "c3d4e5f6-a7b8-9c0d-1e2f-************",
        "itemType": "avatar",
        "name": "Grandmaster Avatar",
        "description": "Show your chess mastery",
        "priceEvc": 500,
        "imageUrl": "https://everchess.io/store/avatars/grandmaster.png",
        "isFeatured": false,
        "isLimited": false
      }
    ],
    "total": 45,
    "limit": 20,
    "offset": 0
  }
}
```

##### B.4.4 Purchase Item

```
POST /api/economy/purchase
```

**Description:** Purchase an item from the store

**Authentication Required:** Yes

**Request Body:**
```json
{
  "itemId": "b2c3d4e5-f6a7-8b9c-0d1e-************"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "transactionId": "d4e5f6a7-b8c9-0d1e-2f3a-************",
    "itemId": "b2c3d4e5-f6a7-8b9c-0d1e-************",
    "itemName": "Royal Gold Set",
    "pricePaid": 2000,
    "newBalance": 3500,
    "inventoryId": "e5f6a7b8-c9d0-1e2f-3a4b-************"
  }
}
```

**Error Responses:**
- 400 Bad Request: Insufficient balance
- 404 Not Found: Item not found
- 409 Conflict: Item already owned

##### B.4.5 Get User Inventory

```
GET /api/economy/inventory
```

**Description:** Get the current user's inventory

**Authentication Required:** Yes

**Query Parameters:**
- `itemType`: Filter by item type (chesssets, avatars, emotes, etc.)
- `limit`: Maximum number of items to return (default: 50)
- `offset`: Number of items to skip (default: 0)

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "inventoryId": "e5f6a7b8-c9d0-1e2f-3a4b-************",
        "itemId": "b2c3d4e5-f6a7-8b9c-0d1e-************",
        "itemType": "chessset",
        "name": "Royal Gold Set",
        "description": "Luxurious gold-plated chess pieces",
        "imageUrl": "https://everchess.io/store/chesssets/royal-gold.png",
        "isEquipped": true,
        "acquiredAt": "2023-01-30T15:45:00Z"
      },
      {
        "inventoryId": "f6a7b8c9-d0e1-2f3a-4b5c-************",
        "itemId": "c3d4e5f6-a7b8-9c0d-1e2f-************",
        "itemType": "avatar",
        "name": "Grandmaster Avatar",
        "description": "Show your chess mastery",
        "imageUrl": "https://everchess.io/store/avatars/grandmaster.png",
        "isEquipped": false,
        "acquiredAt": "2023-01-28T09:20:00Z"
      }
    ],
    "total": 12,
    "limit": 50,
    "offset": 0
  }
}
```

##### B.4.6 Equip Item

```
POST /api/economy/inventory/equip
```

**Description:** Equip an item from the user's inventory

**Authentication Required:** Yes

**Request Body:**
```json
{
  "inventoryId": "f6a7b8c9-d0e1-2f3a-4b5c-************",
  "itemType": "avatar"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "inventoryId": "f6a7b8c9-d0e1-2f3a-4b5c-************",
    "itemType": "avatar",
    "name": "Grandmaster Avatar",
    "isEquipped": true,
    "previouslyEquipped": {
      "inventoryId": "a7b8c9d0-e1f2-3a4b-5c6d-************",
      "itemType": "avatar",
      "name": "Default Avatar",
      "isEquipped": false
    }
  }
}
```

**Error Responses:**
- 404 Not Found: Item not found in inventory
- 400 Bad Request: Invalid item type

### Appendix C: Game Logic and Chess Rules Implementation

This appendix provides detailed information on the implementation of chess rules and game logic in the Everchess platform.

#### C.1 Chess Rules Implementation

##### C.1.1 Basic Movement Rules

The following table outlines the movement patterns for each chess piece:

| Piece | Movement Pattern |
|-------|-----------------|
| Pawn | Moves forward one square, with the option to move two squares on its first move. Captures diagonally forward one square. Can be promoted to any other piece (except a king) upon reaching the eighth rank. |
| Rook | Moves any number of squares along a rank or file. Cannot jump over other pieces. |
| Knight | Moves in an 'L' shape: two squares in one direction and then one square perpendicular to that direction. Can jump over other pieces. |
| Bishop | Moves any number of squares diagonally. Cannot jump over other pieces. |
| Queen | Combines the power of the rook and bishop, moving any number of squares along a rank, file, or diagonal. Cannot jump over other pieces. |
| King | Moves one square in any direction. Cannot move to a square under attack by an enemy piece. |

##### C.1.2 Special Move Rules

**Castling:**
- The king moves two squares toward a rook, and the rook moves to the square the king crossed.
- Requirements:
  - Neither the king nor the rook has previously moved
  - There are no pieces between the king and the rook
  - The king is not in check
  - The king does not pass through or finish on a square that is attacked by an enemy piece

**En Passant:**
- If a pawn moves two squares forward from its starting position and lands beside an opponent's pawn, the opponent's pawn can capture it as if it had moved only one square forward.
- This capture must be made immediately after the two-square advance, or the right to do so is lost.

**Pawn Promotion:**
- When a pawn reaches the eighth rank, it must be exchanged for a queen, rook, bishop, or knight of the same color.
- The choice is not limited to pieces that have been captured.

##### C.1.3 Game End Conditions

**Checkmate:**
- A player's king is in check and there is no legal move to escape the check.
- The player who delivers checkmate wins the game.

**Stalemate:**
- A player has no legal moves, but their king is not in check.
- The game is a draw.

**Draw by Insufficient Material:**
- Neither player has enough pieces to force a checkmate.
- Common insufficient material scenarios:
  - King vs. King
  - King and Bishop vs. King
  - King and Knight vs. King
  - King and Bishop vs. King and Bishop (bishops on same-colored squares)

**Draw by Threefold Repetition:**
- The same position occurs three times with the same player to move.
- Either player can claim a draw when this occurs.

**Draw by the Fifty-Move Rule:**
- No capture or pawn move has occurred in the last 50 consecutive moves by each player.
- Either player can claim a draw when this occurs.

**Draw by Agreement:**
- Both players agree to a draw.

**Resignation:**
- A player voluntarily concedes the game.

#### C.2 Game Logic Implementation

##### C.2.1 Move Validation Algorithm

The move validation process follows these steps:

1. **Basic Validation:**
   - Check if the move is within the board boundaries
   - Verify that the piece exists at the starting square
   - Confirm the piece belongs to the player whose turn it is

2. **Movement Pattern Validation:**
   - Verify the move follows the correct pattern for the piece type
   - Check if the path is clear for pieces that cannot jump (all except knights)
   - For pawns, validate special rules (first move, diagonal capture, en passant)

3. **Special Move Validation:**
   - For castling, verify all conditions are met
   - For en passant, verify the capture is valid and timely
   - For pawn promotion, ensure the pawn has reached the eighth rank

4. **Check Validation:**
   - Verify the move doesn't leave or put the player's king in check
   - For castling, verify the king doesn't move through or to a square under attack

5. **Post-Move State Update:**
   - Update the board state with the new piece positions
   - Handle captures, removing captured pieces
   - Process special moves (castling, en passant, promotion)
   - Update game metadata (move count, half-move clock, etc.)

##### C.2.2 FEN Notation

The Forsyth-Edwards Notation (FEN) is used to represent the board state. A FEN string consists of six fields separated by spaces:

1. **Piece Placement:** Describes the piece placement from rank 8 to 1, with each rank separated by a slash. Numbers represent consecutive empty squares.
2. **Active Color:** 'w' for White to move, 'b' for Black to move.
3. **Castling Availability:** Letters indicate castling rights (K, Q, k, q). A dash (-) means no castling rights.
4. **En Passant Target Square:** If a pawn has just made a two-square move, this is the square behind it. Otherwise, it's a dash (-).
5. **Halfmove Clock:** The number of halfmoves since the last pawn advance or capture.
6. **Fullmove Number:** The number of full moves. It starts at 1 and is incremented after Black's move.

Example FEN for the starting position:
```
rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
```

##### C.2.3 Move History Format

Move history is stored in JSON format with the following structure:

```json
{
  "moves": [
    {
      "moveNumber": 1,
      "white": {
        "from": "e2",
        "to": "e4",
        "piece": "p",
        "capturedPiece": null,
        "isCheck": false,
        "isCheckmate": false,
        "isPromotion": false,
        "promotedTo": null,
        "isCastle": false,
        "isEnPassant": false,
        "timeLeft": 595,
        "timestamp": "2023-01-25T18:31:00Z",
        "fen": "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
      },
      "black": {
        "from": "e7",
        "to": "e5",
        "piece": "p",
        "capturedPiece": null,
        "isCheck": false,
        "isCheckmate": false,
        "isPromotion": false,
        "promotedTo": null,
        "isCastle": false,
        "isEnPassant": false,
        "timeLeft": 590,
        "timestamp": "2023-01-25T18:31:30Z",
        "fen": "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2"
      }
    }
  ]
}
```

##### C.2.4 Time Control Implementation

Time control is implemented with the following features:

1. **Initial Time:** The starting time allocated to each player (in seconds).
2. **Increment:** Time added to a player's clock after completing a move (in seconds).
3. **Time Management:**
   - Time decrements only during a player's turn.
   - After making a move, the increment is added to the player's remaining time.
   - Time is tracked with millisecond precision but displayed in seconds.
4. **Time Forfeit:**
   - If a player's time reaches zero, they lose the game by timeout.
   - A 0.5-second grace period is provided to account for network latency.
5. **Low Time Notification:**
   - Players receive notifications when their time falls below certain thresholds (60s, 30s, 10s).

#### C.3 Anti-Cheating Mechanisms

##### C.3.1 Move Validation System

All moves are validated on the server side to prevent illegal moves or client-side manipulation. The validation process includes:

1. **Rule Compliance:** Ensuring moves follow standard chess rules.
2. **Turn Order:** Verifying the correct player is making the move.
3. **Timing Verification:** Checking that moves are made within the player's time limit.
4. **State Consistency:** Validating that the client's game state matches the server's state.

##### C.3.2 Engine Detection

The system employs multiple methods to detect potential engine use:

1. **Move Time Analysis:**
   - Tracking the time taken for each move.
   - Flagging suspicious patterns, such as consistently quick complex moves.
   - Analyzing time usage variance across different game phases.

2. **Move Quality Assessment:**
   - Comparing player moves with engine recommendations.
   - Calculating the correlation between player moves and top engine lines.
   - Tracking the accuracy of moves in critical positions.

3. **Statistical Analysis:**
   - Building player profiles based on historical play patterns.
   - Detecting significant deviations from established patterns.
   - Comparing performance across different time controls.

4. **Behavioral Monitoring:**
   - Tracking mouse movements and click patterns.
   - Monitoring tab switching and window focus events.
   - Detecting suspicious browser extensions or scripts.

##### C.3.3 Manual Review Process

Suspicious games flagged by the automated system undergo manual review:

1. **Review Triggers:**
   - High engine correlation score (>90% over 20+ moves).
   - Multiple reports from other players.
   - Significant rating gain in a short period.
   - Unusual play patterns detected by the system.

2. **Review Process:**
   - Experienced chess players review flagged games.
   - Multiple reviewers assess each case independently.
   - Consideration of player history and context.
   - Decision based on preponderance of evidence.

3. **Actions Taken:**
   - Warning for first minor offense.
   - Temporary suspension for clear violations.
   - Permanent ban for repeated or severe violations.
   - Rating adjustment for affected opponents.

### Appendix D: Asset Specifications

This appendix provides detailed specifications for the visual and audio assets used in the Everchess platform.

#### D.1 3D Chess Pieces

##### D.1.1 Standard Chess Set Specifications

**Polygon Count Guidelines:**
- King: 3,000-5,000 polygons
- Queen: 2,500-4,500 polygons
- Rook: 1,500-3,000 polygons
- Bishop: 2,000-3,500 polygons
- Knight: 2,500-4,000 polygons
- Pawn: 1,000-2,500 polygons

**Texture Resolutions:**
- Diffuse Maps: 1024x1024 pixels
- Normal Maps: 1024x1024 pixels
- Metallic/Roughness Maps: 1024x1024 pixels
- Ambient Occlusion Maps: 1024x1024 pixels

**Material Properties:**
- Base Color: RGB values defined per set
- Metallic: 0.0-1.0 (varies by material)
- Roughness: 0.2-0.8 (varies by material)
- Normal Intensity: 0.5-1.0

**File Formats:**
- 3D Models: glTF 2.0 (.glb)
- Textures: PNG (diffuse, normal, AO) and JPG (roughness, metallic)

##### D.1.2 NFT Chess Set Requirements

**Unique Visual Elements:**
- Minimum of 5 unique visual features per set
- Custom materials and effects
- Distinctive silhouettes for each piece
- Thematic consistency across the set

**Technical Requirements:**
- Higher polygon count allowed (up to 50% more than standard)
- Additional texture maps for special effects
- Support for animated elements (glow, particle effects)
- Optimized for mobile rendering

**Rarity Tiers:**
- Common: Basic unique designs
- Rare: Enhanced materials and effects
- Epic: Animated elements and special effects
- Legendary: Complex animations and unique interactions

#### D.2 Chessboard Designs

##### D.2.1 Standard Chessboard Specifications

**Board Dimensions:**
- 8x8 grid
- Square size: 1 unit x 1 unit
- Board thickness: 0.2 units
- Border width: 0.5 units

**Texture Resolutions:**
- Diffuse Map: 2048x2048 pixels
- Normal Map: 2048x2048 pixels
- Roughness Map: 1024x1024 pixels

**Material Properties:**
- Light Squares: RGB defined per board theme
- Dark Squares: RGB defined per board theme
- Border: Complementary to square colors
- Roughness: 0.3-0.7 (varies by material)

**File Formats:**
- 3D Model: glTF 2.0 (.glb)
- Textures: PNG and JPG

##### D.2.2 Environment Settings

**Lighting:**
- Main Directional Light: Color temperature 5500K, intensity 1.0
- Fill Light: Color temperature 6500K, intensity 0.5
- Rim Light: Color temperature 4500K, intensity 0.3

**Ambient Settings:**
- Ambient Intensity: 0.2
- Ambient Color: Varies by environment theme
- Environment Map: 1024x1024 HDR cubemap

**Post-Processing:**
- Ambient Occlusion: Medium intensity
- Bloom: Subtle effect for highlights
- Color Grading: Varies by environment theme

#### D.3 UI Elements

##### D.3.1 Game Interface Elements

**Button Specifications:**
- Primary Buttons: 160x60 pixels, rounded corners (8px radius)
- Secondary Buttons: 140x50 pixels, rounded corners (6px radius)
- Icon Buttons: 48x48 pixels, circular or square with rounded corners

**Color Palette:**
- Primary: #3A7BF2 (Blue)
- Secondary: #2C3E50 (Dark Blue)
- Accent: #F39C12 (Orange)
- Success: #2ECC71 (Green)
- Warning: #F1C40F (Yellow)
- Danger: #E74C3C (Red)
- Background: #F5F7FA (Light Gray)
- Text: #333333 (Dark Gray)

**Typography:**
- Headings: Montserrat Bold, sizes 24px, 20px, 18px
- Body Text: Open Sans Regular, sizes 16px, 14px, 12px
- Button Text: Open Sans SemiBold, size 16px
- Game Info: Roboto Mono, sizes 14px, 12px

**Icon Set:**
- Line weight: 2px
- Corner radius: 2px
- Size: 24x24 pixels (standard), 16x16 pixels (small)
- Format: SVG

##### D.3.2 HUD Elements

**Move List:**
- Width: 240px
- Height: Variable (scrollable)
- Background: Semi-transparent (#FFFFFF with 90% opacity)
- Border: 1px solid #DDDDDD
- Text: 14px Roboto Mono

**Clock Display:**
- Width: 120px
- Height: 60px
- Background: Semi-transparent (#333333 with 80% opacity)
- Text: 24px Roboto Mono, #FFFFFF
- Low Time Indicator: Pulsing red background when under 30 seconds

**Player Info Panel:**
- Width: 240px
- Height: 80px
- Background: Semi-transparent (#FFFFFF with 90% opacity)
- Avatar Size: 60x60 pixels
- Username: 16px Open Sans Bold
- Rating: 14px Open Sans Regular

#### D.4 Audio Assets

##### D.4.1 Sound Effects

**Game Sounds:**
- Piece Movement: 0.5-1.0 second WAV files, 44.1kHz, 16-bit
- Capture: 0.5-1.5 second WAV files, 44.1kHz, 16-bit
- Check: 0.5-1.0 second WAV files, 44.1kHz, 16-bit
- Checkmate: 1.0-2.0 second WAV files, 44.1kHz, 16-bit
- Clock Low Time: 0.3-0.5 second WAV files, 44.1kHz, 16-bit
- Game Start/End: 1.0-2.0 second WAV files, 44.1kHz, 16-bit

**UI Sounds:**
- Button Click: 0.1-0.2 second WAV files, 44.1kHz, 16-bit
- Menu Navigation: 0.1-0.3 second WAV files, 44.1kHz, 16-bit
- Notification: 0.5-1.0 second WAV files, 44.1kHz, 16-bit
- Achievement Unlock: 1.0-2.0 second WAV files, 44.1kHz, 16-bit

**Volume Levels:**
- Master Volume: 0-100%
- Game Sounds: 0-100%
- UI Sounds: 0-100%
- Music: 0-100%

##### D.4.2 Background Music

**Game Music:**
- Format: OGG Vorbis, 128-192 kbps
- Duration: 3-5 minutes with seamless looping
- Themes: Concentration, Strategy, Tension

**Menu Music:**
- Format: OGG Vorbis, 128-192 kbps
- Duration: 2-3 minutes with seamless looping
- Themes: Relaxed, Inviting, Elegant

**Implementation:**
- Dynamic crossfading between tracks
- Volume adjustment based on game state
- Mute option for players

### Appendix E: Glossary of Terms

This appendix provides definitions for technical terms, game-specific terminology, and abbreviations used throughout the PRD.

#### E.1 Chess Terminology

**Castling:** A special move involving the king and either rook, where the king moves two squares toward the rook, and the rook moves to the square the king crossed.

**Check:** A situation where a player's king is under attack by an opponent's piece.

**Checkmate:** A situation where a player's king is in check and there is no legal move to escape the check, resulting in the end of the game.

**En Passant:** A special pawn capture that can occur when a pawn moves two squares forward from its starting position and lands beside an opponent's pawn.

**FEN (Forsyth-Edwards Notation):** A standard notation for describing a particular board position of a chess game.

**PGN (Portable Game Notation):** A standard format for recording chess games, including moves and metadata.

**Stalemate:** A situation where a player has no legal moves but their king is not in check, resulting in a draw.

**Zugzwang:** A situation where a player is at a disadvantage because they must make a move when they would prefer to pass.

#### E.2 Technical Terminology

**API (Application Programming Interface):** A set of rules and protocols that allows different software applications to communicate with each other.

**JWT (JSON Web Token):** A compact, URL-safe means of representing claims to be transferred between two parties.

**Microservices:** An architectural style that structures an application as a collection of loosely coupled services.

**Redux:** A predictable state container for JavaScript apps, often used with React.

**Socket.IO:** A library that enables real-time, bidirectional communication between web clients and servers.

**WebGL:** A JavaScript API for rendering interactive 3D and 2D graphics within any compatible web browser.

**Web3:** The concept of a decentralized web, built on blockchain technology.

#### E.3 Platform-Specific Terminology

**Battlepass:** A tiered reward system that players progress through by earning XP from gameplay and completing missions.

**EVC (Everchess Coin):** The in-game currency used for purchases in the Everchess platform.

**NFT Chess Set:** A non-fungible token representing ownership of a unique digital chess set that can be used in the game.

**Staking:** The process of locking up NFT chess sets to earn rewards over time.

**XP (Experience Points):** Points earned through gameplay and completing missions, used to level up and progress through the battlepass.

## Mobile App Store Optimizations

This section outlines crucial optimizations to ensure successful release of Everchess native iOS and Android apps through the Apple App Store and Google Play Store, with special emphasis on leveraging Supabase for crash reporting and feedback collection.

### 1. App Store Compliance Review

#### Description
A thorough review of Apple App Store and Google Play Store guidelines to ensure the Everchess app complies with all requirements, particularly for Web3 features (NFT minting, staking) and in-app purchases (Everchess Coin, EVC).

#### Rationale
- **Apple App Store**: Strict rules prohibit apps from bypassing in-app purchase systems for digital goods (Guideline 3.1.1). NFT features must be structured externally (e.g., via Magic Eden).
- **Google Play Store**: Requires compliance with gambling and financial transaction policies (e.g., wager mode must avoid unlicensed gambling perceptions).
- Ensures smooth approval and avoids rejection delays.

#### Implementation Details
- **Phase 5 - Deployment Process Setup**:
  - Review Apple App Store Guidelines and Google Play Developer Policy Center
  - Ensure NFT minting/staking occurs externally (redirect to Magic Eden) to comply with Apple's rules
  - Document compliance for EVC purchases using Apple/Google in-app purchase APIs (`expo-in-app-purchases`)
  - Verify wager mode uses in-game currency (EVC) and includes age verification
  - Create a compliance checklist covering privacy, content moderation, and Web3 transparency
- **Deliverable**: Compliance checklist and documentation
- **Timeline Impact**: 2-3 days in Phase 5

### 2. Beta Testing Program

#### Description
Implementation of a beta testing program using TestFlight (iOS) and Google Play Beta (Android) to collect real-world feedback before public release.

#### Rationale
- Validates stability, usability, and performance across diverse devices
- Identifies bugs or UX issues, improving launch quality
- Engages early adopters for community momentum

#### Implementation Details
- **Phase 5 - Comprehensive Testing Implementation**:
  - Configure TestFlight and Google Play Beta
  - Generate beta builds with EAS Build
  - Recruit 50-100 testers from chess and Web3 communities
  - Create a feedback form and monitor crashes
  - Release at least two beta versions
- **Database Impact**: Add a `beta_testers` table in Supabase:
  ```sql
  CREATE TABLE beta_testers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    platform VARCHAR(20) NOT NULL,
    feedback_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```
- **Timeline Impact**: 2 weeks in Phase 5

### 3. App Store Metadata Preparation

#### Description
Preparation of metadata (description, screenshots, icons) for app store submissions to optimize discoverability and approval.

#### Rationale
- Improves visibility and user acquisition
- Reduces rejection risk with compliant metadata
- Highlights unique features (3D gameplay, NFTs, social elements)

#### Implementation Details
- **Phase 5 - Deployment Process Setup**:
  - Write a 400-4000 character app description
  - Design a 1024x1024 app icon
  - Create 5-10 screenshots per platform
  - Record a 15-30 second demo video for the App Store
  - Select keywords for App Store Optimization (ASO)
- **Timeline Impact**: 3-4 days in Phase 5

### 4. Mobile Performance Testing

#### Description
Implementation of performance testing to ensure smooth gameplay (60 FPS for 3D rendering, <2s screen load times) on diverse devices.

#### Rationale
- Ensures consistent experience on low-end devices
- Reduces negative reviews due to lag or crashes

#### Implementation Details
- **Phase 5 - Performance Optimization**:
  - Define benchmarks: 60 FPS, <2s load times, <100ms move validation latency
  - Test on high-end, mid-range, and low-end devices
  - Use Expo profiling tools and React Native Performance Monitor
  - Optimize Three Fiber (reduce polygons, use WebP textures)
  - Test real-time features (Socket.IO) on 3G/4G
  - Implement fallbacks (e.g., 2D board option)
- **Timeline Impact**: 5-7 days in Phase 5

### 5. Enhanced Crash Reporting with Supabase

#### Description
Implementation of crash reporting using **Supabase** to log crash data and Sentry to capture errors, with real-time alerts via Supabase subscriptions.

#### Rationale
- Supabase logs detailed crash data; Sentry captures errors
- Enables rapid issue resolution for <99.9% uptime

#### Implementation Details
- **Phase 5 - Monitoring Systems Integration**:
  - Use `sentry-expo` to capture crashes
  - Collect context with `expo-device` (device model, OS version)
  - Log to Supabase `error_logs` table
  - Set up real-time alerts via Supabase subscriptions
- **Database Impact**: Add `error_logs` table:
  ```sql
  CREATE TABLE error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    device_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  ```
- **Timeline Impact**: 3-4 days in Phase 5

### 6. App Size Optimization

#### Description
Minimize app size (<100MB iOS, <80MB Android) to improve download rates.

#### Rationale
- Reduces barriers to adoption, especially on low-storage devices
- Optimizes 3D assets and Web3 libraries

#### Implementation Details
- **Phase 5 - Performance Optimization**:
  - Analyze with `expo build:analyze`
  - Compress 3D assets (GLB <1MB, textures <500KB)
  - Use dynamic imports for non-critical features
  - Optimize dependencies and use ABI-specific APKs
  - Target <100MB (iOS), <80MB (Android)
- **Timeline Impact**: 4-5 days in Phase 5

### Impact on Development Timeline

These mobile app store optimizations add approximately **2-3 weeks** to Phase 5, extending the total timeline to **18-25 weeks** (from the original 16-23 weeks), which still fits within the flexible development timeframe while significantly improving the app's quality and launch readiness.

### Appendix F: Risk Assessment and Mitigation

This appendix identifies potential risks to the project and outlines strategies for mitigating these risks.

#### F.1 Technical Risks

| Risk | Probability | Impact | Mitigation Strategy |
|------|------------|--------|---------------------|
| Performance issues on low-end mobile devices | Medium | High | Implement adaptive quality settings, optimize 3D assets, use level of detail (LOD) techniques, provide 2D fallback option |
| Real-time synchronization failures | Medium | High | Implement robust error handling, fallback mechanisms, and state reconciliation |
| Blockchain integration challenges | High | Medium | Start with simplified Web3 features, use established libraries, conduct extensive testing |
| Scalability issues with high user load | Medium | High | Design for horizontal scaling, implement caching, use load balancing |
| Browser compatibility issues | Medium | Medium | Use polyfills, progressive enhancement, and thorough cross-browser testing |

#### F.2 Market Risks

| Risk | Probability | Impact | Mitigation Strategy |
|------|------------|--------|---------------------|
| Low user adoption | Medium | High | Implement effective user acquisition strategies, focus on core chess experience first |
| Negative reception to Web3 features | Medium | Medium | Make Web3 features optional, ensure traditional gameplay is solid |
| Competition from established chess platforms | High | Medium | Focus on unique differentiators (3D, NFTs, social features) |
| Monetization challenges | Medium | High | Diversify revenue streams, balance free and premium features |
| Changing market trends | Medium | Medium | Build flexibility into the roadmap, gather and respond to user feedback |

#### F.3 Project Risks

| Risk | Probability | Impact | Mitigation Strategy | Success Criteria |
|------|------------|--------|---------------------|------------------|
| Scope creep | High | High | Maintain strict MVP definition, use agile methodology, prioritize features | Feature prioritization matrix maintained; MVP scope document version-controlled with change approval process; User story backlog groomed weekly |
| Resource constraints | Medium | High | Focus on core features first, use efficient development practices | Core feature set 100% identified; Development velocity metrics established; Resource allocation plan updated bi-weekly |
| Timeline delays | Medium | Medium | Build buffer into the schedule, identify dependencies early | Critical path analysis completed; 15-20% buffer incorporated in timeline; Dependencies mapped with owners assigned |
| Quality issues | Medium | High | Implement comprehensive testing strategy, establish quality gates | Test coverage exceeds 80%; Automated test suite passes consistently; Quality metrics defined for each release stage |
| Team skill gaps | Low | Medium | Identify training needs early, consider external expertise for specialized areas | Skills assessment matrix completed; Training program established; Expert consultants identified for specialized domains |

#### F.4 Legal and Compliance Risks

| Risk | Probability | Impact | Mitigation Strategy | Success Criteria |
|------|------------|--------|---------------------|------------------|
| Data privacy regulations (GDPR, CCPA) | High | High | Implement privacy by design, obtain necessary consents, document data practices | Compliance audit passes with no critical findings; Privacy policy reviewed by legal counsel; Data mapping documentation complete |
| Blockchain regulatory uncertainty | High | Medium | Stay informed of regulatory developments, design flexible compliance approach | Monthly regulatory review process established; Compliance features isolated in modular components; Adaptation plan documented for major regulatory changes |
| App store rejection | High | High | Conduct thorough review of app store guidelines, external NFT minting, proper use of in-app purchase APIs | Pre-submission checklist 100% complete; Test accounts provided for app review; Alternative distribution plan documented |
| Intellectual property issues | Low | High | Conduct thorough IP review, secure necessary licenses | IP clearance report obtained; All assets properly licensed; Documentation of ownership for all custom assets |
| Age restrictions and gambling laws | Medium | High | Implement age verification, ensure wager features comply with local laws | Age verification system tested across all target markets; Legal review of wager features by jurisdiction; Geofencing capability implemented |
| Terms of service and user agreements | Medium | Medium | Develop comprehensive legal documents with professional legal review | Terms accepted by all legal jurisdictions; User acceptance tracking system implemented; Regular update schedule established |

#### F.5 Risk Interdependencies

The following table highlights how risks across different categories influence each other, requiring coordinated mitigation strategies:

| Primary Risk | Related Risks | Interdependency Description | Coordinated Mitigation Approach |
|--------------|---------------|------------------------------|--------------------------------|
| NFT implementation | App store rejection, Blockchain regulatory uncertainty | App store policies regarding NFTs could impact implementation approach and may change with regulatory developments | Maintain separate tracks for NFT features and core gameplay; design modular architecture to isolate blockchain features |
| Technical performance | User engagement, Market adoption | Poor technical performance will directly impact user retention and word-of-mouth marketing | Establish performance benchmarks that align with user experience goals; prioritize optimization from early development |
| Resource constraints | Timeline delays, Quality issues | Limited resources may force trade-offs between development speed and product quality | Create prioritization framework that balances essential features, quality standards, and schedule constraints |
| Blockchain regulatory uncertainty | Monetization strategy, Age restrictions | Changing regulations could impact tokenization and reward systems | Design alternative monetization pathways; create feature flags to enable/disable blockchain features by jurisdiction |
| Changing market trends | User engagement, App store rejection | Market shifts may require pivoting features, potentially impacting app store compliance | Implement analytics to identify early warning signs of market shifts; maintain flexible roadmap with quarterly reassessment |

#### F.6 Post-Launch Risk Management

The following risks become relevant after the initial product launch and require ongoing monitoring and management:

| Post-Launch Risk | Probability | Impact | Monitoring Strategy | Response Plan |
|------------------|------------|--------|---------------------|---------------|
| Security vulnerabilities | High | Critical | Regular penetration testing; Bug bounty program; Security monitoring tools | Incident response protocol; Emergency patch release process; Communication templates for security incidents |
| User retention decline | Medium | High | Daily/weekly active user metrics; Session length tracking; Churn analysis | Engagement feature prioritization; Targeted re-engagement campaigns; User feedback collection and implementation |
| Server scaling issues | Medium | High | Performance monitoring dashboard; Load testing before major events | Auto-scaling infrastructure; Regional server deployment plan; Degraded mode operations plan |
| Web3 ecosystem changes | High | Medium | Technology partner monitoring; Protocol governance participation | Adapter design patterns for Web3 interactions; Multi-chain support roadmap; Alternative technology assessment |
| Competitor features | High | Medium | Competitive analysis program; Industry conference participation | Feature parity assessment process; Unique value proposition reinforcement; Rapid prototyping pipeline |
| Content moderation challenges | Medium | Medium | User reporting systems; Automated content scanning | Moderation team scaling plan; Community guidelines evolution process; Appeal system implementation |

