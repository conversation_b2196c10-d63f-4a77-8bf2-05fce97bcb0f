# Everchess .gitignore

# Environment Variables & Secrets
.env
.env.local
.env.development
.env.test
.env.production
.env*.local
*.pem
google-services.json
GoogleService-Info.plist

# Node.js

npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
.npm
.node_repl_history

# React Native / Expo
.expo/
dist/
web-build/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
*.aab
*.apk
*.ipa
*.app
*.app.bundle
*.app.js
*.app.json

# iOS
ios/build/
ios/Pods/
ios/Podfile.lock
ios/DerivedData
ios/.build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
*.xcuserstate
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# Android
android/app/build/
android/build/
android/captures/
android/gradle/
android/.gradle/
android/local.properties
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/jarRepositories.xml
.idea/misc.xml
.idea/modules.xml
.idea/vcs.xml
.idea/compiler.xml
.idea/caches
.idea/modules
.idea/inspectionProfiles

# TypeScript/JavaScript
*.tsbuildinfo
.eslintcache
.npm
.yarn-integrity
.next
.nuxt
.cache
.parcel-cache
.docusaurus
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.DS_Store

# Editors & IDEs
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.vscode-test
.history
.ionide
Everchess Workspace.code-workspace

# Testing
coverage/
.nyc_output
*.lcov
.jest/
__tests__/__coverage__/

# Build Outputs
build/
dist/
web-build/
public/build/
out/
.next/
tmp/
temp/
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Blockchain / Solana
wallet-key.json
*-keypair.json
.anchor
.solana
target/
**/*.rs.bk
anchor/target/

# Supabase
.supabase/

# AWS
.aws/
*.pem
.elasticbeanstalk
.ebignore
.ebextensions
.ebignore
.elasticbeanstalk/*
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml

# Three.js/WebGL
*.glb.map
*.gltf.map

# Miscellaneous
.DS_Store
Thumbs.db
*.bak
*.tmp
*.swp
*.zip
*.tar.gz
*.tgz
.vercel
.netlify

# Local development
localhost-key.pem
localhost.pem

# Documentation (Keep out of repo if large/private)


# windsurf rules
.windsurfrules

# Everchess specific
everchess/README.md
everchess/app/web-build/
everchess/app/dist/
everchess/server/dist/

# Local environment files that might contain secrets
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cline specific files
.clinerules/
.clinememory/
memory-bank/
memory-bank/everchess-project-brief.md
