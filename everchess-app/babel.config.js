module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      ['@babel/plugin-transform-private-methods', { 'loose': false }],
      ['@babel/plugin-transform-private-property-in-object', { 'loose': false }],
      ['@babel/plugin-transform-class-properties', { 'loose': false }],
      [
        'module-resolver',
        {
          extensions: ['.js', '.jsx', '.ts', '.tsx'],
          root: ['./'],
          alias: {
            '@': './src',
            'react-native$': 'react-native-web',
            'react-native-gesture-handler': '@gorhom/bottom-sheet',
          },
        },
      ],
      'react-native-web',
    ],
  };
};
