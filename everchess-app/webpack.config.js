const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { GenerateSW } = require('workbox-webpack-plugin');
const WebpackPwaManifest = require('webpack-pwa-manifest');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const webpack = require('webpack');

// Load environment variables from .env file
require('dotenv').config();

const isDevelopment = process.env.NODE_ENV !== 'production';

module.exports = {
  mode: isDevelopment ? 'development' : 'production',
  entry: './index.web.js',
  
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: isDevelopment ? '[name].js' : '[name].[contenthash].js',
    publicPath: '/',
    clean: true,
  },
  module: {
    rules: [
      // Handle TypeScript and JavaScript files with Babel
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: [
          /node_modules\/(?!(@expo|expo|react-native-web|@react-native|@para-wallet|@getpara))/,
          // Exclude problematic React Native modules that don't work on web
          /node_modules\/react-native\/Libraries/,
          /node_modules\/@react-three\/fiber\/native/,
          /node_modules\/@react-navigation\/native-stack\/.*\/views/,
          /node_modules\/@react-navigation\/native\/.*\/use/,
          /node_modules\/@react-native-async-storage/,
        ],
        use: {
          loader: 'babel-loader',
          options: {
            configFile: path.resolve(__dirname, 'babel.config.web.js'),
            cacheDirectory: true,
            cacheCompression: false,
            babelrc: false, // Don't use .babelrc files
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg|woff|woff2|eot|ttf|otf)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 8192,
              name: 'assets/[name].[hash:8].[ext]',
            },
          },
        ],
      },
    ],
  },
  resolve: {
    extensions: [
      '.web.js',
      '.web.jsx',
      '.web.ts',
      '.web.tsx',
      '.js',
      '.jsx',
      '.ts',
      '.tsx',
      '.json',
      '.mjs',
    ],
    // Enable automatic extension resolution
    enforceExtension: false,
    symlinks: false,
    alias: {
      // Path aliases for imports
      '@': path.resolve(__dirname, 'src'),
      // React Native Web
      'react-native$': 'react-native-web',
      'react-native-web': 'react-native-web',
      'react-native-svg': 'react-native-svg-web',
      'react-native-reanimated': 'react-native-reanimated/lib/reanimated2',
      'react-native-gesture-handler': '@gorhom/bottom-sheet',
      '@react-native-community/async-storage': '@react-native-async-storage/async-storage',
      // Polyfills
      'crypto': 'crypto-browserify',
      'stream': 'stream-browserify',
      'util': 'util',
      'buffer': 'buffer',
      'process': 'process/browser',
      'path': 'path-browserify',
      'zlib': 'browserify-zlib',
      'http': 'stream-http',
      'https': 'https-browserify',
      'url': 'url',
      'os': 'os-browserify/browser',
      'assert': 'assert',
      'constants': 'constants-browserify',
      'querystring': 'querystring-es3',
      '_stream_duplex': 'readable-stream/duplex',
      '_stream_passthrough': 'readable-stream/passthrough',
      '_stream_readable': 'readable-stream/readable',
      '_stream_transform': 'readable-stream/transform',
      '_stream_writable': 'readable-stream/writable',
      'readable-stream': 'readable-stream',
      'vm': 'vm-browserify',
      'fs': false,
      'tls': false,
      'net': false,
      'dns': false,
      'child_process': false,
    },
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      util: require.resolve('util/'),
      buffer: require.resolve('buffer/'),
      process: require.resolve('process'),
      path: require.resolve('path-browserify'),
      zlib: require.resolve('browserify-zlib'),
      http: require.resolve('stream-http'),
      https: require.resolve('https-browserify'),
      url: require.resolve('url/'),
      os: require.resolve('os-browserify/browser'),
      assert: require.resolve('assert/'),
      constants: require.resolve('constants-browserify'),
      querystring: require.resolve('querystring-es3'),
      // Use the main readable-stream module for all stream operations
      _stream_duplex: false,
      _stream_passthrough: false,
      _stream_readable: false,
      _stream_transform: false,
      _stream_writable: false,
      'readable-stream': require.resolve('readable-stream'),
      vm: require.resolve('vm-browserify'),
      fs: false,
      tls: false,
      net: false,
      dns: false,
      child_process: false,
    },
  },
  plugins: [
    new CleanWebpackPlugin(),

    // Essential Node.js polyfills for Web3/Para SDK
    new NodePolyfillPlugin({
      excludeAliases: ['console']
    }),

    new HtmlWebpackPlugin({
      template: './web/index.html',
      inject: true,
    }),
    
    new WebpackPwaManifest({
      name: 'Everchess',
      short_name: 'Everchess',
      description: '3D Chess with Web3 Integration',
      background_color: '#ffffff',
      theme_color: '#000000',
      start_url: '/',
      display: 'standalone',
      icons: [
        {
          src: path.resolve('assets/icon.png'),
          sizes: [96, 128, 192, 256, 384, 512],
          destination: path.join('assets', 'icons'),
        },
      ],
    }),
    // Provide global polyfills for Node.js modules
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process',
    }),
    // Define global variables and environment variables
    new webpack.DefinePlugin({
      // Define process.env as an object with all our environment variables
      'process.env': JSON.stringify({
        NODE_ENV: process.env.NODE_ENV || 'development',
        // Para Web SDK standard variables
        REACT_APP_PARA_API_KEY: process.env.REACT_APP_PARA_API_KEY || process.env.EXPO_PUBLIC_PARA_API_KEY,
        REACT_APP_PARA_PROJECT_ID: process.env.REACT_APP_PARA_PROJECT_ID || process.env.EXPO_PUBLIC_PARA_PROJECT_ID,
        REACT_APP_PARA_ENVIRONMENT: process.env.REACT_APP_PARA_ENVIRONMENT || process.env.EXPO_PUBLIC_PARA_ENV || 'beta',
        // Legacy Expo variables (for compatibility)
        EXPO_PUBLIC_PARA_API_KEY: process.env.EXPO_PUBLIC_PARA_API_KEY,
        EXPO_PUBLIC_PARA_PROJECT_ID: process.env.EXPO_PUBLIC_PARA_PROJECT_ID,
        EXPO_PUBLIC_PARA_ENV: process.env.EXPO_PUBLIC_PARA_ENV || 'beta',
      }),
      'global': 'globalThis',
    }),

    ...(isDevelopment ? [] : [
      new GenerateSW({
        clientsClaim: true,
        skipWaiting: true,
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\//,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'google-fonts-stylesheets',
            },
          },
        ],
      }),
    ]),
  ],

  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        para: {
          test: /[\\/]node_modules[\\/](@para|para|@getpara)/,
          name: 'para-sdk',
          chunks: 'all',
        }
      },
    },
  },
  devServer: {
    static: path.join(__dirname, 'dist'),
    historyApiFallback: true,
    compress: true,
    port: 8080,  // Changed from 3000 to 8080
    host: '0.0.0.0',  // Allow connections from any host
    hot: true,
    client: {
      overlay: {
        errors: true,
        warnings: true,
      },
      logging: 'warn',
      progress: true,
    },
    devMiddleware: {
      publicPath: '/',
    },
  },

  devtool: isDevelopment ? 'source-map' : false,
};
