const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');

const isDevelopment = process.env.NODE_ENV !== 'production';

module.exports = {
  mode: isDevelopment ? 'development' : 'production',
  entry: './index.web.minimal.js',
  
  output: {
    path: path.resolve(__dirname, 'dist-minimal'),
    filename: isDevelopment ? '[name].js' : '[name].[contenthash].js',
    publicPath: '/',
    clean: true,
  },
  
  module: {
    rules: [
      // Handle TypeScript and JavaScript files with Babel
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: [
          /node_modules\/(?!(@expo|expo|react-native-web|@react-native|@para-wallet|@getpara))/,
        ],
        use: {
          loader: 'babel-loader',
          options: {
            configFile: path.resolve(__dirname, 'babel.config.web.js'),
            cacheDirectory: true,
            cacheCompression: false,
            babelrc: false,
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg|woff|woff2|eot|ttf|otf)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 8192,
              name: 'assets/[name].[hash:8].[ext]',
            },
          },
        ],
      },
    ],
  },
  
  resolve: {
    extensions: [
      '.web.js',
      '.web.jsx',
      '.web.ts',
      '.web.tsx',
      '.js',
      '.jsx',
      '.ts',
      '.tsx',
      '.json',
    ],
    alias: {
      // Path aliases for imports
      '@': path.resolve(__dirname, 'src'),
      // React Native Web
      'react-native$': 'react-native-web',
      'react-native-web': 'react-native-web',
    },
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      stream: require.resolve('stream-browserify'),
      util: require.resolve('util/'),
      buffer: require.resolve('buffer/'),
      process: require.resolve('process'),
      path: require.resolve('path-browserify'),
      zlib: require.resolve('browserify-zlib'),
      http: require.resolve('stream-http'),
      https: require.resolve('https-browserify'),
      url: require.resolve('url/'),
      os: require.resolve('os-browserify/browser'),
      assert: require.resolve('assert/'),
      constants: require.resolve('constants-browserify'),
      querystring: require.resolve('querystring-es3'),
      vm: require.resolve('vm-browserify'),
      fs: false,
      tls: false,
      net: false,
      dns: false,
      child_process: false,
    },
  },
  
  plugins: [
    new HtmlWebpackPlugin({
      template: './web/index.html',
      inject: true,
    }),
    // Provide global polyfills for Node.js modules
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process',
    }),
    // Define global variables
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'global': 'globalThis',
    }),
  ],
  
  devServer: {
    static: path.join(__dirname, 'dist-minimal'),
    historyApiFallback: true,
    compress: true,
    port: 8081,
    host: '0.0.0.0',
    hot: true,
  },
  
  devtool: isDevelopment ? 'source-map' : false,
};
