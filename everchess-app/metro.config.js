const { getDefaultConfig } = require('expo/metro-config');

// Get the default Metro configuration
const config = getDefaultConfig(__dirname, {
  // Enable CSS support.
  isCSSEnabled: true,
});

// Add the SVG transformer
config.transformer.babelTransformerPath = require.resolve('react-native-svg-transformer');
config.resolver.assetExts = config.resolver.assetExts.filter(ext => ext !== 'svg');
config.resolver.sourceExts.push('svg');

// Add web-specific extensions and configurations for Para SDK compatibility
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'web.js',
  'web.ts', 
  'web.tsx',
];

// Para SDK web compatibility fixes
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Handle specific Para SDK modules that may cause bundling issues
config.resolver.alias = {
  ...config.resolver.alias,
  // Ensure proper resolution of Para SDK components
  '@getpara/core-components': '@getpara/core-components/dist/esm',
  '@getpara/react-sdk': '@getpara/react-sdk',
};

// Transformer configuration for better compatibility
config.transformer = {
  ...config.transformer,
  // Enable minification for web builds
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_keys: true,
      wrap_iife: true,
    },
    sourceMap: {
      includeSources: false,
    },
    toplevel: false,
    compress: {
      reduce_funcs: false,
    },
  },
};

// Unconditionally apply polyfills for all platforms, mirroring the official Para example.
// This is the key fix for the crypto error on Android dev builds.
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  crypto: require.resolve('react-native-quick-crypto'),
  buffer: require.resolve('@craftzdog/react-native-buffer'),
  stream: require.resolve('readable-stream'),
  // Add additional polyfills for web compatibility
  'react-native': require.resolve('react-native-web'),
};

// Platform-specific configurations
if (process.env.EXPO_PLATFORM === 'web') {
  // Web-specific configurations for Para SDK
  config.resolver.platforms = ['web', 'native', 'ios', 'android'];
  
  // Handle module resolution for web
  config.resolver.alias = {
    ...config.resolver.alias,
    'react-native$': 'react-native-web',
    'react-native/Libraries/EventEmitter/RCTDeviceEventEmitter$': 'react-native-web/dist/vendor/react-native/NativeEventEmitter/RCTDeviceEventEmitter',
    'react-native/Libraries/vendor/emitter/EventEmitter$': 'react-native-web/dist/vendor/react-native/emitter/EventEmitter',
  };
}

module.exports = config;
