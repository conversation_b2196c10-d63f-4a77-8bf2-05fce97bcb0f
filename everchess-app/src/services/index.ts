// This file will be used by Metro to resolve the correct paraService implementation.
// If you have paraService.native.ts and paraService.web.ts in the same directory,
// Metro bundler will pick the appropriate one based on the platform.

// Option 1: Rely on Metro's platform-specific extension resolution (.native.ts, .web.ts)
// If paraService.native.ts and paraService.web.ts are in this directory, this should work:
// export * from './paraService'; // This would look for paraService.ts, paraService.ios.ts, paraService.android.ts, paraService.native.ts, paraService.web.ts

// Option 2: Explicit conditional export (more robust if Metro resolution is tricky)
import { Platform } from 'react-native';

let paraServiceInstance: any;

if (Platform.OS === 'web') {
  paraServiceInstance = require('./paraService.web').paraService;
} else {
  paraServiceInstance = require('./paraService.native').paraService;
}

export const paraService = paraServiceInstance;

// Note: If using aliases in tsconfig.json (e.g., "@/services"), ensure they point to this directory
// or that the import path in AuthContext.tsx is updated accordingly.
// For example, if AuthContext.tsx imports from '@services/paraService',
// and if paraService.native.ts/web.ts are in a subdirectory like 'src/services/para/',
// then this index.ts should be 'src/services/para/index.ts' and export from './paraService'
// or AuthContext should import from '@services/para'.

// For simplicity, assuming paraService.native.ts and paraService.web.ts are in the SAME directory as this index.ts.
// If they are in a 'paraService' subdirectory, the require paths would be './paraService/paraService.web' etc.
