import { ParaMobile, WalletType, Environment, OAuthMethod } from '@getpara/react-native-wallet';
import { openAuthSessionAsync } from 'expo-web-browser';
import Constants from 'expo-constants';
import { AuthResult, User } from '../types/auth';

// Define a type for the expected OAuth result structure based on observed returned values
interface ParaOAuthResult {
  email?: string;
  userExists?: boolean;
  [key: string]: any;
}

const PARA_API_KEY = Constants.expoConfig?.extra?.paraApiKey ?? process.env.EXPO_PUBLIC_PARA_API_KEY;
const APP_SCHEME = 'everchess'; // A unique scheme for deep linking

if (!PARA_API_KEY) {
  console.error('Para API Key is missing. Please set EXPO_PUBLIC_PARA_API_KEY in your .env file.');
}

// Initialize ParaMobile with the exact pattern from the official example
const paraMobile = new ParaMobile(Environment.BETA, PARA_API_KEY!, undefined, {
  disableWorkers: true,
});

// Utility function to get or create Solana wallet for authenticated users
const getOrCreateSolanaWallet = async (): Promise<{ address?: string } | null> => {
  try {
    // Check for existing Solana wallets
    const existingWallets = await paraMobile.getWalletsByType(WalletType.SOLANA);
    
    if (existingWallets.length > 0) {
      return existingWallets[0];
    }
    
    // Create new Solana wallet if none exists
    await paraMobile.createWallet({ type: WalletType.SOLANA });
    const newWallets = await paraMobile.getWalletsByType(WalletType.SOLANA);
    
    return newWallets.length > 0 ? newWallets[0] : null;
  } catch (error) {
    console.error('Error getting/creating Solana wallet:', error);
    return null;
  }
};

export const init = async (): Promise<void> => {
  // Initialize the Para client like in the official examples
  try {
    await paraMobile.init();
    console.log('Para client initialized successfully');
  } catch (error) {
    console.error('Failed to initialize para client:', error);
    // Don't throw here - let the app continue and handle errors gracefully in individual functions
  }
};

export const createUserByPhone = async (phoneNumber: string, countryCode: string): Promise<AuthResult> => {
  try {
    await paraMobile.createUserByPhone({ phone: phoneNumber, countryCode });
    return { success: true, needsVerification: true };
  } catch (error: any) {
    console.error('Error in createUserByPhone:', error);
    return { success: false, error: error.message };
  }
};

export const verifyPhone = async (otp: string): Promise<AuthResult> => {
  try {
    const biometricsId = await paraMobile.verifyPhone({ verificationCode: otp });
    return { success: true, biometricsId };
  } catch (error: any) {
    console.error('Error in verifyPhone:', error);
    return { success: false, error: error.message };
  }
};

export const createGuestUser = async (): Promise<AuthResult> => {
  try {
    console.log('[GuestAuth] Creating guest user...');
    
    // Based on Para SDK documentation, guest authentication is handled differently
    // For React Native, we need to create a wallet after authentication
    // Let's create a minimal authentication flow for guest users
    
    // First, try to create a Solana wallet which will be the guest's wallet
    // This requires some form of authentication first
    console.log('[GuestAuth] Creating guest Solana wallet...');
    await paraMobile.createWallet({ type: WalletType.SOLANA });
    
    // Get the newly created wallet
    const wallets = await paraMobile.getWalletsByType(WalletType.SOLANA);
    const guestWallet = wallets[0];
    
    if (!guestWallet?.address) {
      console.log('[GuestAuth] No wallet created, user needs to authenticate first');
      return {
        success: false,
        error: 'Guest mode requires authentication. Please use a social login option first.',
        webFlowInitiated: true,
      };
    }
    
    console.log(`[GuestAuth] Created guest wallet: ${guestWallet.address}`);
    
    const user: User = {
      id: guestWallet.address,
      isAuthenticated: true, // User is authenticated but marked as guest in app logic
      isGuest: true, // This is our app-level guest flag
      solanaAddress: guestWallet.address,
    };
    
    console.log('[GuestAuth] Guest user created successfully.');
    return { success: true, user };
    
  } catch (error: any) {
    console.error('[GuestAuth] Error creating guest user:', error);
    
    // If wallet creation fails due to authentication, guide user to social login
    if (error.message?.includes('not authenticated') || error.message?.includes('login')) {
      return {
        success: false,
        error: 'Guest mode requires authentication. Please use a social login option first.',
        webFlowInitiated: true,
      };
    }
    
    return { success: false, error: error.message || 'Guest authentication failed.' };
  }
};

export const signInWithSocial = async (provider: 'google' | 'apple' | 'x'): Promise<AuthResult> => {
  console.log(`[SocialSignIn] Starting social sign-in with provider: ${provider}`);
  try {
    let method: OAuthMethod;
    switch (provider) {
      case 'google':
        method = OAuthMethod.GOOGLE;
        break;
      case 'apple':
        method = OAuthMethod.APPLE;
        break;
      case 'x':
        method = OAuthMethod.TWITTER;
        break;
      default:
        const unsupportedError = `Unsupported social provider: ${provider}`;
        console.error(`[SocialSignIn] ${unsupportedError}`);
        return { success: false, error: unsupportedError };
    }

    console.log('[SocialSignIn] Getting OAuth URL...');
    const oauthUrl = await paraMobile.getOAuthURL({ method, deeplinkUrl: APP_SCHEME });
    console.log('[SocialSignIn] OAuth URL received.');

    console.log('[SocialSignIn] Opening auth session in browser...');
    const authSessionResult = await openAuthSessionAsync(oauthUrl, APP_SCHEME, {
      preferEphemeralSession: false,
    });
    console.log(`[SocialSignIn] Auth session returned with type: ${authSessionResult.type}`);

    if (authSessionResult.type !== 'success') {
      console.log('[SocialSignIn] OAuth flow was not successful (cancelled or dismissed).');
      return { success: false, error: 'Authentication was cancelled.' };
    }

    console.log('[SocialSignIn] Auth session successful. Waiting for SDK to process result...');
    const oauthResult = (await paraMobile.waitForOAuth()) as ParaOAuthResult;
    console.log('[SocialSignIn] SDK processed OAuth result.');

    const { email, userExists } = oauthResult;
    if (!email) {
      const emailError = 'Failed to retrieve email from social provider.';
      console.error(`[SocialSignIn] ${emailError}`);
      return { success: false, error: emailError };
    }
    console.log(`[SocialSignIn] User email: ${email}, User exists: ${userExists}`);

    if (userExists) {
      console.log('[SocialSignIn] Existing user. Logging in...');
      await paraMobile.login({ email });
    } else {
      console.log('[SocialSignIn] New user. Registering passkey...');
      const biometricId = await paraMobile.getSetUpBiometricsURL({
        isForNewDevice: false,
        authType: 'email',
      });

      if (!biometricId) {
        const bioError = 'Failed to get biometrics setup URL.';
        console.error(`[SocialSignIn] ${bioError}`);
        return { success: false, error: bioError };
      }
      console.log('[SocialSignIn] Biometrics ID obtained. Registering...');
      await paraMobile.registerPasskey({ email, biometricsId: biometricId });
    }
    console.log('[SocialSignIn] User authenticated. Fetching wallets...');

    const primaryWallet = await getOrCreateSolanaWallet();

    if (!primaryWallet?.address) {
      const walletError = 'Failed to find or create a Solana wallet.';
      console.error(`[SocialSignIn] ${walletError}`);
      return { success: false, error: walletError };
    }
    console.log(`[SocialSignIn] Found/created Solana wallet: ${primaryWallet.address}`);

    const user: User = {
      id: primaryWallet.address,
      email,
      isAuthenticated: true,
      isGuest: false,
      solanaAddress: primaryWallet.address,
    };

    console.log('[SocialSignIn] Sign-in process completed successfully.');
    return { success: true, user };
  } catch (error: any) {
    console.error('[SocialSignIn] An error occurred:', JSON.stringify(error, null, 2));

    // Handle Passkey provider not available on Android
    if (error.message?.includes('No create options available')) {
      return {
        success: false,
        error: 'No passkey provider found. Please ensure you are signed into a Google Account in your device settings.',
      };
    }

    // Handle specific crypto error on Android Keystore
    if (error.message?.includes('CryptoFailedException')) {
      return {
        success: false,
        error: 'A keychain error occurred. Please try again or restart the app.',
      };
    }
    if (error.message?.includes('cancelled') || error.message?.includes('canceled')) {
      return { success: false, error: 'Authentication was cancelled.' };
    }
    return { success: false, error: error.message || 'An unknown error occurred.' };
  }
};

export const connectSolanaWallet = async (wallet: 'solflare' | 'phantom' | 'backpack'): Promise<AuthResult> => {
  try {
    console.log(`[SolanaWallet] Connecting ${wallet} wallet...`);
    
    // Based on the official Para example, Solana wallet connection works with existing authenticated users
    // First, check if user has existing Solana wallets (meaning they're authenticated)
    const primaryWallet = await getOrCreateSolanaWallet();
    
    if (!primaryWallet?.address) {
      // No existing wallets, user needs to authenticate first
      console.log('[SolanaWallet] No existing wallets found. Creating new Solana wallet...');
      
      try {
        // Try to create a new Solana wallet
        await paraMobile.createWallet({ type: WalletType.SOLANA });
        
        // Get the newly created wallet
        const newWallets = await paraMobile.getWalletsByType(WalletType.SOLANA);
        const newWallet = newWallets[0];
        
        if (!newWallet?.address) {
          return {
            success: false,
            error: 'Please sign in first before connecting a Solana wallet.',
            webFlowInitiated: true,
          };
        }
        
        console.log(`[SolanaWallet] Created new Solana wallet: ${newWallet.address}`);
        
        const user: User = {
          id: newWallet.address,
          isAuthenticated: true,
          isGuest: false,
          solanaAddress: newWallet.address,
        };
        
        return { success: true, user };
        
      } catch (createError: any) {
        console.error('[SolanaWallet] Failed to create wallet:', createError);
        return {
          success: false,
          error: 'Please sign in first before connecting a Solana wallet.',
          webFlowInitiated: true,
        };
      }
    }
    
    // Use existing wallet - this is the standard flow from the Para example
    console.log(`[SolanaWallet] Using existing Solana wallet: ${primaryWallet.address}`);
    
    // Note: The Para SDK manages MPC wallets internally
    // External wallet connection (Phantom, Solflare, Backpack) would require additional setup
    // For now, we're using the Para-managed Solana wallet
    console.log(`[SolanaWallet] Connected to ${wallet} (using Para-managed wallet)`);
    
    const user: User = {
      id: primaryWallet.address || '',
      isAuthenticated: true,
      isGuest: false,
      solanaAddress: primaryWallet.address || '',
    };
    
    return { success: true, user };
    
  } catch (error: any) {
    console.error(`[SolanaWallet] Error connecting ${wallet} wallet:`, error);
    
    if (error.message?.includes('not authenticated') || error.message?.includes('login')) {
      return { 
        success: false, 
        error: 'Please sign in first before connecting a Solana wallet.',
        webFlowInitiated: true,
      };
    }
    
    return { 
      success: false, 
      error: error.message || `Failed to connect ${wallet} wallet.` 
    };
  }
};

export const signUpOrLoginWithPhone = async (phone: string, countryCode: string) => {
  return createUserByPhone(phone, countryCode);
};

export const verifyLoginOtp = async (
  _identifier: string,
  otp: string,
  _method: 'email' | 'phone',
  _biometricsId?: string,
): Promise<AuthResult> => {
  return verifyPhone(otp);
};

export const logout = async (): Promise<void> => {
  // Native SDK session is managed by the OS/keychain, so logout is a client-side concept.
  // No specific paraMobile method needs to be called here for this implementation.
  console.log('User logged out from native service');
};

// Implement email authentication using Para SDK
export const signInWithEmail = async (email: string): Promise<AuthResult> => {
  try {
    console.log(`[EmailSignIn] Starting email sign-in for: ${email}`);
    
    // Check if user exists
    const userExists = await paraMobile.checkIfUserExists({ email });
    
    if (userExists) {
      console.log('[EmailSignIn] Existing user. Logging in...');
      await paraMobile.login({ email });
    } else {
      console.log('[EmailSignIn] New user. Setting up biometrics...');
      const biometricId = await paraMobile.getSetUpBiometricsURL({
        isForNewDevice: false,
        authType: 'email',
      });

      if (!biometricId) {
        const bioError = 'Failed to get biometrics setup URL.';
        console.error(`[EmailSignIn] ${bioError}`);
        return { success: false, error: bioError };
      }
      
      console.log('[EmailSignIn] Registering passkey...');
      await paraMobile.registerPasskey({ email, biometricsId: biometricId });
    }
    
    // Create or get Solana wallet
    const primaryWallet = await getOrCreateSolanaWallet();

    if (!primaryWallet?.address) {
      const walletError = 'Failed to find or create a Solana wallet.';
      console.error(`[EmailSignIn] ${walletError}`);
      return { success: false, error: walletError };
    }

    const user: User = {
      id: primaryWallet.address,
      email,
      isAuthenticated: true,
      isGuest: false,
      solanaAddress: primaryWallet.address,
    };

    console.log('[EmailSignIn] Email sign-in completed successfully.');
    return { success: true, user };
  } catch (error: any) {
    console.error('[EmailSignIn] Error:', error);
    return { success: false, error: error.message || 'Email sign-in failed.' };
  }
};

export const signUpWithEmail = async (email: string): Promise<AuthResult> => {
  // For Para SDK, sign up and sign in are the same flow
  return signInWithEmail(email);
};
