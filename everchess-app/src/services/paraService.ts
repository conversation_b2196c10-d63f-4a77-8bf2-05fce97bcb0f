// Default to the native service. The web implementation is now handled via hooks in AuthContext.
import * as paraService from './paraService.native';

// Export all functions from the native service
export const {
  init,
  createUserByPhone,
  verifyPhone,
  createGuestUser,
  signInWithSocial,
  connectSolanaWallet,
  signUpOrLoginWithPhone,
  verifyLoginOtp,
  logout,
  signInWithEmail,
  signUpWithEmail
} = paraService;

export default paraService;
