/**
 * @deprecated paraService.web.ts is deprecated.
 * The web authentication flow has been refactored to use the hook-based approach
 * from `@getpara/react-sdk` directly within AuthContext.tsx.
 * This service is no longer used and will be removed.
 */

const deprecatedService = {
  init: async () => {},
  signUpOrLoginWithEmail: async () => ({ success: false, error: 'Deprecated' }),
  signUpOrLoginWithPhone: async () => ({ success: false, error: 'Deprecated' }),
  verifyLoginOtp: async () => ({ success: false, error: 'Deprecated' }),
  createGuestUser: async () => ({ success: false, error: 'Deprecated' }),
  logout: async () => {},
  signInWithSocial: async () => ({ success: false, error: 'Deprecated' }),
  connectSolanaWallet: async () => ({ success: false, error: 'Deprecated' }),
};

export const paraService = deprecatedService;
