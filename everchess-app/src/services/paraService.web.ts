/**
 * Para Wallet Service - Web Implementation
 *
 * This service provides a unified interface for Para Wallet operations on web.
 * It integrates with the @getpara/react-sdk hooks and provides a service layer
 * for consistent API across platforms.
 */

import { AuthResult } from '@/types/auth';

// Web implementation uses the Para React SDK hooks directly
// This service provides a consistent interface for the AuthContext
export const paraService = {
  /**
   * Initialize Para service (no-op for web as initialization is handled by ParaProvider)
   */
  init: async (): Promise<void> => {
    // Web initialization is handled by ParaProvider in App.web.tsx
    console.log('Para Web SDK initialized via ParaProvider');
  },

  /**
   * Web authentication is handled by the Para Modal
   * These methods return a success indicator that the modal was opened
   */
  signUpOrLoginWithEmail: async (email: string): Promise<AuthResult> => {
    console.log('Web: Email auth handled by Para Modal');
    return { success: true, webFlowInitiated: true };
  },

  signUpOrLoginWithPhone: async (phone: string, countryCode: string): Promise<AuthResult> => {
    console.log('Web: Phone auth handled by Para Modal');
    return { success: true, webFlowInitiated: true };
  },

  verifyLoginOtp: async (identifier: string, otp: string, method: 'email' | 'phone'): Promise<AuthResult> => {
    console.log('Web: OTP verification handled by Para Modal');
    return { success: true, webFlowInitiated: true };
  },

  createGuestUser: async (): Promise<AuthResult> => {
    console.log('Web: Guest user creation handled by Para Modal');
    return { success: true, webFlowInitiated: true };
  },

  logout: async (): Promise<void> => {
    console.log('Web: Logout handled by Para SDK hooks');
    // Actual logout is handled by the useLogout hook in AuthContext
  },

  signInWithSocial: async (provider: string): Promise<AuthResult> => {
    console.log(`Web: ${provider} auth handled by Para Modal`);
    return { success: true, webFlowInitiated: true };
  },

  connectSolanaWallet: async (): Promise<AuthResult> => {
    console.log('Web: Solana wallet connection handled by Para Modal');
    return { success: true, webFlowInitiated: true };
  },
};
