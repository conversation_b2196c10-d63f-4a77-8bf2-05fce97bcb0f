export type AuthStackParamList = {
  AuthScreen: undefined; // New unified auth screen
  SignUp: undefined; // Keep for reference, but won't be used
  OtpVerification: {
    identifier: string; // The phone number or email being verified
  };
};

export type MainStackParamList = {
  HomeScreen: undefined;
  AuthTestScreen: undefined; // Development testing screen
  // Add other main app screens here
};

// Example for a tab navigator if you have one
// export type MainTabParamList = {
//   HomeTab: undefined;
//   ProfileTab: undefined;
// };

// You can combine stack params if needed, or keep them separate
// export type RootStackParamList = AuthStackParamList & MainStackParamList;
