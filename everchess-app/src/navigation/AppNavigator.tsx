import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '@/context/AuthContext';
import { ActivityIndicator, View } from 'react-native';

// Import Screens
import AuthScreen from '@/screens/Auth/AuthScreen';
import OtpVerificationScreen from '@/screens/Auth/OtpVerificationScreen';
import SignUpScreen from '@/screens/Auth/SignUpScreen';
import HomeScreen from '@/screens/HomeScreen';
import AuthTestScreen from '@/screens/AuthTestScreen';

// Import Nav Types
import { AuthStackParamList, MainStackParamList } from './types';

const AuthStack = createNativeStackNavigator<AuthStackParamList>();
const MainStack = createNativeStackNavigator<MainStackParamList>();

const AuthNavigator = () => (
  <AuthStack.Navigator screenOptions={{ headerShown: false }}>
    <AuthStack.Screen name="AuthScreen" component={AuthScreen} />
    <AuthStack.Screen name="OtpVerification" component={OtpVerificationScreen} />
    {/* The old SignUp screen is no longer in the primary flow */}
    <AuthStack.Screen name="SignUp" component={SignUpScreen} />
  </AuthStack.Navigator>
);

const MainNavigator = () => (
  <MainStack.Navigator screenOptions={{ headerShown: false }}>
    <MainStack.Screen name="HomeScreen" component={HomeScreen} />
    <MainStack.Screen name="AuthTestScreen" component={AuthTestScreen} />
  </MainStack.Navigator>
);

const AppNavigator: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#121212' }}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  return isAuthenticated ? <MainNavigator /> : <AuthNavigator />;
};

export default AppNavigator;
