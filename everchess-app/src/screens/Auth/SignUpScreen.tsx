import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/navigation/types';

const theme = {
  backgroundColor: '#1d1d1d',
  foregroundColor: '#ffffff',
  accentColor: '#ffffff',
  borderRadius: 8,
};

type SignUpScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'SignUp'>;

const SignUpScreen: React.FC = () => {
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('1'); // Default to '1' for US
  const { createUserByPhone, isLoading, error } = useAuth();
  const navigation = useNavigation<SignUpScreenNavigationProp>();

  const handleSignUp = async () => {
    if (!phone || !countryCode) {
      Alert.alert('Error', 'Please enter your country code and phone number.');
      return;
    }
    const result = await createUserByPhone(phone, countryCode);
    if (result.success && result.needsVerification) {
      Alert.alert('Check Your Phone', 'A verification code has been sent to your phone.');
      navigation.navigate('OtpVerification', { identifier: phone });
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Create Account with Phone</Text>
      <View style={styles.phoneInputContainer}>
        <TextInput
          style={styles.countryCodeInput}
          placeholder="+1"
          placeholderTextColor="#aaa"
          value={countryCode}
          onChangeText={setCountryCode}
          keyboardType="phone-pad"
        />
        <TextInput
          style={styles.phoneInput}
          placeholder="Phone Number"
          placeholderTextColor="#aaa"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
          autoCapitalize="none"
        />
      </View>
      <View style={styles.buttonContainer}>
        <Button title={isLoading ? 'Sending Code...' : 'Sign Up with Phone'} onPress={handleSignUp} disabled={isLoading} color={theme.accentColor} />
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
      <Text style={styles.infoText}>
        You'll receive an SMS message to verify your account.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: theme.backgroundColor,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: theme.foregroundColor,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '80%',
    marginVertical: 10,
  },
  countryCodeInput: {
    padding: 15,
    borderWidth: 1,
    borderColor: theme.foregroundColor,
    borderRadius: theme.borderRadius,
    color: theme.foregroundColor,
    backgroundColor: '#2a2a2a',
    fontSize: 16,
    marginRight: 10,
    textAlign: 'center',
  },
  phoneInput: {
    flex: 1,
    padding: 15,
    borderWidth: 1,
    borderColor: theme.foregroundColor,
    borderRadius: theme.borderRadius,
    color: theme.foregroundColor,
    backgroundColor: '#2a2a2a',
    fontSize: 16,
  },
  buttonContainer: {
    width: '80%',
    marginVertical: 10,
  },
  errorText: {
    color: 'red',
    marginTop: 10,
    fontSize: 14,
  },
  infoText: {
    color: theme.foregroundColor,
    opacity: 0.8,
    marginTop: 15,
    textAlign: 'center',
    paddingHorizontal: '10%',
    fontSize: 14,
  },
});

export default SignUpScreen;
