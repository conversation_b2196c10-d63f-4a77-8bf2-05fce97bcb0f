import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ScrollView, Linking } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';

const theme = {
  backgroundColor: '#1d1d1d',
  foregroundColor: '#ffffff',
  accentColor: '#4CAF50',
  secondaryColor: '#2196F3',
  textSecondary: '#aaa',
  borderRadius: 8,
};

const SignInScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const { signInWithEmail, isLoading, error, continueAsGuest } = useAuth();

  const handleSignIn = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter your email.');
      return;
    }
    await signInWithEmail(email);
  };

  const handleGuestMode = async () => {
    try {
      await continueAsGuest();
    } catch (err) {
      Alert.alert('Error', 'Failed to continue as guest. Please try again.');
    }
  };

  const handleLearnMore = () => {
    // Open a web page or modal explaining passkeys and security
    Linking.openURL('https://getpara.com/security');
  };

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      <View style={styles.container}>
        <Text style={styles.title}>Welcome to Everchess</Text>
        <Text style={styles.subtitle}>Sign in to save your progress and access all features</Text>
        
        {error && <Text style={styles.errorText}>{error}</Text>}
        
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Continue with Email</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your email"
            placeholderTextColor={theme.textSecondary}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            autoComplete="email"
          />
          
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]}
            onPress={handleSignIn}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>
              {isLoading ? 'Signing In...' : 'Continue with Email'}
            </Text>
          </TouchableOpacity>
          
          <Text style={styles.note}>
            We'll send you a secure link to sign in. No password needed!{' '}
            <Text style={styles.learnMore} onPress={handleLearnMore}>
              Learn more
            </Text>
          </Text>
          
          <View style={styles.dividerContainer}>
            <View style={styles.divider} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.divider} />
          </View>
          
          <TouchableOpacity 
            style={[styles.button, styles.secondaryButton]}
            onPress={handleGuestMode}
            disabled={isLoading}
          >
            <Text style={[styles.buttonText, { color: theme.foregroundColor }]}>
              Continue as Guest
            </Text>
          </TouchableOpacity>
          
          <Text style={styles.disclaimer}>
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: theme.backgroundColor,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.foregroundColor,
    marginTop: 40,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.textSecondary,
    marginBottom: 32,
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#2a2a2a',
    borderRadius: theme.borderRadius,
    padding: 24,
    width: '100%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.foregroundColor,
    marginBottom: 16,
  },
  input: {
    width: '100%',
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#444',
    borderRadius: theme.borderRadius,
    color: theme.foregroundColor,
    backgroundColor: '#333',
    fontSize: 16,
  },
  button: {
    width: '100%',
    padding: 16,
    borderRadius: theme.borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: theme.accentColor,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#444',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  note: {
    fontSize: 13,
    color: theme.textSecondary,
    marginBottom: 24,
    textAlign: 'center',
  },
  learnMore: {
    color: theme.secondaryColor,
    textDecorationLine: 'underline',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: '#444',
  },
  dividerText: {
    paddingHorizontal: 10,
    color: theme.textSecondary,
    fontSize: 14,
  },
  disclaimer: {
    fontSize: 12,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 16,
  },
  errorText: {
    color: '#ff4444',
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 14,
  },
});

export default SignInScreen;
