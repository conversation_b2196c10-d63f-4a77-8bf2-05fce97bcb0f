import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { AuthStackParamList } from '@/navigation/types';
import { useAuth } from '@/contexts/AuthContext';

type OtpVerificationScreenRouteProp = RouteProp<AuthStackParamList, 'OtpVerification'>;

const OtpVerificationScreen: React.FC = () => {
  const route = useRoute<OtpVerificationScreenRouteProp>();
  const navigation = useNavigation();
  const { verifyPhone, isLoading, error } = useAuth();

  const { identifier } = route.params;
  const [otp, setOtp] = useState<string>('');

  const handleVerifyOtp = async () => {
    if (!otp.trim() || otp.length < 6) {
      Alert.alert('Invalid OTP', 'Please enter the 6-digit code sent to you.');
      return;
    }

    const result = await verifyPhone(otp);

    if (result.success) {
      Alert.alert(
        'Phone Verified!',
        'Your phone number has been successfully verified. The next step is to create a secure passkey.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } else {
      Alert.alert('Verification Failed', result.error || 'An unknown error occurred.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Verify Phone Number</Text>
      <Text style={styles.subtitle}>
        An OTP has been sent to {identifier}.
      </Text>
      
      <TextInput
        style={styles.input}
        placeholder="Enter 6-digit OTP"
        value={otp}
        onChangeText={setOtp}
        keyboardType="number-pad"
        maxLength={6}
        placeholderTextColor="#aaa"
      />

      <Button title={isLoading ? 'Verifying...' : 'Verify OTP'} onPress={handleVerifyOtp} disabled={isLoading} />
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1d1d1d',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#ffffff',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#aaa',
  },
  input: {
    width: '80%',
    padding: 15,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ffffff',
    borderRadius: 8,
    color: '#ffffff',
    backgroundColor: '#2a2a2a',
    fontSize: 18,
    textAlign: 'center',
  },
  errorText: {
    color: 'red',
    marginTop: 10,
    fontSize: 14,
  },
});

export default OtpVerificationScreen;
