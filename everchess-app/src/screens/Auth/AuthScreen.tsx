import React, { useState } from 'react';
import { useFonts, Sora_400Regular, Sora_600SemiBold } from '@expo-google-fonts/sora';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '@/context/AuthContext';
import { AuthStackParamList } from '@/navigation/types';

const { width: screenWidth } = Dimensions.get('window');
const isLargeScreen = screenWidth >= 768;

// Google Icon Component
const GoogleIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.googleIcon}>G</Text>
  </View>
);

// X (Twitter) Icon Component
const XIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.xIcon}>𝕏</Text>
  </View>
);

// Apple Icon Component
const AppleIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.appleIcon}></Text>
  </View>
);

// Diamond logo component
const DiamondLogo = () => (
  <View style={styles.logoContainer}>
    <View style={styles.diamond} />
  </View>
);

// Everchess Branding Component (for larger screens)
const EverchessBranding = () => (
  <View style={styles.brandingContainer}>
    <View style={styles.brandingContent}>
      <View style={styles.brandingLogoContainer}>
        <View style={styles.brandingDiamond} />
      </View>
      <Text style={styles.brandingTitle}>EVERCHESS</Text>
      <Text style={styles.brandingSubtitle}>ANCIENT MADE MODERN</Text>

      <View style={styles.featuresContainer}>
        <Text style={styles.featuresTitle}>Experience Chess Like Never Before</Text>
        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.checkmark}>✓</Text>
            </View>
            <Text style={styles.featureText}>Collect unique 3D chess sets</Text>
          </View>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.checkmark}>✓</Text>
            </View>
            <Text style={styles.featureText}>Earn rewards through the battlepass</Text>
          </View>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.checkmark}>✓</Text>
            </View>
            <Text style={styles.featureText}>Compete in tournaments and win prizes</Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

type AuthScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'AuthScreen'>;

const AuthScreen: React.FC = () => {
  const [identifier, setIdentifier] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [fontsLoaded] = useFonts({
    Sora_400Regular,
    Sora_600SemiBold,
  });

  const { signInWithSocial, signInWithEmail, signInWithPhone } = useAuth();
  const navigation = useNavigation<AuthScreenNavigationProp>();

  const handleEmailOrPhoneSubmit = async () => {
    if (!identifier.trim()) {
      Alert.alert('Input Required', 'Please enter your email or phone number.');
      return;
    }

    setIsLoading(true);
    try {
      const isEmail = identifier.includes('@');
      const isPhone = /^\+?[1-9]\d{1,14}$/.test(identifier);

      if (isEmail) {
        const result = await signInWithEmail(identifier);
        if (result?.needsVerification) {
          navigation.navigate('OtpVerification', { identifier });
        }
      } else if (isPhone) {
        const result = await signInWithPhone(identifier);
        if (result?.needsVerification) {
          navigation.navigate('OtpVerification', { identifier });
        }
      } else {
        Alert.alert(
          'Invalid Input', 
          'Please enter a valid email or phone number with country code (e.g., +14155552671).'
        );
      }
    } catch (error: any) {
      Alert.alert('Authentication Error', error.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'google' | 'apple' | 'x') => {
    setIsLoading(true);
    try {
      await signInWithSocial(provider);
    } catch (error: any) {
      Alert.alert(
        `Continue with ${provider.charAt(0).toUpperCase() + provider.slice(1)} Failed`, 
        error.message || 'An unexpected error occurred.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!fontsLoaded) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, { justifyContent: 'center' }]}>
          <ActivityIndicator size="large" color="#FFFFFF" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.mainContainer}>
        {/* Auth Form Section */}
        <View style={[styles.authSection, isLargeScreen && styles.authSectionLarge]}>
          <ScrollView
            contentContainerStyle={styles.scrollViewContent}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.container}>
              {/* Mobile logo (only visible on mobile) */}
              {!isLargeScreen && (
                <View style={styles.mobileLogoSection}>
                  <DiamondLogo />
                  <Text style={styles.mobileTitle}>EVERCHESS</Text>
                  <Text style={styles.mobileSubtitle}>ANCIENT MADE MODERN</Text>
                </View>
              )}

              <View style={styles.authCard}>
                <Text style={styles.authTitle}>Sign Up or Login</Text>

                {/* Social Login Buttons */}
                <View style={styles.socialLoginContainer}>
                  <TouchableOpacity
                    style={styles.socialButton}
                    onPress={() => handleSocialSignIn('google')}
                    disabled={isLoading}
                  >
                    <GoogleIcon />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.socialButton}
                    onPress={() => handleSocialSignIn('x')}
                    disabled={isLoading}
                  >
                    <XIcon />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.socialButton}
                    onPress={() => handleSocialSignIn('apple')}
                    disabled={isLoading}
                  >
                    <AppleIcon />
                  </TouchableOpacity>
                </View>

                {/* Email/Phone Input */}
                <View style={styles.inputContainer}>
                  <View style={styles.inputWrapper}>
                    <Text style={styles.inputIcon}>✉</Text>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter email or phone"
                      placeholderTextColor="#888888"
                      value={identifier}
                      onChangeText={setIdentifier}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoCorrect={false}
                      editable={!isLoading}
                    />
                  </View>
                </View>

                {/* Footer */}
                <View style={styles.footer}>
                  <Text style={styles.footerText}>
                    By logging in you agree to our{' '}
                    <Text style={styles.linkText}>Terms & Conditions</Text>
                  </Text>

                  <Text style={styles.poweredByText}>
                    Powered by <Text style={styles.paraText}>Para</Text>
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>

        {/* Branding Section (only visible on larger screens) */}
        {isLargeScreen && <EverchessBranding />}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#2b2b2b' // Changed to gray background
  },
  mainContainer: {
    flex: 1,
    flexDirection: isLargeScreen ? 'row' : 'column',
  },
  authSection: {
    flex: 1,
    backgroundColor: '#2b2b2b',
  },
  authSectionLarge: {
    flex: isLargeScreen ? 0.5 : 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: 40,
  },
  container: {
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  mobileLogoSection: {
    alignItems: 'center',
    marginBottom: 40,
    ...(isLargeScreen && { display: 'none' }),
  },
  logoContainer: {
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamond: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 8,
  },
  mobileTitle: {
    fontSize: 28,
    color: '#FFFFFF',
    marginBottom: 8,
    fontFamily: 'Sora_600SemiBold',
    textAlign: 'center',
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#fcdf3a', // Everchess yellow
    fontFamily: 'Sora_400Regular',
    textAlign: 'center',
  },
  authCard: {
    backgroundColor: '#3a3a3a', // Slightly lighter gray for the card
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  authTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
    textAlign: 'center',
    marginBottom: 32,
  },
  socialLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  socialButton: {
    width: 70,
    height: 70,
    borderRadius: 12,
    backgroundColor: '#4a4a4a',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  socialIconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  googleIcon: {
    fontSize: 24,
    color: '#4285F4',
    fontFamily: 'Sora_600SemiBold',
  },
  xIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  appleIcon: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 24
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  inputIcon: {
    fontSize: 16,
    color: '#888888',
    marginRight: 8,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    color: '#FFFFFF',
    fontFamily: 'Sora_400Regular',
    fontSize: 16,
    paddingVertical: 16,
    paddingHorizontal: 0,
  },
  footer: {
    alignItems: 'center',
    marginTop: 24,
  },
  footerText: {
    color: '#888888',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Sora_400Regular',
    marginBottom: 16,
  },
  linkText: {
    color: '#FFFFFF',
    textDecorationLine: 'underline'
  },
  poweredByText: {
    color: '#888888',
    fontSize: 12,
    fontFamily: 'Sora_400Regular',
  },
  paraText: {
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  // Branding section styles (for larger screens)
  brandingContainer: {
    flex: 0.5,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  brandingContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  brandingLogoContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandingDiamond: {
    width: 80,
    height: 80,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 12,
  },
  brandingTitle: {
    fontSize: 48,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandingSubtitle: {
    fontSize: 18,
    color: '#fcdf3a',
    fontFamily: 'Sora_400Regular',
    textAlign: 'center',
    marginBottom: 48,
  },
  featuresContainer: {
    backgroundColor: '#3a3a3a',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  featuresTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
    textAlign: 'center',
    marginBottom: 24,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(252, 223, 58, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    fontSize: 12,
    color: '#fcdf3a',
    fontFamily: 'Sora_600SemiBold',
  },
  featureText: {
    fontSize: 14,
    color: '#cccccc',
    fontFamily: 'Sora_400Regular',
    flex: 1,
  },
});

export default AuthScreen;
