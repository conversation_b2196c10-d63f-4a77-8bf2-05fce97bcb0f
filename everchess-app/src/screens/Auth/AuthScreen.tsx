import React, { useState } from 'react';
import { useFonts, Sora_400Regular, Sora_600SemiBold } from '@expo-google-fonts/sora';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  SafeAreaView,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAuth } from '@/context/AuthContext';
import { AuthStackParamList } from '@/navigation/types';

// Google Icon Component
const GoogleIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.googleIcon}>G</Text>
  </View>
);

// X (Twitter) Icon Component
const XIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.xIcon}>𝕏</Text>
  </View>
);

// Apple Icon Component
const AppleIcon = () => (
  <View style={styles.socialIconContainer}>
    <Text style={styles.appleIcon}></Text>
  </View>
);

// Diamond logo component
const DiamondLogo = () => (
  <View style={styles.logoContainer}>
    <View style={styles.diamond} />
  </View>
);

type AuthScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'AuthScreen'>;

const AuthScreen: React.FC = () => {
  const [identifier, setIdentifier] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [fontsLoaded] = useFonts({
    Sora_400Regular,
    Sora_600SemiBold,
  });

  const { signInWithSocial, signInWithEmail, signInWithPhone } = useAuth();
  const navigation = useNavigation<AuthScreenNavigationProp>();

  const handleEmailOrPhoneSubmit = async () => {
    if (!identifier.trim()) {
      Alert.alert('Input Required', 'Please enter your email or phone number.');
      return;
    }

    setIsLoading(true);
    try {
      const isEmail = identifier.includes('@');
      const isPhone = /^\+?[1-9]\d{1,14}$/.test(identifier);

      if (isEmail) {
        const result = await signInWithEmail(identifier);
        if (result?.needsVerification) {
          navigation.navigate('OtpVerification', { identifier });
        }
      } else if (isPhone) {
        const result = await signInWithPhone(identifier);
        if (result?.needsVerification) {
          navigation.navigate('OtpVerification', { identifier });
        }
      } else {
        Alert.alert(
          'Invalid Input', 
          'Please enter a valid email or phone number with country code (e.g., +14155552671).'
        );
      }
    } catch (error: any) {
      Alert.alert('Authentication Error', error.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'google' | 'apple' | 'x') => {
    setIsLoading(true);
    try {
      await signInWithSocial(provider);
    } catch (error: any) {
      Alert.alert(
        `Continue with ${provider.charAt(0).toUpperCase() + provider.slice(1)} Failed`, 
        error.message || 'An unexpected error occurred.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!fontsLoaded) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, { justifyContent: 'center' }]}>
          <ActivityIndicator size="large" color="#FFFFFF" />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.scrollViewContent} showsVerticalScrollIndicator={false}>
        <View style={styles.container}>
          <DiamondLogo />
          
          <Text style={styles.title}>Welcome to Everchess</Text>
          <Text style={styles.subtitle}>Continue with your preferred method</Text>

          {/* Social Login Buttons */}
          <View style={styles.socialLoginContainer}>
            <TouchableOpacity
              style={styles.socialButton}
              onPress={() => handleSocialSignIn('google')}
              disabled={isLoading}
            >
              <GoogleIcon />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.socialButton}
              onPress={() => handleSocialSignIn('x')}
              disabled={isLoading}
            >
              <XIcon />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.socialButton}
              onPress={() => handleSocialSignIn('apple')}
              disabled={isLoading}
            >
              <AppleIcon />
            </TouchableOpacity>
          </View>

          <Text style={styles.separatorText}>or</Text>

          {/* Email/Phone Input */}
          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <Text style={styles.inputIcon}>✉</Text>
              <TextInput
                style={styles.input}
                placeholder="Email or phone number"
                placeholderTextColor="#888888"
                value={identifier}
                onChangeText={setIdentifier}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
              />
            </View>
            
            <TouchableOpacity
              style={[styles.emailPhoneButton, isLoading && styles.buttonDisabled]}
              onPress={handleEmailOrPhoneSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.emailPhoneButtonText}>Continue</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing, you agree to our{' '}
            <Text style={styles.linkText}>Terms of Service</Text> and{' '}
            <Text style={styles.linkText}>Privacy Policy</Text>
          </Text>
          
          <Text style={styles.poweredByText}>
            Powered by <Text style={styles.paraText}>Para</Text>
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: { 
    flex: 1, 
    backgroundColor: '#000000' 
  },
  scrollViewContent: { 
    flexGrow: 1, 
    justifyContent: 'center',
    paddingVertical: 40,
  },
  container: {
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamond: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 8,
  },
  title: { 
    fontSize: 28, 
    color: '#FFFFFF', 
    marginBottom: 16, 
    fontFamily: 'Sora_600SemiBold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 40,
    fontFamily: 'Sora_400Regular',
    textAlign: 'center',
  },
  socialLoginContainer: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    width: '100%', 
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  socialButton: {
    width: 80,
    height: 80,
    borderRadius: 16,
    backgroundColor: '#1A1A1A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333333',
  },
  socialIconContainer: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  googleIcon: {
    fontSize: 24,
    color: '#4285F4',
    fontFamily: 'Sora_600SemiBold',
  },
  xIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  appleIcon: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  inputContainer: { 
    width: '100%', 
    marginBottom: 40 
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#333333',
  },
  inputIcon: {
    fontSize: 16,
    color: '#888888',
    marginRight: 8,
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    color: '#FFFFFF',
    fontFamily: 'Sora_400Regular',
    fontSize: 16,
    paddingVertical: 16,
    paddingHorizontal: 0,
  },
  emailPhoneButton: {
    width: '100%',
    height: 48,
    borderRadius: 12,
    backgroundColor: '#1A1A1A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333333',
    marginTop: 16,
  },
  emailPhoneButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  separatorText: { 
    color: '#888888', 
    fontSize: 16, 
    marginVertical: 32,
    fontFamily: 'Sora_400Regular',
  },
  footer: { 
    paddingHorizontal: 32,
    paddingBottom: 40,
    alignItems: 'center' 
  },
  footerText: { 
    color: '#888888', 
    fontSize: 12, 
    textAlign: 'center', 
    fontFamily: 'Sora_400Regular',
    marginBottom: 16,
  },
  linkText: { 
    color: '#FFFFFF', 
    textDecorationLine: 'underline' 
  },
  poweredByText: {
    color: '#888888',
    fontSize: 12,
    fontFamily: 'Sora_400Regular',
  },
  paraText: {
    color: '#FFFFFF',
    fontFamily: 'Sora_600SemiBold',
  },
});

export default AuthScreen;
