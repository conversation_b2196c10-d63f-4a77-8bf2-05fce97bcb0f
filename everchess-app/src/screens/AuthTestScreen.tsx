import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  runAuthTestSuite,
  testGuestAuth,
  testSolanaWalletConnection,
  testSocialLogin,
  logTestResults,
  TestResult,
} from '../utils/authTestUtils';

const AuthTestScreen: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTest = async (testFunction: () => Promise<TestResult | TestResult[]>) => {
    setIsRunning(true);
    try {
      const result = await testFunction();
      const results = Array.isArray(result) ? result : [result];
      setTestResults(results);
      logTestResults(results);
    } catch (error: any) {
      Alert.alert('Test Error', error.message || 'Unknown error occurred');
    } finally {
      setIsRunning(false);
    }
  };

  const TestButton: React.FC<{
    title: string;
    onPress: () => void;
    disabled?: boolean;
  }> = ({ title, onPress, disabled }) => (
    <TouchableOpacity
      style={[styles.testButton, disabled && styles.disabledButton]}
      onPress={onPress}
      disabled={disabled || isRunning}
    >
      <Text style={[styles.testButtonText, disabled && styles.disabledText]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const ResultItem: React.FC<{ result: TestResult }> = ({ result }) => (
    <View style={styles.resultItem}>
      <View style={styles.resultHeader}>
        <Text style={styles.resultTitle}>{result.testName}</Text>
        <Text style={[styles.resultStatus, result.success ? styles.success : styles.failure]}>
          {result.success ? '✅ PASS' : '❌ FAIL'}
        </Text>
      </View>
      {result.error && (
        <Text style={styles.resultError}>Error: {result.error}</Text>
      )}
      {result.details && (
        <Text style={styles.resultDetails}>
          Details: {JSON.stringify(result.details, null, 2)}
        </Text>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>Para SDK Authentication Tests</Text>
        <Text style={styles.subtitle}>
          Test the implemented authentication flows
        </Text>

        <View style={styles.buttonContainer}>
          <TestButton
            title="Run Full Test Suite"
            onPress={() => runTest(runAuthTestSuite)}
          />
          
          <TestButton
            title="Test Guest Auth"
            onPress={() => runTest(testGuestAuth)}
          />
          
          <TestButton
            title="Test Phantom Wallet"
            onPress={() => runTest(() => testSolanaWalletConnection('phantom'))}
          />
          
          <TestButton
            title="Test Solflare Wallet"
            onPress={() => runTest(() => testSolanaWalletConnection('solflare'))}
          />
          
          <TestButton
            title="Test Backpack Wallet"
            onPress={() => runTest(() => testSolanaWalletConnection('backpack'))}
          />
          
          <TestButton
            title="Test Google Login"
            onPress={() => runTest(() => testSocialLogin('google'))}
          />
          
          <TestButton
            title="Test X Login"
            onPress={() => runTest(() => testSocialLogin('x'))}
          />
          
          <TestButton
            title="Test Apple Login"
            onPress={() => runTest(() => testSocialLogin('apple'))}
          />
        </View>

        {isRunning && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Running tests...</Text>
          </View>
        )}

        {testResults.length > 0 && (
          <View style={styles.resultsContainer}>
            <Text style={styles.resultsTitle}>Test Results</Text>
            {testResults.map((result, index) => (
              <ResultItem key={index} result={result} />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  testButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledText: {
    color: '#999',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#007AFF',
  },
  resultsContainer: {
    marginTop: 20,
  },
  resultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  resultStatus: {
    fontSize: 14,
    fontWeight: '600',
  },
  success: {
    color: '#4CAF50',
  },
  failure: {
    color: '#F44336',
  },
  resultError: {
    fontSize: 14,
    color: '#F44336',
    marginBottom: 5,
  },
  resultDetails: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});

export default AuthTestScreen;
