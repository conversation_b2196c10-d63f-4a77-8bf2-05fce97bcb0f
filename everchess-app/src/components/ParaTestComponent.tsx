import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useModal, useAccount } from '@getpara/react-sdk';

export const ParaTestComponent: React.FC = () => {
  const [componentError, setComponentError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Safely use Para hooks with error handling
  let modalHook: ReturnType<typeof useModal> | null = null;
  let accountHook: ReturnType<typeof useAccount> | null = null;

  try {
    modalHook = useModal();
    accountHook = useAccount();
  } catch (error) {
    console.error('🔥 Error using Para hooks:', error);
    if (!componentError) {
      setComponentError(error instanceof Error ? error.message : 'Para hooks error');
    }
  }

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        console.log('🔍 ParaTestComponent initializing...');
        
        // Small delay to ensure Para is fully loaded
        await new Promise(resolve => setTimeout(resolve, 200));
        
        console.log('🔍 Para hooks status:', {
          modal: modalHook ? 'Available' : 'Not available',
          account: accountHook ? 'Available' : 'Not available',
        });

        setIsInitialized(true);
        console.log('✅ ParaTestComponent initialized successfully');
      } catch (error) {
        console.error('🔥 ParaTestComponent initialization failed:', error);
        setComponentError(error instanceof Error ? error.message : 'Component initialization error');
      }
    };

    initializeComponent();
  }, []);

  const handleLogin = () => {
    try {
      console.log('🔍 Opening Para modal...');
      if (modalHook?.openModal) {
        modalHook.openModal();
      } else {
        console.error('🔥 Modal hook not available');
        setComponentError('Modal functionality not available');
      }
    } catch (error) {
      console.error('🔥 Error opening modal:', error);
      setComponentError(error instanceof Error ? error.message : 'Modal error');
    }
  };

  // Show loading state
  if (!isInitialized && !componentError) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Everchess</Text>
        <Text style={styles.subtitle}>Ancient Made Modern</Text>
        <Text style={styles.statusText}>Loading Para Wallet...</Text>
      </View>
    );
  }

  // Show error state
  if (componentError) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Everchess</Text>
        <Text style={styles.subtitle}>Ancient Made Modern</Text>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Para Wallet Error: {componentError}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton} 
            onPress={() => {
              setComponentError(null);
              setIsInitialized(false);
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const account = accountHook?.data;
  const status = accountHook?.status;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Everchess</Text>
      <Text style={styles.subtitle}>Ancient Made Modern</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {status || 'unknown'}
        </Text>
        {account?.isConnected && (
          <Text style={styles.statusText}>
            Connected: {account.isConnected ? 'Yes' : 'No'}
          </Text>
        )}
      </View>

      <TouchableOpacity style={styles.button} onPress={handleLogin}>
        <Text style={styles.buttonText}>
          {account?.isConnected ? 'Manage Account' : 'Connect Wallet'}
        </Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>🏰 3D Chess Experience</Text>
        <Text style={styles.infoText}>🔗 Web3 Integration</Text>
        <Text style={styles.infoText}>🎮 Cross-Platform Gaming</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    padding: 20,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#888888',
    marginBottom: 40,
    textAlign: 'center',
  },
  statusContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#2a2a2a',
    borderRadius: 10,
    minWidth: 250,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 5,
  },
  button: {
    backgroundColor: '#5856D6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 30,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#ff6b6b',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 15,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  infoContainer: {
    alignItems: 'center',
  },
  infoText: {
    color: '#888888',
    fontSize: 16,
    marginBottom: 5,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#2a2a2a',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
});
