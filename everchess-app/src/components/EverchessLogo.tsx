import React from 'react';
import { View, Image, Platform } from 'react-native';

interface EverchessLogoProps {
  size?: number;
  variant?: 'default' | 'minimal'; // For different logo styles
}

export const EverchessLogo: React.FC<EverchessLogoProps> = ({
  size = 48,
  variant = 'default'
}) => {
  // Use different logo sources based on platform and variant
  const getLogoSource = () => {
    // For now, use the same logo for all variants
    // You can add different logo files later
    return require('../../assets/everchess-logo.png');
  };

  return (
    <View style={{
      width: size,
      height: size,
      backgroundColor: '#3a3a3a',
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#4a4a4a',
      // Add subtle shadow for depth
      ...Platform.select({
        ios: {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 3,
        },
        web: {
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        },
      }),
    }}>
      <Image
        source={getLogoSource()}
        style={{
          width: size * 0.7,
          height: size * 0.7,
        }}
        resizeMode="contain"
        // Add accessibility
        accessibilityLabel="Everchess Logo"
        accessibilityRole="image"
      />
    </View>
  );
};
