import React from 'react';
import { View } from 'react-native';

interface EverchessLogoProps {
  size?: number;
  color?: string;
}

export const EverchessLogo: React.FC<EverchessLogoProps> = ({
  size = 48,
  color = '#ffffff'
}) => {
  return (
    <View style={{
      width: size,
      height: size,
      backgroundColor: '#3a3a3a',
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#4a4a4a',
    }}>
      {/* Simple geometric chess piece representation */}
      <View style={{
        width: size * 0.6,
        height: size * 0.6,
        backgroundColor: color,
        borderRadius: 4,
        transform: [{ rotate: '45deg' }],
        position: 'relative',
      }}>
        {/* Inner diamond */}
        <View style={{
          position: 'absolute',
          top: '25%',
          left: '25%',
          width: '50%',
          height: '50%',
          backgroundColor: '#2b2b2b',
          borderRadius: 2,
        }} />
      </View>
    </View>
  );
};
