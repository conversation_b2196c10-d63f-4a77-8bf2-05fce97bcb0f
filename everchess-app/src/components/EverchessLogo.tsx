import React from 'react';
import { View } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface EverchessLogoProps {
  size?: number;
}

export const EverchessLogo: React.FC<EverchessLogoProps> = ({
  size = 48
}) => {
  return (
    <View style={{
      width: size,
      height: size,
      backgroundColor: '#3a3a3a',
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#4a4a4a',
    }}>
      <Svg width={size * 0.7} height={size * 0.7} viewBox="0 0 1024 1024">
        {/* Official Everchess Logo - Red sections */}
        <Path
          fill="#D33523"
          opacity="1.000000"
          stroke="none"
          d="M1025.000000,889.000000 C1025.000000,934.270508 1025.000000,979.541016 1025.000000,1025.000000 C965.312561,1025.000000 905.624939,1025.000000 845.689453,1024.734619 C847.513550,1023.522644 849.585571,1022.576172 850.404663,1022.202026 C850.918701,1018.199219 851.715454,1015.276428 851.541443,1012.412659 C851.372864,1009.638062 850.206238,1006.924072 849.298096,1003.506592 C852.505371,1003.506592 854.494507,1003.506592 856.468567,1003.506592 C852.625732,997.895874 856.851013,993.881470 858.917725,989.320007 C855.743469,989.083191 853.151611,988.889893 850.549500,988.695801 C848.962463,983.613464 854.734680,979.947815 855.406189,980.210327 C860.493042,982.198730 861.139954,978.023438 862.602478,975.575806 C864.276611,972.774292 866.265625,972.881287 868.939270,973.362183 C870.152161,973.580383 871.572266,972.646729 872.898438,972.235596 C871.994202,970.845154 871.269104,969.274841 870.119507,968.131042 C869.415283,967.430237 868.060303,967.383484 867.466125,967.028442 C874.263489,966.719666 880.592285,966.425476 887.389832,966.057556 C900.223083,964.210938 912.505005,967.906128 925.142029,965.305237 C939.364563,962.378113 950.902832,956.778748 959.168213,944.708130 C964.331116,937.168518 965.918579,928.479553 967.932678,920.164307 C968.238098,920.605591 968.456604,920.762817 968.696777,920.872498 C968.733765,920.889404 968.949097,920.688599 968.949707,920.587708 C968.993896,913.062256 969.024719,905.536743 969.397461,897.946228 C971.526489,896.278870 972.373596,893.246155 976.001404,894.804138 C980.462891,896.720215 985.146790,898.118225 988.080627,899.155334 C990.932312,898.042297 992.716125,896.717468 994.436646,896.795105 C999.177307,897.008850 1003.941956,892.871948 1008.669617,896.801086 C1008.846558,896.948120 1009.583801,896.542297 1009.984131,896.278870 C1011.981934,894.964233 1013.954407,893.611084 1016.183289,892.104126 C1016.032104,890.985779 1015.767212,889.026672 1015.502258,887.067505 C1015.815247,887.047424 1016.128235,887.027344 1016.441223,887.007324 C1017.212891,887.931824 1017.984497,888.856323 1019.090637,890.181580 C1019.851501,888.075989 1020.404480,886.545898 1021.142273,884.504211 C1022.755859,886.384644 1023.877930,887.692322 1025.000000,889.000000 z"
        />

        {/* Dark Red sections */}
        <Path
          fill="#D33E1C"
          opacity="1.000000"
          stroke="none"
          d="M1.000000,73.000000 C1.000000,49.057823 1.000000,25.115646 1.000000,1.000000 C37.354042,1.000000 73.708366,1.000000 110.525970,1.368969 C104.853966,9.126450 114.742706,10.258992 115.730476,14.793474 C120.446243,13.986043 120.446243,13.986043 123.529762,19.165846 C122.236351,21.121374 120.952972,23.061739 119.661629,25.014149 C114.226715,20.746378 114.226715,20.746378 109.074669,22.919703 C110.588341,23.236572 112.102013,23.553442 114.854858,24.129723 C112.661118,25.792496 111.100502,26.975386 108.329544,29.075659 C112.199287,29.273705 114.864861,29.410122 117.417015,29.540735 C118.818764,33.741390 120.528191,37.770737 116.060417,41.031845 C115.893692,41.153542 116.013840,41.668274 116.000153,41.999924 C116.000000,42.000000 115.999695,42.000153 115.596237,42.016098 C114.462517,43.226032 112.989738,44.524635 113.140472,45.594498 C113.602280,48.872265 114.687302,52.062229 115.456444,55.005024 C114.227417,56.417435 113.108528,57.703266 111.569611,58.991615 C107.361313,58.487152 103.573059,57.980164 99.535278,57.439781 C99.521568,57.305321 99.419907,56.308086 99.312088,55.250462 C95.617996,55.946461 94.561378,58.016499 95.058914,61.127644 C95.006859,61.112690 95.114738,61.103477 94.807053,61.091629 C94.019730,61.394421 93.540092,61.709064 92.831390,62.298073 C79.912582,67.002518 71.505165,76.416847 63.810951,87.162872 C63.023701,88.267860 62.556652,89.243973 62.072620,90.427773 C62.055637,90.635460 61.890282,91.018013 61.668865,91.195366 C61.315773,91.910576 61.184097,92.448425 61.050545,93.005203 C61.048668,93.024124 61.021370,93.050613 60.753983,93.242523 C60.341549,94.284721 60.196499,95.134995 60.047447,96.003647 C60.043449,96.022018 60.025600,96.055840 59.676231,96.185760 C58.225880,95.797913 56.481987,95.526680 56.167740,94.707222 C55.635967,93.320503 56.007870,91.587257 56.329132,89.947632 C56.957932,89.208847 57.257450,88.522133 57.556969,87.835411 C57.196987,87.709862 56.837002,87.584312 56.477020,87.458771 C56.318062,88.305946 56.159107,89.153122 55.617100,90.000793 C49.973492,89.801399 49.845905,92.744591 51.864113,97.005592 C48.296204,95.952225 45.452076,95.112541 43.685780,94.591072 C43.871422,89.905121 44.039104,85.672485 44.206787,81.439842 C43.313972,81.439842 42.421154,81.439842 42.440605,81.439842 C41.817684,83.782402 41.796997,86.800339 40.782082,87.183281 C35.545765,89.159035 36.725777,83.192513 34.316017,81.114235 C32.957123,82.025139 31.712736,82.859291 30.468348,83.693443 C30.048731,83.513962 29.629116,83.334480 29.209499,83.154999 C29.641493,80.945732 30.073486,78.736458 30.505480,76.527184 C30.019743,76.334229 29.534008,76.141273 29.048271,75.948318 C27.943663,79.129410 26.839052,82.310501 25.734444,85.491585 C25.520678,85.522514 25.306911,85.553436 25.093145,85.584358 C25.093145,83.345032 25.093145,81.105698 25.093145,80.107262 C22.161753,78.202545 20.180691,76.915321 17.749096,75.335358 C14.734862,76.044632 18.117363,84.347519 10.734589,82.236794 C11.932012,79.556328 12.955928,77.264259 13.999450,74.928299 C9.395274,74.245331 5.197637,73.622665 1.000000,73.000000 z"
        />

        {/* Teal sections */}
        <Path
          fill="#0B706F"
          opacity="1.000000"
          stroke="none"
          d="M17.000000,1025.000000 C11.684817,1025.000000 6.369635,1025.000000 1.000000,1025.000000 C1.000000,987.645813 1.000000,950.291565 1.333589,912.469116 C2.111963,911.999817 2.556748,911.998718 3.036271,912.279419 C3.373113,912.881409 3.675218,913.201599 3.977345,913.521729 C4.156708,913.314941 4.336049,913.108093 4.515371,912.901245 C4.009689,912.601685 3.504025,912.302063 3.006785,911.629272 C3.066015,910.929871 2.999550,910.496460 3.185143,910.293396 C6.298679,906.886902 9.378291,903.443787 12.642079,900.185730 C13.227450,899.601379 15.243179,899.343567 15.515648,899.719604 C19.056578,904.606140 20.394709,900.290344 23.337767,896.443848 C23.857088,898.413208 24.224863,899.807922 24.221901,899.796692 C26.335302,897.498901 27.784409,894.675720 30.017113,893.757141 C34.585533,891.877686 37.856773,889.599060 38.075256,884.236694 C38.118359,883.178833 38.984215,882.154541 39.470638,881.114746 C39.886398,881.178345 40.302162,881.241882 40.717922,881.305481 C41.150536,883.162842 41.583149,885.020142 42.015762,886.877502 C42.411156,887.227173 42.806545,887.576782 43.201939,887.926453 C45.743904,880.925293 51.442898,877.560486 56.980110,874.463013 C56.996441,891.045715 56.881302,907.160950 57.110878,923.271240 C57.152378,926.183533 58.609135,929.075684 59.655388,932.222351 C61.901966,936.979797 63.905666,941.492004 65.620956,945.871460 C64.725677,946.169006 63.683170,946.519836 63.587757,947.043335 C63.185379,949.250977 63.065403,951.510010 62.829266,953.879333 C60.152512,953.053040 58.415287,952.516785 56.170341,951.823853 C56.562599,953.798645 56.781223,954.899353 56.999924,956.000000 C57.000000,956.000000 57.000153,956.000000 56.605186,956.001709 C55.214260,956.403137 53.477673,956.632324 53.340115,957.229675 C52.287430,961.801636 47.868500,965.836182 51.369602,971.291565 C52.062912,972.371887 50.288960,975.042236 49.639946,976.974976 C49.284451,978.033569 48.991886,979.126770 48.507038,980.124756 C47.994976,981.178833 47.499878,982.600708 46.607014,983.009705 C45.701328,983.424561 44.111557,983.128601 43.192417,982.552246 C41.727196,981.633545 40.569256,980.224670 39.351189,979.088196 C35.794777,981.870117 35.245152,983.989197 38.983406,986.654236 C41.606014,988.523865 42.174927,991.176941 39.079071,993.649963 C36.722172,995.532776 34.353790,997.489868 32.416538,999.774963 C31.311865,1001.078064 30.864975,1003.064148 30.496454,1004.821228 C30.256945,1005.963135 30.692135,1007.246521 30.825047,1008.466553 C30.395815,1008.860596 29.966581,1009.254700 29.537350,1009.648743 C26.499693,1007.648499 23.462036,1005.648193 19.507267,1003.044006 C20.468016,1005.375671 20.726183,1007.118347 21.711477,1008.196838 C25.597424,1012.450500 22.185678,1016.422607 19.904335,1018.378845 C17.402178,1020.524353 16.966366,1022.348755 17.000000,1025.000000 z"
        />

        {/* Gold sections */}
        <Path
          fill="#D7A220"
          opacity="1.000000"
          stroke="none"
          d="M0.999999,520.000000 C1.000000,478.645782 1.000000,437.291534 1.323006,395.498657 C4.018014,396.499237 6.390771,397.941376 6.810419,394.060150 C8.431804,395.733276 9.771900,398.323822 11.677854,398.844971 C16.429985,400.144409 20.236856,401.994507 21.097765,407.999054 C23.447697,404.915283 24.148170,400.990631 28.982487,402.649628 C28.021341,404.206543 27.228006,405.491608 26.567890,406.560913 C30.208378,407.849030 33.563862,409.036316 36.185909,409.964081 C39.038445,408.707733 43.230522,405.557556 44.598396,406.501251 C48.855843,409.438293 52.149998,406.705505 55.498745,406.178619 C56.013283,408.250549 56.477371,410.119324 56.965523,412.457581 C56.974705,434.291656 56.959824,455.656281 56.678604,477.291626 C56.208408,479.684143 56.004551,481.805908 55.801746,483.916748 C51.881157,481.816498 49.750198,483.715057 45.249077,493.459290 C43.123039,492.902496 40.966076,492.337616 37.432781,491.412292 C38.575432,496.596649 39.536087,500.955231 40.791801,506.652557 C37.455383,504.194031 35.516033,502.764984 33.332970,501.156311 C32.149815,504.014160 31.049541,506.671783 29.713123,509.899841 C28.233452,507.725403 27.302618,506.357483 26.319185,504.912292 C25.699438,506.462250 25.167799,507.791840 24.539402,509.363403 C24.130350,508.377960 23.836487,507.670013 23.118980,505.941437 C22.746799,517.529968 13.149608,517.538940 6.638444,521.202271 C5.077987,522.080261 3.519190,522.961121 2.152667,523.731812 C2.027105,523.325317 1.513552,521.662659 0.999999,520.000000 z"
        />
      </Svg>
    </View>
  );
};
