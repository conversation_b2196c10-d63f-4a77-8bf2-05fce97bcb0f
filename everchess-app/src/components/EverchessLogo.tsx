import React from 'react';
import { View } from 'react-native';
import Svg, { Polygon, Circle, Rect } from 'react-native-svg';

interface EverchessLogoProps {
  size?: number;
}

export const EverchessLogo: React.FC<EverchessLogoProps> = ({
  size = 48
}) => {
  return (
    <View style={{
      width: size,
      height: size,
      backgroundColor: '#3a3a3a',
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#4a4a4a',
    }}>
      <Svg width={size * 0.7} height={size * 0.7} viewBox="0 0 100 100">
        {/* Simplified Everchess Logo - Optimized for React Native */}

        {/* Main Diamond Shape - Red */}
        <Polygon
          points="50,10 80,30 80,70 50,90 20,70 20,30"
          fill="#D33523"
          stroke="#D33E1C"
          strokeWidth="1"
        />

        {/* Inner Diamond - Teal */}
        <Polygon
          points="50,25 65,35 65,65 50,75 35,65 35,35"
          fill="#0B706F"
          stroke="#117264"
          strokeWidth="0.5"
        />

        {/* Center Accent - Gold */}
        <Circle
          cx="50"
          cy="50"
          r="8"
          fill="#D7A220"
          stroke="#DA921C"
          strokeWidth="0.5"
        />

        {/* Top Accent - Dark Red */}
        <Rect
          x="45"
          y="15"
          width="10"
          height="8"
          fill="#D33E1C"
          rx="2"
        />

        {/* Side Accents - Teal variations */}
        <Circle cx="30" cy="40" r="3" fill="#0A6B72" />
        <Circle cx="70" cy="40" r="3" fill="#0A6B72" />
        <Circle cx="30" cy="60" r="3" fill="#127263" />
        <Circle cx="70" cy="60" r="3" fill="#127263" />
      </Svg>
    </View>
  );
};
