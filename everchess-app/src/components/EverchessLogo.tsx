import React from 'react';
import { View, Image } from 'react-native';

interface EverchessLogoProps {
  size?: number;
}

export const EverchessLogo: React.FC<EverchessLogoProps> = ({
  size = 48
}) => {
  return (
    <View style={{
      width: size,
      height: size,
      backgroundColor: '#3a3a3a',
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#4a4a4a',
    }}>
      <Image
        source={require('../../assets/Everchess_Logo.svg')}
        style={{
          width: size * 0.7,
          height: size * 0.7,
        }}
        resizeMode="contain"
      />
    </View>
  );
};
