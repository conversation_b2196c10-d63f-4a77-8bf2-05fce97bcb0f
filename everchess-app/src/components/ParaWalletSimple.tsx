import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

// Create QueryClient instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

interface ParaWalletProviderProps {
  children: React.ReactNode;
  apiKey: string;
  projectId?: string;
}

// Main Para Provider Component - Simplified and Safe
export const ParaWalletProvider: React.FC<ParaWalletProviderProps> = ({ 
  children, 
  apiKey, 
  projectId 
}) => {
  const [ParaComponents, setParaComponents] = useState<any>(null);
  const [Environment, setEnvironment] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadParaSDK = async () => {
      try {
        console.log('🔍 Loading Para SDK components...');
        
        // Dynamic import of Para SDK
        const paraModule = await import('@getpara/react-sdk');
        
        console.log('✅ Para SDK loaded, available exports:', Object.keys(paraModule));
        
        // Extract the components we need
        const { ParaProvider, ParaModal, Environment: EnvEnum } = paraModule;
        
        if (!ParaProvider || !ParaModal || !EnvEnum) {
          throw new Error('Required Para SDK components not found');
        }

        setParaComponents({ ParaProvider, ParaModal });
        setEnvironment(EnvEnum);
        
      } catch (err) {
        console.error('🔥 Failed to load Para SDK:', err);
        setError(err instanceof Error ? err.message : 'Failed to load Para SDK');
      }
    };

    loadParaSDK();
  }, []);

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Para SDK Error: {error}</Text>
      </View>
    );
  }

  if (!ParaComponents || !Environment) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading Para SDK...</Text>
      </View>
    );
  }

  // Get the correct environment enum value
  const getEnvironment = () => {
    const envString = process.env.REACT_APP_PARA_ENVIRONMENT || 'beta';
    
    switch (envString.toLowerCase()) {
      case 'production':
      case 'prod':
        return Environment.PROD;
      case 'dev':
      case 'development':
        return Environment.DEV;
      case 'sandbox':
        return Environment.SANDBOX;
      case 'beta':
      case 'staging':
      default:
        return Environment.BETA;
    }
  };

  const paraConfig = {
    env: getEnvironment(),
    apiKey: apiKey,
  };

  console.log('🔍 Final Para configuration:', {
    env: paraConfig.env,
    apiKey: apiKey ? 'SET' : 'MISSING',
    projectId: projectId ? 'SET' : 'MISSING',
  });

  const { ParaProvider, ParaModal } = ParaComponents;

  return (
    <QueryClientProvider client={queryClient}>
      <ParaProvider
        paraClientConfig={paraConfig}
        callbacks={{
          onLogin: (event: any) => {
            console.log('✅ Para Login successful:', event.detail);
          },
          onLogout: (event: any) => {
            console.log('✅ Para Logout:', event.detail);
          },
          onSignMessage: (event: any) => {
            console.log('✅ Para Message Signed:', event.detail);
          },
        }}
      >
        {children}
        <ParaModal />
      </ParaProvider>
    </QueryClientProvider>
  );
};

// Simplified Para Wallet Button - No dynamic hook usage
export const ParaWalletButton: React.FC = () => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [authError, setAuthError] = useState<string | null>(null);

  // Simple connection handler that opens Para modal
  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionStatus('connecting');
    setAuthError(null);
    
    try {
      console.log('🔍 Opening Para authentication modal...');
      
      // Get Para modal from window object (Para SDK adds it globally)
      const paraModal = (window as any).para?.modal;
      
      if (paraModal) {
        paraModal.open();
        console.log('✅ Para modal opened successfully');
      } else {
        // Fallback: Try to trigger Para modal through DOM event
        const event = new CustomEvent('para-open-modal');
        window.dispatchEvent(event);
        console.log('✅ Para modal event dispatched');
      }
      
    } catch (error) {
      console.error('🔥 Failed to open Para modal:', error);
      setAuthError(error instanceof Error ? error.message : 'Failed to open authentication');
      setConnectionStatus('disconnected');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    setConnectionStatus('disconnected');
    setAuthError(null);
    console.log('✅ Disconnected from Para');
  };

  // Listen for Para authentication events
  useEffect(() => {
    const handleParaLogin = (event: any) => {
      console.log('✅ Para login detected:', event);
      setConnectionStatus('connected');
      setAuthError(null);
    };

    const handleParaLogout = (event: any) => {
      console.log('✅ Para logout detected:', event);
      setConnectionStatus('disconnected');
      setAuthError(null);
    };

    // Listen for Para events
    window.addEventListener('para-login', handleParaLogin);
    window.addEventListener('para-logout', handleParaLogout);

    return () => {
      window.removeEventListener('para-login', handleParaLogin);
      window.removeEventListener('para-logout', handleParaLogout);
    };
  }, []);

  if (connectionStatus === 'connected') {
    return (
      <View style={styles.container}>
        <Text style={styles.titleText}>🎉 Connected to Para Wallet</Text>
        <Text style={styles.successText}>✅ Authentication Successful</Text>
        <Text style={styles.infoText}>
          You can now access all Everchess features
        </Text>
        <TouchableOpacity style={styles.disconnectButton} onPress={handleDisconnect}>
          <Text style={styles.buttonText}>Disconnect</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.titleText}>Connect to Everchess</Text>
      <Text style={styles.infoText}>
        Choose your preferred authentication method
      </Text>
      
      <TouchableOpacity
        style={[styles.connectButton, isConnecting && styles.disabledButton]}
        onPress={handleConnect}
        disabled={isConnecting}
      >
        <Text style={styles.buttonText}>
          {isConnecting ? 'Opening Para Wallet...' : '🔐 Connect with Para Wallet'}
        </Text>
      </TouchableOpacity>
      
      <View style={styles.authMethodsContainer}>
        <Text style={styles.authMethodsTitle}>Available Authentication Methods:</Text>
        <View style={styles.authMethodsList}>
          <Text style={styles.authMethod}>📧 Email Authentication</Text>
          <Text style={styles.authMethod}>📱 Phone Number (SMS)</Text>
          <Text style={styles.authMethod}>🔍 Google Account</Text>
          <Text style={styles.authMethod}>🍎 Apple ID</Text>
          <Text style={styles.authMethod}>🎮 Discord</Text>
          <Text style={styles.authMethod}>🐦 Twitter/X</Text>
          <Text style={styles.authMethod}>🔗 External Wallets</Text>
        </View>
      </View>
      
      {authError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>⚠️ {authError}</Text>
        </View>
      )}
      
      <Text style={styles.helpText}>
        Para Wallet provides secure, non-custodial authentication with multiple options.
        Your keys are encrypted and only you have access.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    padding: 12,
    backgroundColor: '#ffebee',
    borderRadius: 6,
    margin: 8,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  successText: {
    fontSize: 16,
    color: '#22c55e',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  authMethodsContainer: {
    marginTop: 16,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 6,
  },
  authMethodsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  authMethodsList: {
    alignItems: 'flex-start',
  },
  authMethod: {
    fontSize: 13,
    color: '#555',
    marginBottom: 4,
    paddingLeft: 8,
  },
  helpText: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    textAlign: 'center',
  },
  connectButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
    alignItems: 'center',
  },
  disconnectButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 12,
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
