import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Social Login Icons Component
// This will be updated with your SVG files once uploaded

interface SocialIconProps {
  type: 'google' | 'twitter' | 'apple';
  size?: number;
  color?: string;
}

export const SocialIcon: React.FC<SocialIconProps> = ({ 
  type, 
  size = 20, 
  color = '#ffffff' 
}) => {
  const renderIcon = () => {
    switch (type) {
      case 'google':
        // Temporary Google icon - will be replaced with your SVG
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <Text style={[styles.iconText, { fontSize: size * 0.8, color }]}>G</Text>
          </View>
        );
      
      case 'twitter':
        // Temporary X/Twitter icon - will be replaced with your SVG
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <Text style={[styles.iconText, { fontSize: size * 0.7, color }]}>𝕏</Text>
          </View>
        );
      
      case 'apple':
        // Temporary Apple icon - will be replaced with your SVG
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <Text style={[styles.iconText, { fontSize: size * 0.8, color }]}></Text>
          </View>
        );
      
      default:
        return null;
    }
  };

  return renderIcon();
};

// Google Icon Component (placeholder for SVG)
export const GoogleIcon: React.FC<{ size?: number; color?: string }> = ({ 
  size = 20, 
  color = '#ffffff' 
}) => {
  // This will be replaced with your Google SVG
  return (
    <View style={[styles.svgContainer, { width: size, height: size }]}>
      <Text style={[styles.svgText, { fontSize: size * 0.8, color }]}>G</Text>
    </View>
  );
};

// Twitter/X Icon Component (placeholder for SVG)
export const TwitterIcon: React.FC<{ size?: number; color?: string }> = ({ 
  size = 20, 
  color = '#ffffff' 
}) => {
  // This will be replaced with your Twitter/X SVG
  return (
    <View style={[styles.svgContainer, { width: size, height: size }]}>
      <Text style={[styles.svgText, { fontSize: size * 0.7, color }]}>𝕏</Text>
    </View>
  );
};

// Apple Icon Component (placeholder for SVG)
export const AppleIcon: React.FC<{ size?: number; color?: string }> = ({ 
  size = 20, 
  color = '#ffffff' 
}) => {
  // This will be replaced with your Apple SVG
  return (
    <View style={[styles.svgContainer, { width: size, height: size }]}>
      <Text style={[styles.svgText, { fontSize: size * 0.8, color }]}></Text>
    </View>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  svgContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  svgText: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

// Instructions for SVG Integration:
// 
// 1. Install react-native-svg: npm install react-native-svg
// 2. For web: npm install react-native-svg-web
// 3. Replace the placeholder components above with actual SVG components
// 
// Example SVG component structure:
// 
// import Svg, { Path } from 'react-native-svg';
// 
// export const GoogleIconSVG: React.FC<{ size?: number; color?: string }> = ({ 
//   size = 20, 
//   color = '#ffffff' 
// }) => (
//   <Svg width={size} height={size} viewBox="0 0 24 24">
//     <Path
//       d="YOUR_SVG_PATH_DATA_HERE"
//       fill={color}
//     />
//   </Svg>
// );
