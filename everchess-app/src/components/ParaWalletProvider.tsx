import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ParaProvider, ParaModal, Environment as ParaWebEnvironment } from '@getpara/react-sdk';
import { paraConfig, validateParaConfig, ParaEnvironment } from '../config/para.config';

// Create a QueryClient instance for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

interface ParaWalletProviderProps {
  children: React.ReactNode;
}

export const ParaWalletProvider: React.FC<ParaWalletProviderProps> = ({ children }) => {
  const [isParaReady, setIsParaReady] = useState(false);
  const [paraError, setParaError] = useState<string | null>(null);

  useEffect(() => {
    const initializePara = async () => {
      try {
        console.log('🔍 Initializing Para Wallet Provider...');
        
        // Validate Para configuration
        if (!validateParaConfig(paraConfig)) {
          throw new Error('Para configuration validation failed');
        }

        // Small delay to ensure all dependencies are loaded
        await new Promise(resolve => setTimeout(resolve, 100));
        
        console.log('✅ Para Wallet Provider initialized successfully');
        setIsParaReady(true);
      } catch (error) {
        console.error('🔥 Para Wallet Provider initialization failed:', error);
        setParaError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    initializePara();
  }, []);

  // Show loading state while Para is initializing
  if (!isParaReady && !paraError) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Initializing Para Wallet...</Text>
      </View>
    );
  }

  // Show error state if Para failed to initialize
  if (paraError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Para Wallet initialization failed: {paraError}
        </Text>
        <Text style={styles.errorSubtext}>
          Please check your configuration and try again.
        </Text>
      </View>
    );
  }

  // Map our environment enum to Para's environment enum
  const paraEnvironment = paraConfig.environment === ParaEnvironment.PRODUCTION
    ? ParaWebEnvironment.PRODUCTION
    : ParaWebEnvironment.BETA;

  console.log('🔍 Para environment:', paraEnvironment);
  console.log('🔍 Para config:', { apiKey: paraConfig.apiKey ? 'SET' : 'MISSING', env: paraEnvironment });

  return (
    <QueryClientProvider client={queryClient}>
      <ParaProvider
        paraClientConfig={{
          apiKey: paraConfig.apiKey,
          env: paraEnvironment,
        }}
      >
        {children}
        <ParaModal />
      </ParaProvider>
    </QueryClientProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    padding: 20,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  errorSubtext: {
    color: '#888888',
    fontSize: 14,
    textAlign: 'center',
  },
});
