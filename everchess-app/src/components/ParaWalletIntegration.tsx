import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { QueryClient, QueryClientProvider, useQueryClient } from '@tanstack/react-query';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

// Types
interface ParaContextType {
  isInitialized: boolean;
  error: string | null;
  paraSDK: any | null;
  connectWallet: () => Promise<void>;
  disconnect: () => Promise<void>;
  account: any | null;
}

interface ParaProviderProps {
  children: ReactNode;
  apiKey: string;
  projectId?: string;
}

// Context
const ParaContext = createContext<ParaContextType | null>(null);

// Custom hook
export const usePara = () => {
  const context = useContext(ParaContext);
  if (!context) {
    throw new Error('usePara must be used within ParaProvider');
  }
  return context;
};

// Para Provider Component
const ParaProviderInner: React.FC<ParaProviderProps> = ({ children, apiKey, projectId }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paraSDK, setParaSDK] = useState<any>(null);
  const [account, setAccount] = useState<any>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    initializePara();
  }, [apiKey, projectId]);

  const initializePara = async () => {
    try {
      setError(null);
      console.log('🔍 Initializing Para SDK...');
      
      // Dynamic import of Para SDK to avoid SSR issues
      const paraModule = await import('@getpara/react-sdk');
      console.log('✅ Para SDK module loaded:', Object.keys(paraModule));
      
      // Try different import patterns
      const ParaSDK = paraModule.default || paraModule.Para || paraModule;
      
      if (!ParaSDK) {
        throw new Error('Para SDK not found in module');
      }

      console.log('🔍 Creating Para instance with config:', {
        apiKey: apiKey ? 'SET' : 'MISSING',
        projectId: projectId ? 'SET' : 'MISSING',
        environment: process.env.NODE_ENV === 'production' ? 'production' : 'beta',
      });

      // Initialize Para SDK
      const paraInstance = new ParaSDK({
        apiKey,
        projectId,
        environment: process.env.NODE_ENV === 'production' ? 'production' : 'beta',
      });

      setParaSDK(paraInstance);
      setIsInitialized(true);
      console.log('✅ Para SDK initialized successfully');
      
      // Listen for account changes if available
      if (paraInstance.on) {
        paraInstance.on('accountChanged', (newAccount: any) => {
          console.log('🔍 Account changed:', newAccount);
          setAccount(newAccount);
        });
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize Para SDK';
      setError(errorMessage);
      console.error('🔥 Para SDK Initialization Error:', err);
    }
  };

  const connectWallet = async () => {
    if (!paraSDK) {
      throw new Error('Para SDK not initialized');
    }

    try {
      console.log('🔍 Connecting wallet...');
      const result = await paraSDK.connect();
      console.log('✅ Wallet connected:', result);
      setAccount(result.account || result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect wallet';
      setError(errorMessage);
      console.error('🔥 Wallet connection error:', err);
      throw err;
    }
  };

  const disconnect = async () => {
    if (!paraSDK) return;
    
    try {
      console.log('🔍 Disconnecting wallet...');
      await paraSDK.disconnect();
      setAccount(null);
      console.log('✅ Wallet disconnected');
    } catch (err) {
      console.error('🔥 Disconnect error:', err);
    }
  };

  const contextValue: ParaContextType = {
    isInitialized,
    error,
    paraSDK,
    connectWallet,
    disconnect,
    account,
  };

  return (
    <ParaContext.Provider value={contextValue}>
      {children}
    </ParaContext.Provider>
  );
};

// Main Para Provider with Query Client
export const ParaProvider: React.FC<ParaProviderProps> = ({ children, apiKey, projectId }) => {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        retry: 3,
        staleTime: 5 * 60 * 1000, // 5 minutes
        refetchOnWindowFocus: false,
      },
      mutations: {
        retry: 1,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      <ParaProviderInner apiKey={apiKey} projectId={projectId}>
        {children}
      </ParaProviderInner>
    </QueryClientProvider>
  );
};

// Para Wallet Component
export const ParaWalletButton: React.FC = () => {
  const { isInitialized, error, connectWallet, disconnect, account } = usePara();
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      await connectWallet();
    } catch (err) {
      console.error('Connection failed:', err);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await disconnect();
    } catch (err) {
      console.error('Disconnect failed:', err);
    }
  };

  if (!isInitialized) {
    return (
      <View style={styles.container}>
        <Text style={styles.statusText}>Initializing Para Wallet...</Text>
        {error && <Text style={styles.errorText}>Error: {error}</Text>}
      </View>
    );
  }

  if (account) {
    return (
      <View style={styles.container}>
        <Text style={styles.successText}>Connected: {account.address || 'Connected'}</Text>
        <TouchableOpacity style={styles.disconnectButton} onPress={handleDisconnect}>
          <Text style={styles.buttonText}>Disconnect</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.connectButton, isConnecting && styles.disabledButton]}
        onPress={handleConnect}
        disabled={isConnecting}
      >
        <Text style={styles.buttonText}>
          {isConnecting ? 'Connecting...' : 'Connect Para Wallet'}
        </Text>
      </TouchableOpacity>
      {error && <Text style={styles.errorText}>Error: {error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 8,
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  successText: {
    fontSize: 16,
    color: '#22c55e',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 8,
  },
  connectButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
    alignItems: 'center',
  },
  disconnectButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
