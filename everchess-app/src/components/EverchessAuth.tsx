import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { useAccount, useWallet, useModal, useClient } from '@getpara/react-sdk';
import { GoogleIcon, TwitterIcon, AppleIcon } from './SocialIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Custom Everchess Authentication Component matching the exact design
export const EverchessAuth: React.FC = () => {
  const { data: account } = useAccount();
  const { data: wallet } = useWallet();
  const para = useClient();
  const { openModal } = useModal();

  const [emailOrPhone, setEmailOrPhone] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);

  const handleSocialLogin = async (provider: 'google' | 'twitter' | 'apple') => {
    setIsConnecting(true);
    try {
      console.log(`🔍 Opening Para modal for ${provider} login...`);
      openModal();
    } catch (error) {
      console.error(`🔥 Failed to open ${provider} login:`, error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEmailPhoneLogin = async () => {
    if (!emailOrPhone.trim()) return;
    
    setIsConnecting(true);
    try {
      console.log('🔍 Opening Para modal for email/phone login...');
      openModal();
    } catch (error) {
      console.error('🔥 Failed to open email/phone login:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleLogout = async () => {
    try {
      await para.logout({ preservePregenWallets: false });
      console.log('✅ Logged out successfully');
    } catch (error) {
      console.error('🔥 Failed to logout:', error);
    }
  };

  // Connected state - show user info
  if (account?.isConnected) {
    const displayAddress = wallet 
      ? para.getDisplayAddress(wallet.id, {
          truncate: true,
          addressType: wallet.type,
        })
      : 'No Wallet Selected';

    return (
      <View style={styles.connectedContainer}>
        <View style={styles.connectedModal}>
          <View style={styles.starIcon}>
            <Text style={styles.starText}>✦</Text>
          </View>
          
          <Text style={styles.connectedTitle}>Welcome to Everchess</Text>
          <Text style={styles.connectedSubtitle}>Successfully Connected</Text>
          
          <View style={styles.walletInfo}>
            <Text style={styles.walletLabel}>Wallet Address:</Text>
            <Text style={styles.walletAddress}>{displayAddress}</Text>
          </View>

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authentication state - exact design from images
  return (
    <View style={styles.container}>
      <View style={styles.modal}>
        {/* Star Icon */}
        <View style={styles.starIcon}>
          <Text style={styles.starText}>✦</Text>
        </View>

        {/* Title */}
        <Text style={styles.title}>Sign Up or Login</Text>

        {/* Social Login Buttons */}
        <View style={styles.socialButtonsContainer}>
          {/* Google Button */}
          <TouchableOpacity
            style={[styles.socialButton, isConnecting && styles.disabledButton]}
            onPress={() => handleSocialLogin('google')}
            disabled={isConnecting}
          >
            <GoogleIcon size={24} color="#ffffff" />
          </TouchableOpacity>

          {/* X/Twitter Button */}
          <TouchableOpacity
            style={[styles.socialButton, isConnecting && styles.disabledButton]}
            onPress={() => handleSocialLogin('twitter')}
            disabled={isConnecting}
          >
            <TwitterIcon size={24} color="#ffffff" />
          </TouchableOpacity>

          {/* Apple Button */}
          <TouchableOpacity
            style={[styles.socialButton, isConnecting && styles.disabledButton]}
            onPress={() => handleSocialLogin('apple')}
            disabled={isConnecting}
          >
            <AppleIcon size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Email/Phone Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputIconContainer}>
            <Text style={styles.inputIcon}>✉</Text>
            <Text style={styles.inputIcon}>📱</Text>
          </View>
          <TextInput
            style={styles.input}
            placeholder="Enter email or phone"
            placeholderTextColor="#888888"
            value={emailOrPhone}
            onChangeText={setEmailOrPhone}
            keyboardType="email-address"
            autoCapitalize="none"
            onSubmitEditing={handleEmailPhoneLogin}
          />
        </View>
      </View>

      {/* Terms and Powered by */}
      <View style={styles.footer}>
        <Text style={styles.termsText}>
          By logging in you agree to our{' '}
          <Text style={styles.termsLink}>Terms & Conditions</Text>
        </Text>
        <Text style={styles.poweredBy}>
          Powered by <Text style={styles.paraText}>✦ Para</Text>
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#2b2b2b',
    borderRadius: 16,
    padding: 32,
    width: Math.min(400, screenWidth - 40),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  starIcon: {
    marginBottom: 24,
  },
  starText: {
    fontSize: 32,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 32,
    textAlign: 'center',
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  socialButton: {
    width: 56,
    height: 56,
    backgroundColor: '#404040',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#555555',
  },
  disabledButton: {
    opacity: 0.6,
  },
  socialButtonText: {
    fontSize: 20,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#404040',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#555555',
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: '100%',
  },
  inputIconContainer: {
    flexDirection: 'row',
    marginRight: 12,
    gap: 4,
  },
  inputIcon: {
    fontSize: 16,
    color: '#888888',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
    paddingVertical: 4,
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
    gap: 8,
  },
  termsText: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  termsLink: {
    color: '#ffffff',
    textDecorationLine: 'underline',
  },
  poweredBy: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  paraText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  // Connected state styles
  connectedContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  connectedModal: {
    backgroundColor: '#2b2b2b',
    borderRadius: 16,
    padding: 32,
    width: Math.min(400, screenWidth - 40),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  connectedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  connectedSubtitle: {
    fontSize: 16,
    color: '#22c55e',
    marginBottom: 24,
    textAlign: 'center',
  },
  walletInfo: {
    backgroundColor: '#404040',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 24,
  },
  walletLabel: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'monospace',
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
