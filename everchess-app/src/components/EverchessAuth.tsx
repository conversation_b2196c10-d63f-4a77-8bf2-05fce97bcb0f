import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { useAccount, useWallet, useModal, useClient } from '@getpara/react-sdk';
import { OAuthMethod } from '@getpara/react-sdk';
import { GoogleIcon, TwitterIcon, AppleIcon } from './SocialIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isLargeScreen = screenWidth >= 768;

// Custom Everchess Authentication Component matching the exact design
export const EverchessAuth: React.FC = () => {
  const { data: account } = useAccount();
  const { data: wallet } = useWallet();
  const para = useClient();
  const { openModal } = useModal();

  const [emailOrPhone, setEmailOrPhone] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [showAuthMethod, setShowAuthMethod] = useState(false);
  const [authMethod, setAuthMethod] = useState<'passkey' | 'password' | 'verify' | 'choose' | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [password, setPassword] = useState('');
  const [userAuthMethods, setUserAuthMethods] = useState<string[]>([]);

  const handleSocialLogin = async (provider: 'google' | 'twitter' | 'apple') => {
    setIsConnecting(true);
    try {
      console.log(`🔍 Starting ${provider} OAuth authentication...`);

      // Map provider to Para OAuth method
      let oAuthMethod: OAuthMethod;
      switch (provider) {
        case 'google':
          oAuthMethod = OAuthMethod.GOOGLE;
          break;
        case 'twitter':
          oAuthMethod = OAuthMethod.TWITTER;
          break;
        case 'apple':
          oAuthMethod = OAuthMethod.APPLE;
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }

      // Get OAuth URL and open popup
      const oAuthURL = await para.getOAuthURL(oAuthMethod);
      const popup = window.open(oAuthURL, "oAuthPopup", "popup=true,width=500,height=600");

      if (!popup) {
        throw new Error('Failed to open OAuth popup. Please allow popups for this site.');
      }

      // Wait for OAuth completion
      const { email, userExists } = await para.waitForOAuth();
      console.log(`✅ OAuth completed for ${email}, userExists: ${userExists}`);

      // Handle authentication flow based on user existence
      if (userExists) {
        // Existing user - login flow
        const authUrl = await para.initiateUserLogin({ email, useShortUrl: false });
        const loginPopup = window.open(authUrl, "loginPopup", "popup=true,width=500,height=600");

        const { needsWallet } = await para.waitForLoginAndSetup({ popupWindow: loginPopup });

        if (needsWallet) {
          await para.createWallet({ skipDistribute: false });
        }
      } else {
        // New user - signup flow
        const authUrl = await para.getSetUpBiometricsURL({ authType: "email", isForNewDevice: false });
        const signupPopup = window.open(authUrl, "signUpPopup", "popup=true,width=500,height=600");

        const result = await para.waitForPasskeyAndCreateWallet();

        if ("needsWallet" in result && result.needsWallet) {
          await para.createWallet({ skipDistribute: false });
        }
      }

      console.log(`✅ ${provider} authentication completed successfully`);
    } catch (error) {
      console.error(`🔥 Failed to authenticate with ${provider}:`, error);
      alert(`Authentication failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEmailPhoneLogin = async () => {
    if (!emailOrPhone.trim()) {
      alert('Please enter an email address');
      return;
    }

    setIsConnecting(true);
    try {
      console.log('🔍 Starting email authentication for:', emailOrPhone);

      // Check if user exists and get their auth methods
      const userExists = await para.checkIfUserExists({ email: emailOrPhone });
      console.log(`User exists: ${userExists}`);

      if (userExists) {
        // Get user's preferred authentication methods
        try {
          const userInfo = await para.getUserInfo({ email: emailOrPhone });
          const authMethods = userInfo?.authMethods || [];
          setUserAuthMethods(authMethods);

          // If user has only one auth method, show it directly
          if (authMethods.includes('passkey') && !authMethods.includes('password')) {
            setAuthMethod('passkey');
          } else if (authMethods.includes('password') && !authMethods.includes('passkey')) {
            setAuthMethod('password');
          } else {
            // User has multiple methods, let them choose
            setAuthMethod('choose');
          }
        } catch (error) {
          // Fallback to passkey if we can't get user info
          console.log('Could not get user auth methods, defaulting to passkey');
          setAuthMethod('passkey');
        }

        setShowAuthMethod(true);
      } else {
        // New user - show verification first, then choice
        await para.createUser({ email: emailOrPhone });
        setAuthMethod('verify');
        setShowAuthMethod(true);
      }
    } catch (error) {
      console.error('🔥 Failed to authenticate with email:', error);
      alert(`Authentication failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleVerificationCode = async () => {
    if (!verificationCode.trim()) {
      alert('Please enter the verification code');
      return;
    }

    setIsConnecting(true);
    try {
      console.log('🔍 Verifying email with code:', verificationCode);

      // Verify email - for new users, show choice of auth method
      const result = await para.verifyEmail({ verificationCode });

      if (result) {
        // New user verified - show choice between passkey and password
        setAuthMethod('choose');
      }
    } catch (error) {
      console.error('🔥 Failed to verify email:', error);
      alert(`Verification failed: ${error.message || 'Invalid verification code'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleAuthMethodChoice = (method: 'passkey' | 'password') => {
    setAuthMethod(method);
  };

  const handlePasskeyAuth = async () => {
    setIsConnecting(true);
    try {
      console.log('🔍 Starting passkey authentication...');

      // Use Para's direct passkey authentication
      const result = await para.authenticateWithPasskey({ email: emailOrPhone });

      if (result.success) {
        console.log('✅ Passkey authentication successful');
        // Handle successful authentication
        handleAuthSuccess();
      }
    } catch (error) {
      console.error('🔥 Passkey authentication failed:', error);
      alert(`Passkey authentication failed: ${error.message || 'Unknown error'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handlePasswordAuth = async () => {
    if (!password.trim()) {
      alert('Please enter your password');
      return;
    }

    setIsConnecting(true);
    try {
      console.log('🔍 Starting password authentication...');

      // Use Para's direct password authentication
      const result = await para.authenticateWithPassword({
        email: emailOrPhone,
        password: password
      });

      if (result.success) {
        console.log('✅ Password authentication successful');
        handleAuthSuccess();
      }
    } catch (error) {
      console.error('🔥 Password authentication failed:', error);
      alert(`Password authentication failed: ${error.message || 'Invalid credentials'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleAuthSuccess = async () => {
    try {
      // Check if wallet creation is needed
      const wallets = await para.getWallets();
      if (Object.keys(wallets).length === 0) {
        await para.createWallet({ skipDistribute: false });
      }

      // Reset auth state
      setShowAuthMethod(false);
      setAuthMethod(null);
      setVerificationCode('');
      setPassword('');
      setEmailOrPhone('');

      console.log('✅ Authentication completed successfully');
    } catch (error) {
      console.error('🔥 Failed to complete authentication setup:', error);
    }
  };

  const handleBackToLogin = () => {
    setShowAuthMethod(false);
    setAuthMethod(null);
    setVerificationCode('');
    setPassword('');
    setEmailOrPhone('');
    setUserAuthMethods([]);
  };

  // Listen for authentication completion
  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      // Listen for Para authentication completion
      if (event.origin.includes('getpara.com') || event.origin.includes('para.com')) {
        if (event.data.type === 'PARA_AUTH_SUCCESS') {
          console.log('✅ Para authentication completed successfully');

          // Check if wallet creation is needed
          try {
            const wallets = await para.getWallets();
            if (Object.keys(wallets).length === 0) {
              await para.createWallet({ skipDistribute: false });
            }

            // Reset auth state
            setAuthStep(null);
            setAuthUrl(null);
            setVerificationCode('');
            setEmailOrPhone('');
          } catch (error) {
            console.error('🔥 Failed to complete wallet setup:', error);
          }
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [para]);

  const handleLogout = async () => {
    try {
      await para.logout({ preservePregenWallets: false });
      console.log('✅ Logged out successfully');
    } catch (error) {
      console.error('🔥 Failed to logout:', error);
    }
  };

  // Connected state - show user info
  if (account?.isConnected) {
    const displayAddress = wallet 
      ? para.getDisplayAddress(wallet.id, {
          truncate: true,
          addressType: wallet.type,
        })
      : 'No Wallet Selected';

    return (
      <View style={styles.connectedContainer}>
        <View style={styles.connectedModal}>
          <View style={styles.starIcon}>
            <Text style={styles.starText}>✦</Text>
          </View>
          
          <Text style={styles.connectedTitle}>Welcome to Everchess</Text>
          <Text style={styles.connectedSubtitle}>Successfully Connected</Text>
          
          <View style={styles.walletInfo}>
            <Text style={styles.walletLabel}>Wallet Address:</Text>
            <Text style={styles.walletAddress}>{displayAddress}</Text>
          </View>

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authentication state - production ready design
  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        {/* Auth Form Section */}
        <View style={[styles.authSection, isLargeScreen && styles.authSectionLarge]}>
          <View style={styles.authContent}>
            {/* Mobile logo (only visible on mobile) */}
            {!isLargeScreen && (
              <View style={styles.mobileLogoSection}>
                <View style={styles.logoContainer}>
                  <View style={styles.diamond} />
                </View>
                <Text style={styles.mobileTitle}>EVERCHESS</Text>
                <Text style={styles.mobileSubtitle}>ANCIENT MADE MODERN</Text>
              </View>
            )}

            <View style={styles.authCard}>
              <Text style={styles.authTitle}>Sign Up or Login</Text>

              {/* Social Login Buttons */}
              <View style={styles.socialButtonsContainer}>
                {/* Google Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('google')}
                  disabled={isConnecting}
                >
                  <GoogleIcon size={24} color="#4285F4" />
                </TouchableOpacity>

                {/* X/Twitter Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('twitter')}
                  disabled={isConnecting}
                >
                  <TwitterIcon size={20} color="#ffffff" />
                </TouchableOpacity>

                {/* Apple Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('apple')}
                  disabled={isConnecting}
                >
                  <AppleIcon size={24} color="#ffffff" />
                </TouchableOpacity>
              </View>

              {/* Email/Phone Input */}
              <View style={styles.inputContainer}>
                <View style={styles.inputIconContainer}>
                  <Text style={styles.inputIcon}>✉</Text>
                  <Text style={styles.inputIcon}>📱</Text>
                </View>
                <TextInput
                  style={styles.input}
                  placeholder="Enter email or phone"
                  placeholderTextColor="#888888"
                  value={emailOrPhone}
                  onChangeText={setEmailOrPhone}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  onSubmitEditing={handleEmailPhoneLogin}
                />
              </View>

              {/* Inline Authentication Method - appears right below email input */}
              {showAuthMethod && (
                <View style={styles.inlineAuthContainer}>
                  {/* Email Verification Step */}
                  {authMethod === 'verify' && (
                    <>
                      <Text style={styles.inlineAuthTitle}>
                        Check your email for verification code
                      </Text>
                      <View style={styles.inlineInputContainer}>
                        <TextInput
                          style={styles.inlineInput}
                          placeholder="Enter verification code"
                          placeholderTextColor="#888888"
                          value={verificationCode}
                          onChangeText={setVerificationCode}
                          keyboardType="number-pad"
                          autoCapitalize="none"
                          onSubmitEditing={handleVerificationCode}
                        />
                        <TouchableOpacity
                          style={[styles.inlineButton, isConnecting && styles.disabledButton]}
                          onPress={handleVerificationCode}
                          disabled={isConnecting || !verificationCode.trim()}
                        >
                          <Text style={styles.inlineButtonText}>
                            {isConnecting ? '...' : '→'}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </>
                  )}

                  {/* Choice between Passkey and Password (for new users or users with multiple methods) */}
                  {authMethod === 'choose' && (
                    <>
                      <Text style={styles.inlineAuthTitle}>
                        Choose your authentication method
                      </Text>
                      <View style={styles.authChoiceContainer}>
                        <TouchableOpacity
                          style={styles.authChoiceButton}
                          onPress={() => handleAuthMethodChoice('passkey')}
                        >
                          <Text style={styles.authChoiceIcon}>🔐</Text>
                          <Text style={styles.authChoiceText}>Use Passkey</Text>
                          <Text style={styles.authChoiceSubtext}>Biometric or device authentication</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={styles.authChoiceButton}
                          onPress={() => handleAuthMethodChoice('password')}
                        >
                          <Text style={styles.authChoiceIcon}>🔑</Text>
                          <Text style={styles.authChoiceText}>Use Password</Text>
                          <Text style={styles.authChoiceSubtext}>Traditional password login</Text>
                        </TouchableOpacity>
                      </View>
                    </>
                  )}

                  {/* Passkey Authentication */}
                  {authMethod === 'passkey' && (
                    <>
                      <Text style={styles.inlineAuthTitle}>
                        Authenticate with your passkey
                      </Text>
                      <TouchableOpacity
                        style={[styles.passkeyButton, isConnecting && styles.disabledButton]}
                        onPress={handlePasskeyAuth}
                        disabled={isConnecting}
                      >
                        <Text style={styles.passkeyIcon}>🔐</Text>
                        <Text style={styles.passkeyButtonText}>
                          {isConnecting ? 'Authenticating...' : 'Use Passkey'}
                        </Text>
                      </TouchableOpacity>
                      <Text style={styles.passkeySubtext}>
                        Use your fingerprint, face, or device PIN
                      </Text>
                    </>
                  )}

                  {/* Password Authentication */}
                  {authMethod === 'password' && (
                    <>
                      <Text style={styles.inlineAuthTitle}>
                        Enter your password
                      </Text>
                      <View style={styles.inlineInputContainer}>
                        <TextInput
                          style={styles.inlineInput}
                          placeholder="Enter password"
                          placeholderTextColor="#888888"
                          value={password}
                          onChangeText={setPassword}
                          secureTextEntry
                          autoCapitalize="none"
                          onSubmitEditing={handlePasswordAuth}
                        />
                        <TouchableOpacity
                          style={[styles.inlineButton, isConnecting && styles.disabledButton]}
                          onPress={handlePasswordAuth}
                          disabled={isConnecting || !password.trim()}
                        >
                          <Text style={styles.inlineButtonText}>
                            {isConnecting ? '...' : '→'}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </>
                  )}

                  <TouchableOpacity style={styles.resetButton} onPress={handleBackToLogin}>
                    <Text style={styles.resetButtonText}>Start over</Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Terms and Powered by */}
              <View style={styles.footer}>
                <Text style={styles.termsText}>
                  By logging in you agree to our{' '}
                  <Text style={styles.termsLink}>Terms & Conditions</Text>
                </Text>
                <Text style={styles.poweredBy}>
                  Powered by <Text style={styles.paraText}>Para</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Branding Section (only visible on larger screens) */}
        {isLargeScreen && (
          <View style={styles.brandingContainer}>
            <View style={styles.brandingContent}>
              <View style={styles.brandingLogoContainer}>
                <View style={styles.brandingDiamond} />
              </View>
              <Text style={styles.brandingTitle}>EVERCHESS</Text>
              <Text style={styles.brandingSubtitle}>ANCIENT MADE MODERN</Text>

              <View style={styles.featuresContainer}>
                <Text style={styles.featuresTitle}>Experience Chess Like Never Before</Text>
                <View style={styles.featuresList}>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Collect unique 3D chess sets</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Earn rewards through the battlepass</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Compete in tournaments and win prizes</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2b2b2b', // Gray background for entire screen
    minHeight: '100vh',
  },
  mainContainer: {
    flex: 1,
    flexDirection: isLargeScreen ? 'row' : 'column',
  },
  authSection: {
    flex: 1,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authSectionLarge: {
    flex: isLargeScreen ? 0.5 : 1,
  },
  authContent: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  mobileLogoSection: {
    alignItems: 'center',
    marginBottom: 40,
    ...(isLargeScreen && { display: 'none' }),
  },
  logoContainer: {
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamond: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 8,
  },
  mobileTitle: {
    fontSize: 28,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#fcdf3a', // Everchess yellow
    textAlign: 'center',
  },
  authCard: {
    backgroundColor: '#3a3a3a', // Slightly lighter gray for the card
    borderRadius: 16,
    padding: 24,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  authTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 32,
  },
  authSubtitle: {
    fontSize: 14,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
    padding: 8,
  },
  backButtonText: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: '500',
  },
  continueButton: {
    backgroundColor: '#fcdf3a',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
    marginTop: 16,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2b2b2b',
  },
  authFrame: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#2b2b2b',
    marginTop: 16,
  },
  // Inline authentication styles
  inlineAuthContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#404040',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  inlineAuthTitle: {
    fontSize: 14,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 12,
  },
  inlineInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  inlineInput: {
    flex: 1,
    backgroundColor: '#4a4a4a',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: '#ffffff',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  inlineButton: {
    backgroundColor: '#fcdf3a',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    minWidth: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inlineButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2b2b2b',
  },
  compactAuthFrame: {
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#2b2b2b',
    marginBottom: 12,
  },
  resetButton: {
    alignSelf: 'center',
    padding: 8,
  },
  resetButtonText: {
    fontSize: 12,
    color: '#888888',
    textDecorationLine: 'underline',
  },
  // Authentication choice styles
  authChoiceContainer: {
    gap: 12,
  },
  authChoiceButton: {
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#5a5a5a',
    alignItems: 'center',
  },
  authChoiceIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  authChoiceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
  },
  authChoiceSubtext: {
    fontSize: 12,
    color: '#cccccc',
    textAlign: 'center',
  },
  // Passkey authentication styles
  passkeyButton: {
    backgroundColor: '#fcdf3a',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  passkeyIcon: {
    fontSize: 20,
  },
  passkeyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2b2b2b',
  },
  passkeySubtext: {
    fontSize: 12,
    color: '#cccccc',
    textAlign: 'center',
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  socialButton: {
    width: 70,
    height: 70,
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  disabledButton: {
    opacity: 0.6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5a5a5a',
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: '100%',
    marginBottom: 24,
  },
  inputIconContainer: {
    flexDirection: 'row',
    marginRight: 12,
    gap: 4,
  },
  inputIcon: {
    fontSize: 16,
    color: '#888888',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
    paddingVertical: 4,
  },
  footer: {
    alignItems: 'center',
    gap: 8,
  },
  termsText: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  termsLink: {
    color: '#ffffff',
    textDecorationLine: 'underline',
  },
  poweredBy: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  paraText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  // Branding section styles (for larger screens)
  brandingContainer: {
    flex: 0.5,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  brandingContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  brandingLogoContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandingDiamond: {
    width: 80,
    height: 80,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 12,
  },
  brandingTitle: {
    fontSize: 48,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandingSubtitle: {
    fontSize: 18,
    color: '#fcdf3a',
    textAlign: 'center',
    marginBottom: 48,
  },
  featuresContainer: {
    backgroundColor: '#3a3a3a',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  featuresTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 24,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(252, 223, 58, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    fontSize: 12,
    color: '#fcdf3a',
    fontWeight: 'bold',
  },
  featureText: {
    fontSize: 14,
    color: '#cccccc',
    flex: 1,
  },
  // Connected state styles
  connectedContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  connectedModal: {
    backgroundColor: '#2b2b2b',
    borderRadius: 16,
    padding: 32,
    width: Math.min(400, screenWidth - 40),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  connectedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  connectedSubtitle: {
    fontSize: 16,
    color: '#22c55e',
    marginBottom: 24,
    textAlign: 'center',
  },
  walletInfo: {
    backgroundColor: '#404040',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 24,
  },
  walletLabel: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'monospace',
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
