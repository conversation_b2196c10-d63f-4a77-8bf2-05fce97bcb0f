import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { useAccount, useWallet, useModal, useClient } from '@getpara/react-sdk';
import { OAuthMethod } from '@getpara/react-sdk';
import { GoogleIcon, TwitterIcon, AppleIcon } from './SocialIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isLargeScreen = screenWidth >= 768;

// Custom Everchess Authentication Component matching the exact design
export const EverchessAuth: React.FC = () => {
  const { data: account } = useAccount();
  const { data: wallet } = useWallet();
  const para = useClient();
  const { openModal } = useModal();

  const [emailOrPhone, setEmailOrPhone] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [showAuthOptions, setShowAuthOptions] = useState(false);

  const handleSocialLogin = async (provider: 'google' | 'twitter' | 'apple') => {
    setIsConnecting(true);
    try {
      console.log(`🔍 Starting ${provider} OAuth authentication...`);

      // Map provider to Para OAuth method
      let oAuthMethod: OAuthMethod;
      switch (provider) {
        case 'google':
          oAuthMethod = OAuthMethod.GOOGLE;
          break;
        case 'twitter':
          oAuthMethod = OAuthMethod.TWITTER;
          break;
        case 'apple':
          oAuthMethod = OAuthMethod.APPLE;
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }

      // Get OAuth URL and open popup
      const oAuthURL = await para.getOAuthURL(oAuthMethod);
      const popup = window.open(oAuthURL, "oAuthPopup", "popup=true,width=500,height=600");

      if (!popup) {
        throw new Error('Failed to open OAuth popup. Please allow popups for this site.');
      }

      // Wait for OAuth completion
      const { email, userExists } = await para.waitForOAuth();
      console.log(`✅ OAuth completed for ${email}, userExists: ${userExists}`);

      // Handle authentication flow based on user existence
      if (userExists) {
        // Existing user - login flow
        const authUrl = await para.initiateUserLogin({ email, useShortUrl: false });
        const loginPopup = window.open(authUrl, "loginPopup", "popup=true,width=500,height=600");

        const { needsWallet } = await para.waitForLoginAndSetup({ popupWindow: loginPopup });

        if (needsWallet) {
          await para.createWallet({ skipDistribute: false });
        }
      } else {
        // New user - signup flow
        const authUrl = await para.getSetUpBiometricsURL({ authType: "email", isForNewDevice: false });
        const signupPopup = window.open(authUrl, "signUpPopup", "popup=true,width=500,height=600");

        const result = await para.waitForPasskeyAndCreateWallet();

        if ("needsWallet" in result && result.needsWallet) {
          await para.createWallet({ skipDistribute: false });
        }
      }

      console.log(`✅ ${provider} authentication completed successfully`);
    } catch (error) {
      console.error(`🔥 Failed to authenticate with ${provider}:`, error);
      alert(`Authentication failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  // Show authentication options after email is entered
  const handleEmailPhoneLogin = async () => {
    if (!emailOrPhone.trim()) {
      alert('Please enter an email address');
      return;
    }

    // Show password/passkey options
    setShowAuthOptions(true);
  };

  // Handle passkey authentication using Para SDK custom UI methods
  const handlePasskeyAuth = async () => {
    setIsConnecting(true);
    try {
      console.log('🔍 Starting passkey authentication for:', emailOrPhone);

      // Check if user exists first
      const isExistingUser = await para.checkIfUserExists({ email: emailOrPhone });

      if (isExistingUser) {
        // Existing user - initiate login flow
        console.log('🔍 Existing user detected, initiating login...');
        const webAuthUrlForLogin = await para.initiateUserLogin({
          email: emailOrPhone,
          useShortUrl: false
        });

        // Open authentication popup for passkey
        const popupWindow = window.open(webAuthUrlForLogin, "passkeyLoginPopup", "popup=true,width=500,height=600");

        // Wait for login completion and wallet setup
        const { needsWallet } = await para.waitForLoginAndSetup({ popupWindow });

        // Create wallet if needed
        if (needsWallet) {
          await para.createWallet({ type: 'EVM', skipDistribute: false });
        }

        console.log('✅ Passkey authentication successful');
      } else {
        // New user - create user and setup passkey
        console.log('🔍 New user detected, creating account...');
        await para.createUser({ email: emailOrPhone });

        // User will receive verification email
        alert('Please check your email for verification code, then try again.');
        setShowAuthOptions(false); // Go back to email input
      }

    } catch (error) {
      console.error('🔥 Failed passkey authentication:', error);
      alert(`Passkey authentication failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle password authentication using Para SDK custom UI methods
  const handlePasswordAuth = async () => {
    setIsConnecting(true);
    try {
      console.log('🔍 Starting password authentication for:', emailOrPhone);

      // Check if user exists first
      const isExistingUser = await para.checkIfUserExists({ email: emailOrPhone });

      if (isExistingUser) {
        // Existing user - initiate login flow
        console.log('🔍 Existing user detected, initiating login...');
        const webAuthUrlForLogin = await para.initiateUserLogin({
          email: emailOrPhone,
          useShortUrl: false
        });

        // Open authentication popup for password
        const popupWindow = window.open(webAuthUrlForLogin, "passwordLoginPopup", "popup=true,width=500,height=600");

        // Wait for login completion and wallet setup
        const { needsWallet } = await para.waitForLoginAndSetup({ popupWindow });

        // Create wallet if needed
        if (needsWallet) {
          await para.createWallet({ type: 'EVM', skipDistribute: false });
        }

        console.log('✅ Password authentication successful');
      } else {
        // New user - create user and setup password
        console.log('🔍 New user detected, creating account...');
        await para.createUser({ email: emailOrPhone });

        // User will receive verification email
        alert('Please check your email for verification code, then try again.');
        setShowAuthOptions(false); // Go back to email input
      }

    } catch (error) {
      console.error('🔥 Failed password authentication:', error);
      alert(`Password authentication failed: ${error.message || 'Unknown error occurred'}`);
    } finally {
      setIsConnecting(false);
    }
  };



  // Monitor authentication state changes
  useEffect(() => {
    console.log('🔍 Account state changed:', account);
    if (account?.isConnected) {
      console.log('✅ User is now connected!');
      // Reset our custom UI state when user successfully authenticates
      setShowAuthOptions(false);
      setEmailOrPhone('');
      setIsConnecting(false);
    }
  }, [account]);

  // Monitor wallet state changes
  useEffect(() => {
    console.log('🔍 Wallet state changed:', wallet);
  }, [wallet]);

  const handleLogout = async () => {
    try {
      await para.logout({ preservePregenWallets: false });
      console.log('✅ Logged out successfully');
    } catch (error) {
      console.error('🔥 Failed to logout:', error);
    }
  };

  // Connected state - show user info
  if (account?.isConnected) {
    const displayAddress = wallet 
      ? para.getDisplayAddress(wallet.id, {
          truncate: true,
          addressType: wallet.type,
        })
      : 'No Wallet Selected';

    return (
      <View style={styles.connectedContainer}>
        <View style={styles.connectedModal}>
          <View style={styles.starIcon}>
            <Text style={styles.starText}>✦</Text>
          </View>
          
          <Text style={styles.connectedTitle}>Welcome to Everchess</Text>
          <Text style={styles.connectedSubtitle}>Successfully Connected</Text>
          
          <View style={styles.walletInfo}>
            <Text style={styles.walletLabel}>Wallet Address:</Text>
            <Text style={styles.walletAddress}>{displayAddress}</Text>
          </View>

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authentication state - production ready design
  return (
    <View style={styles.container}>
      <View style={styles.unifiedContainer}>
        <View style={styles.unifiedContent}>
          {/* Logo, Title and Tagline */}
          <View style={styles.brandingLogoContainer}>
            <View style={styles.brandingDiamond} />
          </View>
          <Text style={styles.brandingTitle}>EVERCHESS</Text>
          <Text style={styles.brandingSubtitle}>ANCIENT MADE MODERN</Text>

          {/* Auth Card */}
          <View style={styles.authCard}>
              <Text style={styles.authTitle}>Sign Up or Login</Text>

              {/* Social Login Buttons */}
              <View style={styles.socialButtonsContainer}>
                {/* Google Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('google')}
                  disabled={isConnecting}
                >
                  <GoogleIcon size={24} color="#4285F4" />
                </TouchableOpacity>

                {/* X/Twitter Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('twitter')}
                  disabled={isConnecting}
                >
                  <TwitterIcon size={20} color="#ffffff" />
                </TouchableOpacity>

                {/* Apple Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('apple')}
                  disabled={isConnecting}
                >
                  <AppleIcon size={24} color="#ffffff" />
                </TouchableOpacity>
              </View>

              {/* Email/Phone Input */}
              <View style={styles.inputContainer}>
                <View style={styles.inputIconContainer}>
                  <Text style={styles.inputIcon}>@</Text>
                </View>
                <TextInput
                  style={styles.input}
                  placeholder="Enter email or phone"
                  placeholderTextColor="#888888"
                  value={emailOrPhone}
                  onChangeText={setEmailOrPhone}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  onSubmitEditing={handleEmailPhoneLogin}
                />
              </View>

              {/* Continue Button or Auth Options */}
              {!showAuthOptions ? (
                <TouchableOpacity
                  style={[styles.continueButton, (isConnecting || !emailOrPhone.trim()) && styles.disabledButton]}
                  onPress={handleEmailPhoneLogin}
                  disabled={isConnecting || !emailOrPhone.trim()}
                >
                  <Text style={styles.continueButtonText}>
                    {isConnecting ? 'Opening authentication...' : 'Continue'}
                  </Text>
                </TouchableOpacity>
              ) : (
                <View style={styles.authOptionsContainer}>
                  <TouchableOpacity
                    style={[styles.authOptionButton, isConnecting && styles.disabledButton]}
                    onPress={handlePasskeyAuth}
                    disabled={isConnecting}
                  >
                    <Text style={styles.authOptionButtonText}>
                      {isConnecting ? 'Opening...' : 'Passkey'}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.authOptionButton, isConnecting && styles.disabledButton]}
                    onPress={handlePasswordAuth}
                    disabled={isConnecting}
                  >
                    <Text style={styles.authOptionButtonText}>
                      {isConnecting ? 'Opening...' : 'Password'}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Terms and Powered by */}
              <View style={styles.footer}>
                <Text style={styles.termsText}>
                  By logging in you agree to our{' '}
                  <Text style={styles.termsLink}>Terms & Conditions</Text>
                </Text>
                <Text style={styles.poweredBy}>
                  Powered by <Text style={styles.paraText}>Para</Text>
                </Text>
              </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2b2b2b', // Gray background for entire screen
    minHeight: '100vh',
  },
  unifiedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    width: '100%',
    maxWidth: isLargeScreen ? '90%' : '100%',
    alignSelf: 'center',
  },
  unifiedContent: {
    width: '100%',
    maxWidth: isLargeScreen ? 600 : 400,
    alignItems: 'center',
  },
  authSection: {
    flex: 1,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authSectionLarge: {
    flex: 1,
  },
  authContent: {
    width: '100%',
    maxWidth: 520,
    alignItems: 'center',
  },
  mobileLogoSection: {
    alignItems: 'center',
    marginBottom: 40,
    ...(isLargeScreen && { display: 'none' }),
  },
  logoContainer: {
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamond: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 8,
  },
  mobileTitle: {
    fontSize: 28,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#fcdf3a', // Everchess yellow
    textAlign: 'center',
  },
  authCard: {
    backgroundColor: '#3a3a3a', // Slightly lighter gray for the card
    borderRadius: 20,
    padding: 32,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#4a4a4a',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)',
    elevation: 8,
    marginTop: 32,
  },
  authTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 32,
  },
  authSubtitle: {
    fontSize: 14,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
    padding: 8,
  },
  backButtonText: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: '500',
  },
  continueButton: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
    marginTop: 16,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2b2b2b',
  },
  // Auth options styles
  authOptionsContainer: {
    width: '100%',
    marginTop: 16,
    gap: 12,
  },
  authOptionButton: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
  },
  authOptionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2b2b2b',
  },
  authFrame: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#2b2b2b',
    marginTop: 16,
  },
  // Inline authentication styles
  inlineAuthContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#404040',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  inlineAuthTitle: {
    fontSize: 14,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 12,
  },
  inlineInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  inlineInput: {
    flex: 1,
    backgroundColor: '#4a4a4a',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: '#ffffff',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  inlineButton: {
    backgroundColor: '#fcdf3a',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    minWidth: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inlineButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2b2b2b',
  },
  compactAuthFrame: {
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#2b2b2b',
    marginBottom: 12,
  },
  resetButton: {
    alignSelf: 'center',
    padding: 8,
  },
  resetButtonText: {
    fontSize: 12,
    color: '#888888',
    textDecorationLine: 'underline',
  },
  // Authenticating state styles
  authenticatingContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#fcdf3a',
    marginHorizontal: 3,
  },
  dot1: {
    // Animation will be handled by CSS if needed
  },
  dot2: {
    // Animation will be handled by CSS if needed
  },
  dot3: {
    // Animation will be handled by CSS if needed
  },
  authenticatingText: {
    fontSize: 12,
    color: '#cccccc',
    textAlign: 'center',
    lineHeight: 16,
  },
  // Authentication choice styles
  authChoiceContainer: {
    gap: 12,
  },
  authChoiceButton: {
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#5a5a5a',
    alignItems: 'center',
  },
  authChoiceIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  authChoiceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
  },
  authChoiceSubtext: {
    fontSize: 12,
    color: '#cccccc',
    textAlign: 'center',
  },
  // Passkey authentication styles
  passkeyButton: {
    backgroundColor: '#fcdf3a',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    marginBottom: 8,
  },
  passkeyIcon: {
    fontSize: 20,
  },
  passkeyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2b2b2b',
  },
  passkeySubtext: {
    fontSize: 12,
    color: '#cccccc',
    textAlign: 'center',
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  socialButton: {
    width: 70,
    height: 70,
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  disabledButton: {
    opacity: 0.6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5a5a5a',
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: '100%',
    marginBottom: 24,
  },
  inputIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  inputIcon: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: '300',
    opacity: 0.8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
    paddingVertical: 4,
  },
  footer: {
    alignItems: 'center',
    gap: 8,
  },
  termsText: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  termsLink: {
    color: '#ffffff',
    textDecorationLine: 'underline',
  },
  poweredBy: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  paraText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  // Branding section styles (for larger screens)
  brandingContainer: {
    flex: 1,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  brandingContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  brandingLogoContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandingDiamond: {
    width: 80,
    height: 80,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 12,
  },
  brandingTitle: {
    fontSize: 48,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandingSubtitle: {
    fontSize: 18,
    color: '#fcdf3a',
    textAlign: 'center',
    marginBottom: 48,
  },
  featuresContainer: {
    backgroundColor: '#3a3a3a',
    borderRadius: 20,
    padding: 32,
    width: '100%',
    maxWidth: 480,
    borderWidth: 1,
    borderColor: '#4a4a4a',
    alignItems: 'center',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)',
    elevation: 8,
  },
  featuresTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 32,
    marginBottom: 24,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(252, 223, 58, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    fontSize: 12,
    color: '#fcdf3a',
    fontWeight: 'bold',
  },
  featureText: {
    fontSize: 14,
    color: '#cccccc',
    flex: 1,
  },
  // Connected state styles
  connectedContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  connectedModal: {
    backgroundColor: '#2b2b2b',
    borderRadius: 16,
    padding: 32,
    width: Math.min(400, screenWidth - 40),
    alignItems: 'center',
    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)',
    elevation: 8,
  },
  connectedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  connectedSubtitle: {
    fontSize: 16,
    color: '#22c55e',
    marginBottom: 24,
    textAlign: 'center',
  },
  walletInfo: {
    backgroundColor: '#404040',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 24,
  },
  walletLabel: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'monospace',
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
