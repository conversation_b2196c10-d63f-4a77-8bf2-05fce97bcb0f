import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { useAccount, useWallet, useModal, useClient } from '@getpara/react-sdk';
import { GoogleIcon, TwitterIcon, AppleIcon } from './SocialIcons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isLargeScreen = screenWidth >= 768;

// Custom Everchess Authentication Component matching the exact design
export const EverchessAuth: React.FC = () => {
  const { data: account } = useAccount();
  const { data: wallet } = useWallet();
  const para = useClient();
  const { openModal } = useModal();

  const [emailOrPhone, setEmailOrPhone] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);

  const handleSocialLogin = async (provider: 'google' | 'twitter' | 'apple') => {
    setIsConnecting(true);
    try {
      console.log(`🔍 Opening Para modal for ${provider} login...`);
      openModal();
    } catch (error) {
      console.error(`🔥 Failed to open ${provider} login:`, error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEmailPhoneLogin = async () => {
    if (!emailOrPhone.trim()) return;
    
    setIsConnecting(true);
    try {
      console.log('🔍 Opening Para modal for email/phone login...');
      openModal();
    } catch (error) {
      console.error('🔥 Failed to open email/phone login:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleLogout = async () => {
    try {
      await para.logout({ preservePregenWallets: false });
      console.log('✅ Logged out successfully');
    } catch (error) {
      console.error('🔥 Failed to logout:', error);
    }
  };

  // Connected state - show user info
  if (account?.isConnected) {
    const displayAddress = wallet 
      ? para.getDisplayAddress(wallet.id, {
          truncate: true,
          addressType: wallet.type,
        })
      : 'No Wallet Selected';

    return (
      <View style={styles.connectedContainer}>
        <View style={styles.connectedModal}>
          <View style={styles.starIcon}>
            <Text style={styles.starText}>✦</Text>
          </View>
          
          <Text style={styles.connectedTitle}>Welcome to Everchess</Text>
          <Text style={styles.connectedSubtitle}>Successfully Connected</Text>
          
          <View style={styles.walletInfo}>
            <Text style={styles.walletLabel}>Wallet Address:</Text>
            <Text style={styles.walletAddress}>{displayAddress}</Text>
          </View>

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Authentication state - production ready design
  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        {/* Auth Form Section */}
        <View style={[styles.authSection, isLargeScreen && styles.authSectionLarge]}>
          <View style={styles.authContent}>
            {/* Mobile logo (only visible on mobile) */}
            {!isLargeScreen && (
              <View style={styles.mobileLogoSection}>
                <View style={styles.logoContainer}>
                  <View style={styles.diamond} />
                </View>
                <Text style={styles.mobileTitle}>EVERCHESS</Text>
                <Text style={styles.mobileSubtitle}>ANCIENT MADE MODERN</Text>
              </View>
            )}

            <View style={styles.authCard}>
              <Text style={styles.authTitle}>Sign Up or Login</Text>

              {/* Social Login Buttons */}
              <View style={styles.socialButtonsContainer}>
                {/* Google Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('google')}
                  disabled={isConnecting}
                >
                  <GoogleIcon size={24} color="#4285F4" />
                </TouchableOpacity>

                {/* X/Twitter Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('twitter')}
                  disabled={isConnecting}
                >
                  <TwitterIcon size={20} color="#ffffff" />
                </TouchableOpacity>

                {/* Apple Button */}
                <TouchableOpacity
                  style={[styles.socialButton, isConnecting && styles.disabledButton]}
                  onPress={() => handleSocialLogin('apple')}
                  disabled={isConnecting}
                >
                  <AppleIcon size={24} color="#ffffff" />
                </TouchableOpacity>
              </View>

              {/* Email/Phone Input */}
              <View style={styles.inputContainer}>
                <View style={styles.inputIconContainer}>
                  <Text style={styles.inputIcon}>✉</Text>
                  <Text style={styles.inputIcon}>📱</Text>
                </View>
                <TextInput
                  style={styles.input}
                  placeholder="Enter email or phone"
                  placeholderTextColor="#888888"
                  value={emailOrPhone}
                  onChangeText={setEmailOrPhone}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  onSubmitEditing={handleEmailPhoneLogin}
                />
              </View>

              {/* Terms and Powered by */}
              <View style={styles.footer}>
                <Text style={styles.termsText}>
                  By logging in you agree to our{' '}
                  <Text style={styles.termsLink}>Terms & Conditions</Text>
                </Text>
                <Text style={styles.poweredBy}>
                  Powered by <Text style={styles.paraText}>Para</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Branding Section (only visible on larger screens) */}
        {isLargeScreen && (
          <View style={styles.brandingContainer}>
            <View style={styles.brandingContent}>
              <View style={styles.brandingLogoContainer}>
                <View style={styles.brandingDiamond} />
              </View>
              <Text style={styles.brandingTitle}>EVERCHESS</Text>
              <Text style={styles.brandingSubtitle}>ANCIENT MADE MODERN</Text>

              <View style={styles.featuresContainer}>
                <Text style={styles.featuresTitle}>Experience Chess Like Never Before</Text>
                <View style={styles.featuresList}>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Collect unique 3D chess sets</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Earn rewards through the battlepass</Text>
                  </View>
                  <View style={styles.featureItem}>
                    <View style={styles.featureIcon}>
                      <Text style={styles.checkmark}>✓</Text>
                    </View>
                    <Text style={styles.featureText}>Compete in tournaments and win prizes</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2b2b2b', // Gray background for entire screen
    minHeight: '100vh',
  },
  mainContainer: {
    flex: 1,
    flexDirection: isLargeScreen ? 'row' : 'column',
  },
  authSection: {
    flex: 1,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authSectionLarge: {
    flex: isLargeScreen ? 0.5 : 1,
  },
  authContent: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  mobileLogoSection: {
    alignItems: 'center',
    marginBottom: 40,
    ...(isLargeScreen && { display: 'none' }),
  },
  logoContainer: {
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  diamond: {
    width: 60,
    height: 60,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 8,
  },
  mobileTitle: {
    fontSize: 28,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  mobileSubtitle: {
    fontSize: 14,
    color: '#fcdf3a', // Everchess yellow
    textAlign: 'center',
  },
  authCard: {
    backgroundColor: '#3a3a3a', // Slightly lighter gray for the card
    borderRadius: 16,
    padding: 24,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  authTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 32,
  },
  socialButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  socialButton: {
    width: 70,
    height: 70,
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#5a5a5a',
  },
  disabledButton: {
    opacity: 0.6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4a4a4a',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5a5a5a',
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: '100%',
    marginBottom: 24,
  },
  inputIconContainer: {
    flexDirection: 'row',
    marginRight: 12,
    gap: 4,
  },
  inputIcon: {
    fontSize: 16,
    color: '#888888',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#ffffff',
    paddingVertical: 4,
  },
  footer: {
    alignItems: 'center',
    gap: 8,
  },
  termsText: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  termsLink: {
    color: '#ffffff',
    textDecorationLine: 'underline',
  },
  poweredBy: {
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
  },
  paraText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  // Branding section styles (for larger screens)
  brandingContainer: {
    flex: 0.5,
    backgroundColor: '#2b2b2b',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  brandingContent: {
    alignItems: 'center',
    maxWidth: 400,
  },
  brandingLogoContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandingDiamond: {
    width: 80,
    height: 80,
    backgroundColor: '#FFFFFF',
    transform: [{ rotate: '45deg' }],
    borderRadius: 12,
  },
  brandingTitle: {
    fontSize: 48,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandingSubtitle: {
    fontSize: 18,
    color: '#fcdf3a',
    textAlign: 'center',
    marginBottom: 48,
  },
  featuresContainer: {
    backgroundColor: '#3a3a3a',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  featuresTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 24,
  },
  featuresList: {
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(252, 223, 58, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    fontSize: 12,
    color: '#fcdf3a',
    fontWeight: 'bold',
  },
  featureText: {
    fontSize: 14,
    color: '#cccccc',
    flex: 1,
  },
  // Connected state styles
  connectedContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  connectedModal: {
    backgroundColor: '#2b2b2b',
    borderRadius: 16,
    padding: 32,
    width: Math.min(400, screenWidth - 40),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  connectedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  connectedSubtitle: {
    fontSize: 16,
    color: '#22c55e',
    marginBottom: 24,
    textAlign: 'center',
  },
  walletInfo: {
    backgroundColor: '#404040',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 24,
  },
  walletLabel: {
    fontSize: 14,
    color: '#888888',
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 16,
    color: '#ffffff',
    fontFamily: 'monospace',
  },
  logoutButton: {
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    width: '100%',
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
