import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { 
  Environment, 
  ParaProvider, 
  ParaModal, 
  useAccount, 
  useWallet, 
  useModal, 
  useClient,
  useSignMessage 
} from '@getpara/react-sdk';

// Create QueryClient instance as per official docs
const queryClient = new QueryClient();

interface ParaWalletProviderProps {
  children: React.ReactNode;
  apiKey: string;
  projectId?: string;
}

// Main Para Provider Component - Following Official Documentation Exactly
export const ParaWalletProvider: React.FC<ParaWalletProviderProps> = ({ 
  children, 
  apiKey, 
  projectId 
}) => {
  // Get the correct environment enum value
  const getEnvironment = () => {
    const envString = process.env.REACT_APP_PARA_ENVIRONMENT || 'beta';
    
    switch (envString.toLowerCase()) {
      case 'production':
      case 'prod':
        return Environment.PROD;
      case 'dev':
      case 'development':
        return Environment.DEV;
      case 'sandbox':
        return Environment.SANDBOX;
      case 'beta':
      case 'staging':
      default:
        return Environment.BETA;
    }
  };

  const paraConfig = {
    env: getEnvironment(),
    apiKey: apiKey,
  };

  console.log('🔍 Final Para configuration:', {
    env: paraConfig.env,
    apiKey: apiKey ? 'SET' : 'MISSING',
    projectId: projectId ? 'SET' : 'MISSING',
  });

  // Following official documentation structure exactly with custom theme
  return (
    <QueryClientProvider client={queryClient}>
      <ParaProvider
        paraClientConfig={paraConfig}
        paraModalConfig={{
          theme: {
            "foregroundColor": "#ffffff",
            "backgroundColor": "#2b2b2b",
            "accentColor": "#ffffff",
            "darkForegroundColor": "#ffffff",
            "darkBackgroundColor": "#2b2b2b",
            "darkAccentColor": "#ffffff",
            "mode": "dark",
            "borderRadius": "12px",
            "font": "system-ui"
          },
          oAuthMethods: ["GOOGLE", "TWITTER", "APPLE"],
          authLayout: ["AUTH:FULL"],
          recoverySecretStepEnabled: true,
          onRampTestMode: true,
          disableEmailLogin: false,
          disablePhoneLogin: false,
          appName: "Everchess",
          logo: ""
        }}
        callbacks={{
          onLogout: (event) => {
            console.log('✅ Para Logout:', event.detail);
          },
          onLogin: (event) => {
            console.log('✅ Para Login:', event.detail);
          },
          onSignMessage: (event) => {
            console.log('✅ Para Message Signed:', event.detail);
          },
          onAccountSetup: (event) => {
            console.log('✅ Para Account Setup:', event.detail);
          },
          onAccountCreation: (event) => {
            console.log('✅ Para Account Created:', event.detail);
          },
        }}
      >
        {children}
        <ParaModal />
      </ParaProvider>
    </QueryClientProvider>
  );
};

// Para Wallet Component using official hooks - Following docs exactly
export const ParaWalletButton: React.FC = () => {
  // Use Para hooks directly as per official documentation
  const { data: account } = useAccount();
  const { data: wallet } = useWallet();
  const { signMessageAsync } = useSignMessage();
  const para = useClient();
  const { openModal } = useModal();

  const [message, setMessage] = useState('Hello from Everchess!');
  const [messageSignature, setMessageSignature] = useState<string | undefined>();
  const [isConnecting, setIsConnecting] = useState(false);

  console.log('🔍 Para hooks data:', {
    account: account,
    wallet: wallet,
    isConnected: account?.isConnected,
  });

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      console.log('🔍 Opening Para authentication modal...');
      openModal();
      console.log('✅ Para modal opened successfully');
    } catch (error) {
      console.error('🔥 Failed to open Para modal:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleSignMessage = async () => {
    if (!wallet || !message) {
      console.log('⚠️ No wallet or message to sign');
      return;
    }

    try {
      console.log('🔍 Signing message:', message);
      
      const signatureRes = await signMessageAsync({
        messageBase64: Buffer.from(message).toString('base64'),
      });

      if (!('pendingTransactionId' in signatureRes) && signatureRes.signature) {
        setMessageSignature(signatureRes.signature);
        console.log('✅ Message signed successfully:', signatureRes.signature);
      }
    } catch (error) {
      console.error('🔥 Failed to sign message:', error);
    }
  };

  const handleLogout = async () => {
    try {
      console.log('🔍 Logging out from Para...');
      await para.logout({ preservePregenWallets: false });
      setMessageSignature(undefined);
      console.log('✅ Logged out successfully');
    } catch (error) {
      console.error('🔥 Failed to logout:', error);
    }
  };

  // Connected state
  if (account?.isConnected) {
    const displayAddress = wallet 
      ? para.getDisplayAddress(wallet.id, {
          truncate: true,
          addressType: wallet.type,
        })
      : 'No Wallet Selected';

    return (
      <View style={styles.container}>
        <Text style={styles.titleText}>🎉 Connected to Para Wallet</Text>
        <Text style={styles.successText}>✅ Authentication Successful</Text>
        
        <View style={styles.walletInfo}>
          <Text style={styles.walletLabel}>Connected Wallet:</Text>
          <Text style={styles.walletAddress}>{displayAddress}</Text>
        </View>

        <View style={styles.messageSection}>
          <Text style={styles.sectionTitle}>Test Message Signing:</Text>
          <Text style={styles.messageText}>Message: "{message}"</Text>
          
          <TouchableOpacity style={styles.signButton} onPress={handleSignMessage}>
            <Text style={styles.buttonText}>Sign Message</Text>
          </TouchableOpacity>
          
          {messageSignature && (
            <View style={styles.signatureContainer}>
              <Text style={styles.signatureLabel}>Signature:</Text>
              <Text style={styles.signatureText}>
                {messageSignature.slice(0, 20)}...{messageSignature.slice(-20)}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.modalButton} onPress={openModal}>
            <Text style={styles.buttonText}>Open Para Modal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.disconnectButton} onPress={handleLogout}>
            <Text style={styles.buttonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Disconnected state
  return (
    <View style={styles.container}>
      <Text style={styles.titleText}>Connect to Everchess</Text>
      <Text style={styles.infoText}>
        Choose your preferred authentication method
      </Text>
      
      <TouchableOpacity
        style={[styles.connectButton, isConnecting && styles.disabledButton]}
        onPress={handleConnect}
        disabled={isConnecting}
      >
        <Text style={styles.buttonText}>
          {isConnecting ? 'Opening Para Wallet...' : '🔐 Connect with Para Wallet'}
        </Text>
      </TouchableOpacity>
      
      <View style={styles.authMethodsContainer}>
        <Text style={styles.authMethodsTitle}>Available Authentication Methods:</Text>
        <View style={styles.authMethodsList}>
          <Text style={styles.authMethod}>📧 Email Authentication</Text>
          <Text style={styles.authMethod}>📱 Phone Number (SMS)</Text>
          <Text style={styles.authMethod}>🔍 Google Account</Text>
          <Text style={styles.authMethod}>🍎 Apple ID</Text>
          <Text style={styles.authMethod}>🎮 Discord</Text>
          <Text style={styles.authMethod}>🐦 Twitter/X</Text>
          <Text style={styles.authMethod}>🔗 External Wallets</Text>
        </View>
      </View>
      
      <Text style={styles.helpText}>
        Para Wallet provides secure, non-custodial authentication with multiple options.
        Your keys are encrypted and only you have access.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 8,
  },
  titleText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  successText: {
    fontSize: 16,
    color: '#22c55e',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  walletInfo: {
    backgroundColor: '#e8f5e8',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  walletLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 14,
    color: '#22c55e',
    fontFamily: 'monospace',
  },
  messageSection: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    borderRadius: 6,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  messageText: {
    fontSize: 13,
    color: '#555',
    marginBottom: 12,
  },
  signatureContainer: {
    marginTop: 12,
    padding: 8,
    backgroundColor: '#e8f5e8',
    borderRadius: 4,
  },
  signatureLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  signatureText: {
    fontSize: 11,
    color: '#22c55e',
    fontFamily: 'monospace',
  },
  authMethodsContainer: {
    marginTop: 16,
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 6,
  },
  authMethodsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  authMethodsList: {
    alignItems: 'flex-start',
  },
  authMethod: {
    fontSize: 13,
    color: '#555',
    marginBottom: 4,
    paddingLeft: 8,
  },
  helpText: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  connectButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
    alignItems: 'center',
  },
  signButton: {
    backgroundColor: '#22c55e',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  modalButton: {
    backgroundColor: '#8b5cf6',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    flex: 1,
  },
  disconnectButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    flex: 1,
  },
  disabledButton: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
