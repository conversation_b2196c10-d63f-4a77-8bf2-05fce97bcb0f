/**
 * Para Wallet Configuration
 * 
 * Centralized configuration for Para Wallet SDK across platforms
 */

import { Platform } from 'react-native';

// Para Environment Types
export enum ParaEnvironment {
  BETA = 'beta',
  PRODUCTION = 'production',
}

// Para Configuration Interface
export interface ParaConfig {
  apiKey: string;
  environment: ParaEnvironment;
  projectId?: string;
  appName: string;
  appDescription: string;
  supportedChains: string[];
  authMethods: {
    email: boolean;
    phone: boolean;
    google: boolean;
    apple: boolean;
    discord: boolean;
    twitter: boolean;
    wallet: boolean;
  };
  theme: {
    colorMode: 'light' | 'dark';
    primaryColor: string;
  };
}

// Get environment variables with fallbacks
// Note: In web builds, these are defined by webpack's DefinePlugin
const getEnvVar = (key: string, fallback: string = ''): string => {
  // For web builds, environment variables are injected at build time
  const value = process.env[key];
  return value || fallback;
};

// Platform-specific configuration
export const getParaConfig = (): ParaConfig => {
  const isWeb = Platform.OS === 'web';
  
  return {
    apiKey: getEnvVar('EXPO_PUBLIC_PARA_API_KEY', ''),
    environment: getEnvVar('EXPO_PUBLIC_PARA_ENV', 'beta') as ParaEnvironment,
    projectId: getEnvVar('EXPO_PUBLIC_PARA_PROJECT_ID', ''),
    appName: 'Everchess',
    appDescription: 'Ancient Made Modern - 3D Chess with Web3 Integration',
    supportedChains: ['solana:mainnet', 'solana:devnet'],
    authMethods: {
      email: true,
      phone: true,
      google: true,
      apple: !isWeb && Platform.OS === 'ios', // Apple Sign-In only on iOS
      discord: true,
      twitter: false, // Disabled for now
      wallet: true,
    },
    theme: {
      colorMode: 'dark',
      primaryColor: '#5856D6', // Everchess brand color
    },
  };
};

// Validation function
export const validateParaConfig = (config: ParaConfig): boolean => {
  if (!config.apiKey) {
    console.error('Para API Key is missing. Authentication will not work.');
    return false;
  }
  
  if (!config.projectId && Platform.OS !== 'web') {
    console.warn('Para Project ID is missing. Some features may not work on native platforms.');
  }
  
  return true;
};

// Export default configuration
export const paraConfig = getParaConfig();
