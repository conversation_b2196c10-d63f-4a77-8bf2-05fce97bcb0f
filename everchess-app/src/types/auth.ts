import { Wallet } from '@getpara/react-sdk';

export interface User {
  id: string;
  email?: string;
  phoneNumber?: string;
  isGuest: boolean;
  isAuthenticated: boolean;
  wallets?: Wallet[];
  connectedAccounts?: any[];
  solanaAddress?: string;
  biometricsId?: string;
}

// Define the result type from service methods, used primarily by native
export type AuthResult = {
  success: boolean;
  user?: User;
  error?: string;
  needsVerification?: boolean;
  verificationMethod?: 'email' | 'phone';
  biometricsId?: string; // For native phone auth
  webFlowInitiated?: boolean;
};

// Define the comprehensive shape of the AuthContext for both platforms
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => Promise<void> | void;

  // Web-specific methods
  openParaModal: () => void;

  // Core authentication methods
  signInWithEmail: (email: string) => Promise<AuthResult>;
  signUpWithEmail: (email: string) => Promise<AuthResult>;
  signInWithPhone: (phone: string) => Promise<AuthResult>;
  signInWithSocial: (provider: 'google' | 'apple' | 'x') => Promise<AuthResult>;
  
  // OTP verification (native-specific)
  verifyLoginOtp: (identifier: string, otp: string, method: 'email' | 'phone') => Promise<AuthResult>;
  
  // Native-specific properties
  biometricsId: string | null;
}
