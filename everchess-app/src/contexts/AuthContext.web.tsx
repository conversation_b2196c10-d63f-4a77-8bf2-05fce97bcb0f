import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Web-only imports
import { useAccount, useModal, useLogout, OAuthMethod } from '@getpara/react-sdk';

import { User, AuthResult } from '@/types/auth'; // Our internal User type

// --- Type Definitions ---
// The full AuthContextType, tailored for web.
interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isNative: boolean;
  error: string | null;
  signInWithEmail: (email: string) => Promise<AuthResult>;
  signUpWithEmail: (email: string) => Promise<AuthResult>;
  signUpOrLoginWithPhone: (phone: string, countryCode: string) => Promise<AuthResult>;
  verifyLoginOtp: (identifier: string, otp: string, method: 'email' | 'phone', biometricsId?: string) => Promise<AuthResult>;
  signInWithSocial: (provider: OAuthMethod) => Promise<AuthResult>;
  connectSolanaWallet: () => Promise<AuthResult>;
  continueAsGuest: () => Promise<AuthResult>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// --- Web Provider ---
const AuthProviderWeb: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { data: account, status: accountStatus } = useAccount();
  const { openModal } = useModal();
  const { logoutAsync } = useLogout();

  const isLoading = accountStatus === 'pending';

  useEffect(() => {
    if (accountStatus === 'success' && account?.isConnected) {
      const internalUser: User = {
        id: (account as any)?.userId || 'para-web-user',
        isAuthenticated: true,
        isGuest: false,
      };
      setCurrentUser(internalUser);
    } else {
      setCurrentUser(null);
    }
  }, [account, accountStatus]);

  const webAction = async (): Promise<AuthResult> => {
    openModal();
    return { success: true, webFlowInitiated: true };
  };

  const unsupportedWebAction = async (actionName: string): Promise<AuthResult> => {
    const err = `${actionName} is handled by the Para Modal on web or is not applicable.`;
    console.warn(err);
    setError(err);
    return { success: false, error: err };
  };

  const signOut = async (): Promise<void> => {
    setError(null);
    try {
      await logoutAsync();
      setCurrentUser(null);
    } catch (e: any) {
      const err = e.message || 'Failed to sign out from web.';
      setError(err);
      console.error(err, e);
    }
  };

  const contextValue: AuthContextType = {
    currentUser,
    isAuthenticated: !!currentUser && !!account?.isConnected,
    isLoading,
    isNative: false,
    error,
    signInWithEmail: () => webAction(),
    signUpWithEmail: () => webAction(),
    signUpOrLoginWithPhone: () => webAction(),
    // @ts-expect-error identifier and otp are unused in the web provider as OTP is handled by the modal
    verifyLoginOtp: (identifier, otp, method) => unsupportedWebAction(`verifyOtp (${method})`),
    signInWithSocial: (_provider) => webAction(),
    connectSolanaWallet: () => webAction(),
    continueAsGuest: () => webAction(),
    signOut,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// --- Main Provider ---
// On web, AuthProvider is simply the AuthProviderWeb.
export const AuthProvider: React.FC<AuthProviderProps> = AuthProviderWeb;

// --- Custom Hook ---
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
