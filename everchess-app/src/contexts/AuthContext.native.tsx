import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Native-only imports
import * as nativeParaService from '@/services/paraService.native';
import { User, AuthResult } from '@/types/auth'; // Our internal User type

// --- Type Definitions ---
// The full AuthContextType, tailored for native.
interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isNative: boolean;
  error: string | null;
  // Updated phone auth methods to align with the new two-step flow
  createUserByPhone: (phone: string, countryCode: string) => Promise<AuthResult>;
  verifyPhone: (otp: string) => Promise<AuthResult>;
  // Placeholders for other methods
  signInWithEmail: (email: string) => Promise<AuthResult>;
  signUpWithEmail: (email: string) => Promise<AuthResult>;
  signInWithSocial: (provider: string) => Promise<AuthResult>;
  connectSolanaWallet: () => Promise<AuthResult>;
  continueAsGuest: () => Promise<AuthResult>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// --- Native Provider ---
const AuthProviderNative: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initialize = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await nativeParaService.init();
      } catch (e: any) {
        const err = e.message || 'Failed to initialize native auth service.';
        setError(err);
        console.error('Native init error:', e);
      }
      setIsLoading(false);
    };
    initialize();
  }, []);

  const nativeAction = async <T extends any[]>(
    action: (...args: T) => Promise<AuthResult>,
    ...args: T
  ): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await action(...args);
      if (result.success && result.user) {
        setCurrentUser(result.user);
      } else if (result.error) {
        setError(result.error);
      }
      return result;
    } catch (e: any) {
      const err = e.message || 'An unexpected error occurred during native action.';
      setError(err);
      console.error('Native action error:', e);
      return { success: false, error: err };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    try {
      await nativeParaService.logout();
      setCurrentUser(null);
    } catch (e: any) {
      setError(e.message || 'Failed to sign out from native.');
      console.error('Native signout error:', e);
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: AuthContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isLoading,
    isNative: true,
    error,
    // Use the new, correctly named methods from the native service
    createUserByPhone: (phone, countryCode) => nativeAction(nativeParaService.createUserByPhone, phone, countryCode),
    verifyPhone: (otp) => nativeAction(nativeParaService.verifyPhone, otp),
    // Placeholders remain for unimplemented features
    signInWithEmail: (email) => nativeAction(nativeParaService.signInWithEmail, email),
    signUpWithEmail: (email) => nativeAction(nativeParaService.signUpWithEmail, email),
    signInWithSocial: async (provider) => ({ success: false, error: `Social sign-in (${provider}) not implemented for native yet.` }),
    connectSolanaWallet: async () => ({ success: false, error: 'Solana wallet connect not implemented for native yet.' }),
    continueAsGuest: () => nativeAction(nativeParaService.createGuestUser),
    signOut,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// --- Main Provider ---
// On native platforms, AuthProvider is simply the AuthProviderNative.
export const AuthProvider: React.FC<AuthProviderProps> = AuthProviderNative;

// --- Custom Hook ---
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
