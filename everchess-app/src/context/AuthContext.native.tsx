import { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import * as paraService from '../services/paraService.native';
import type { User, AuthResult, AuthContextType } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [biometricsId, setBiometricsId] = useState<string | null>(null);

  useEffect(() => {
    paraService.init().finally(() => setIsLoading(false));
  }, []);

  const signInWithEmail = async (email: string): Promise<AuthResult> => {
    setIsLoading(true);
    const result = await paraService.signInWithEmail(email);
    if (result.success && result.biometricsId) {
      setBiometricsId(result.biometricsId);
    }
    setIsLoading(false);
    return result;
  };

  const signUpWithEmail = async (email: string): Promise<AuthResult> => {
    setIsLoading(true);
    const result = await paraService.signUpWithEmail(email);
    if (result.success && result.biometricsId) {
      setBiometricsId(result.biometricsId);
    }
    setIsLoading(false);
    return result;
  };

  const signInWithPhone = async (phone: string): Promise<AuthResult> => {
    setIsLoading(true);
    // Extract country code or default to +1
    const countryCode = phone.startsWith('+') ? phone.substring(0, phone.length - 10) : '+1';
    const phoneNumber = phone.startsWith('+') ? phone.substring(phone.length - 10) : phone;
    
    const result = await paraService.signUpOrLoginWithPhone(phoneNumber, countryCode);
    if (result.success && result.biometricsId) {
      setBiometricsId(result.biometricsId);
    }
    setIsLoading(false);
    return result;
  };

  const verifyLoginOtp = async (identifier: string, otp: string, method: 'email' | 'phone'): Promise<AuthResult> => {
    setIsLoading(true);
    const result = await paraService.verifyLoginOtp(identifier, otp, method, biometricsId ?? undefined);
    if (result.success && result.user?.id) {
      const newUser: User = { 
        id: result.user.id, 
        isAuthenticated: true, 
        isGuest: false 
      };
      if (method === 'email') newUser.email = identifier;
      if (method === 'phone') newUser.phoneNumber = identifier;
      setUser(newUser);
    }
    setIsLoading(false);
    return result;
  };

  const signInWithSocial = async (provider: 'google' | 'apple' | 'x'): Promise<AuthResult> => {
    setIsLoading(true);
    const result = await paraService.signInWithSocial(provider);
    if (result.success && result.user) {
      setUser({ ...result.user, isGuest: false });
    }
    setIsLoading(false);
    return result;
  };

  const logout = async () => {
    setIsLoading(true);
    await paraService.logout();
    setUser(null);
    setBiometricsId(null);
    setIsLoading(false);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user && user.isAuthenticated,
    isLoading,
    biometricsId,
    signInWithEmail,
    signUpWithEmail,
    signInWithPhone,
    verifyLoginOtp,
    signInWithSocial,
    logout,
    // Web-specific methods (not implemented on native)
    openParaModal: () => console.warn('openParaModal is not available on native'),
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
