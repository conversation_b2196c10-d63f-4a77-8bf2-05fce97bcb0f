import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ParaProvider, ParaModal, useAccount, useModal, useWallet } from '@getpara/react-sdk';
import type { User, AuthResult, AuthContextType } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const InnerAuthProvider = ({ children }: { children: ReactNode }) => {
  const { data: account } = useAccount();
  const { openModal } = useModal();
  const { data: wallet } = useWallet();

  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(account === undefined);
    if (account?.isConnected && account.userId) {
      setUser({
        id: account.userId,
        email: account.email ?? undefined,
        phoneNumber: account.phone ?? undefined,
        isAuthenticated: true,
        isGuest: wallet?.isGuest ?? false,
      });
    } else {
      setUser(null);
    }
  }, [account, wallet]);

  const logout = async () => {
    // The Para SDK handles logout via its own UI/state management
    // We just clear our local user state
    setUser(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user && user.isAuthenticated && !user.isGuest,
    isLoading,
    openParaModal: openModal,
    logout,
    // Native-specific methods are no-ops on web
    biometricsId: null,
    signInWithEmail: async () => ({ success: false, error: 'Not applicable on web' }),
    signUpWithEmail: async () => ({ success: false, error: 'Not applicable on web' }),
    signUpOrLoginWithPhone: async () => ({ success: false, error: 'Not applicable on web' }),
    verifyLoginOtp: async () => ({ success: false, error: 'Not applicable on web' }),
    createGuestUser: async () => ({ success: false, error: 'Not applicable on web' }),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      <ParaModal />
    </AuthContext.Provider>
  );
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  return (
    <ParaProvider
      apiKey={process.env.EXPO_PUBLIC_PARA_API_KEY ?? ''}
      env="production"
    >
      <InnerAuthProvider>{children}</InnerAuthProvider>
    </ParaProvider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
