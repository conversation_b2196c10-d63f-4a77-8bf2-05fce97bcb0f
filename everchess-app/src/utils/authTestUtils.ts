/**
 * Authentication Testing Utilities
 * 
 * This file contains utilities to test the Para SDK authentication flows
 * in development mode. These functions help validate the implementation
 * without requiring manual UI testing for every flow.
 */

import { AuthResult } from '../types/auth';
import * as paraService from '../services/paraService.native';

export interface TestResult {
  testName: string;
  success: boolean;
  error?: string | undefined;
  details?: any;
}

/**
 * Test guest authentication flow
 */
export const testGuestAuth = async (): Promise<TestResult> => {
  try {
    console.log('[AuthTest] Testing guest authentication...');
    const result: AuthResult = await paraService.createGuestUser();
    
    return {
      testName: 'Guest Authentication',
      success: result.success,
      error: result.error,
      details: {
        user: result.user,
        webFlowInitiated: result.webFlowInitiated,
      },
    };
  } catch (error: any) {
    return {
      testName: 'Guest Authentication',
      success: false,
      error: error.message || 'Unknown error',
    };
  }
};

/**
 * Test Solana wallet connection flow
 */
export const testSolanaWalletConnection = async (
  walletType: 'solflare' | 'phantom' | 'backpack' = 'phantom'
): Promise<TestResult> => {
  try {
    console.log(`[AuthTest] Testing ${walletType} wallet connection...`);
    const result: AuthResult = await paraService.connectSolanaWallet(walletType);
    
    return {
      testName: `${walletType} Wallet Connection`,
      success: result.success,
      error: result.error,
      details: {
        user: result.user,
        webFlowInitiated: result.webFlowInitiated,
      },
    };
  } catch (error: any) {
    return {
      testName: `${walletType} Wallet Connection`,
      success: false,
      error: error.message || 'Unknown error',
    };
  }
};

/**
 * Test social login flow
 */
export const testSocialLogin = async (
  provider: 'google' | 'x' | 'apple' = 'google'
): Promise<TestResult> => {
  try {
    console.log(`[AuthTest] Testing ${provider} social login...`);
    const result: AuthResult = await paraService.signInWithSocial(provider);
    
    return {
      testName: `${provider} Social Login`,
      success: result.success,
      error: result.error,
      details: {
        user: result.user,
        webFlowInitiated: result.webFlowInitiated,
      },
    };
  } catch (error: any) {
    return {
      testName: `${provider} Social Login`,
      success: false,
      error: error.message || 'Unknown error',
    };
  }
};

/**
 * Run a comprehensive test suite for all authentication flows
 */
export const runAuthTestSuite = async (): Promise<TestResult[]> => {
  console.log('[AuthTest] Starting comprehensive authentication test suite...');
  
  const results: TestResult[] = [];
  
  // Test 1: Guest authentication (should guide to social login)
  results.push(await testGuestAuth());
  
  // Test 2: Solana wallet connection (should guide to authentication)
  results.push(await testSolanaWalletConnection('phantom'));
  
  // Test 3: Social login (requires user interaction, so we'll just test the flow initiation)
  // Note: This will open a browser window for actual authentication
  // results.push(await testSocialLogin('google'));
  
  console.log('[AuthTest] Test suite completed. Results:', results);
  
  return results;
};

/**
 * Log test results in a readable format
 */
export const logTestResults = (results: TestResult[]): void => {
  console.log('\n=== AUTHENTICATION TEST RESULTS ===');
  
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.testName}`);
    console.log(`   Status: ${result.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    if (result.details) {
      console.log(`   Details:`, result.details);
    }
  });
  
  const passCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n=== SUMMARY: ${passCount}/${totalCount} tests passed ===\n`);
};
