#!/usr/bin/env node

/**
 * Para Wallet + Expo PWA Build Verification Script
 * 
 * This script verifies that the Para Wallet integration is properly configured
 * for the Expo PWA build and provides troubleshooting guidance.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Para Wallet + Expo PWA Build Verification\n');

const checks = [];
let allPassed = true;

// Helper function to add check results
const addCheck = (name, passed, message = '') => {
  checks.push({ name, passed, message });
  if (!passed) allPassed = false;
  console.log(`${passed ? '✅' : '❌'} ${name}${message ? ': ' + message : ''}`);
};

// Check 1: Babel Configuration
console.log('1. Checking Babel Configuration...');
const hasBabelConfig = fs.existsSync('babel.config.js');
const hasBabelWebConfig = fs.existsSync('babel.config.web.js');

addCheck('babel.config.js exists', hasBabelConfig);
addCheck('babel.config.web.js exists', hasBabelWebConfig);

if (hasBabelWebConfig) {
  const babelWebContent = fs.readFileSync('babel.config.web.js', 'utf8');
  const hasReactPreset = babelWebContent.includes('@babel/preset-react');
  const hasTypeScriptPreset = babelWebContent.includes('@babel/preset-typescript');
  
  addCheck('Web Babel config has React preset', hasReactPreset);
  addCheck('Web Babel config has TypeScript preset', hasTypeScriptPreset);
}

// Check 2: Webpack Configuration
console.log('\n2. Checking Webpack Configuration...');
const hasWebpackConfig = fs.existsSync('webpack.config.js');
addCheck('webpack.config.js exists', hasWebpackConfig);

if (hasWebpackConfig) {
  const webpackContent = fs.readFileSync('webpack.config.js', 'utf8');
  const usesWebBabelConfig = webpackContent.includes('babel.config.web.js');
  const hasParaIncludes = webpackContent.includes('@getpara') || webpackContent.includes('@para');
  
  addCheck('Webpack uses web-specific Babel config', usesWebBabelConfig);
  addCheck('Webpack includes Para packages in transpilation', hasParaIncludes);
}

// Check 3: Para Dependencies
console.log('\n3. Checking Para Dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredParaDeps = [
  '@getpara/react-sdk',
  '@getpara/react-native-wallet',
  '@getpara/web-sdk'
];

requiredParaDeps.forEach(dep => {
  const isInstalled = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
  addCheck(`${dep} installed`, !!isInstalled, isInstalled ? `v${isInstalled}` : 'missing');
});

// Check 4: Environment Variables
console.log('\n4. Checking Environment Configuration...');
const hasEnvExample = fs.existsSync('.env.example') || fs.existsSync('.env');
addCheck('Environment file exists', hasEnvExample);

// Check 5: Para Configuration
console.log('\n5. Checking Para Configuration...');
const hasParaConfig = fs.existsSync('src/config/para.config.ts');
addCheck('Para config file exists', hasParaConfig);

if (hasParaConfig) {
  const paraConfigContent = fs.readFileSync('src/config/para.config.ts', 'utf8');
  const hasValidation = paraConfigContent.includes('validateParaConfig');
  const hasEnvironmentEnum = paraConfigContent.includes('ParaEnvironment');
  
  addCheck('Para config has validation', hasValidation);
  addCheck('Para config has environment enum', hasEnvironmentEnum);
}

// Check 6: App.web.tsx Integration
console.log('\n6. Checking App.web.tsx Integration...');
const hasAppWeb = fs.existsSync('App.web.tsx');
addCheck('App.web.tsx exists', hasAppWeb);

if (hasAppWeb) {
  const appWebContent = fs.readFileSync('App.web.tsx', 'utf8');
  const hasParaProvider = appWebContent.includes('ParaProvider');
  const hasParaModal = appWebContent.includes('ParaModal');
  const usesParaConfig = appWebContent.includes('paraConfig');
  
  addCheck('App.web.tsx has ParaProvider', hasParaProvider);
  addCheck('App.web.tsx has ParaModal', hasParaModal);
  addCheck('App.web.tsx uses centralized config', usesParaConfig);
}

// Check 7: Build Scripts
console.log('\n7. Checking Build Scripts...');
const hasWebScript = packageJson.scripts && packageJson.scripts['web'];
const hasBuildWebScript = packageJson.scripts && packageJson.scripts['build:web'];

addCheck('Web dev script exists', !!hasWebScript);
addCheck('Web build script exists', !!hasBuildWebScript);

// Check 8: TypeScript Configuration
console.log('\n8. Checking TypeScript Configuration...');
const hasTsConfig = fs.existsSync('tsconfig.json');
addCheck('tsconfig.json exists', hasTsConfig);

if (hasTsConfig) {
  const tsConfigContent = fs.readFileSync('tsconfig.json', 'utf8');
  const hasJsxReact = tsConfigContent.includes('"jsx"') && tsConfigContent.includes('react');
  addCheck('TypeScript JSX configured for React', hasJsxReact);
}

// Summary
console.log('\n' + '='.repeat(50));
console.log(`📊 Summary: ${checks.filter(c => c.passed).length}/${checks.length} checks passed`);

if (allPassed) {
  console.log('\n🎉 All checks passed! Your Para Wallet + Expo PWA setup looks good.');
  console.log('\n🚀 Next steps:');
  console.log('1. Set your EXPO_PUBLIC_PARA_API_KEY in .env');
  console.log('2. Run: npm run build:web');
  console.log('3. Test authentication in the built app');
} else {
  console.log('\n⚠️  Some checks failed. Please review the issues above.');
  console.log('\n🔧 Common fixes:');
  console.log('- Ensure babel.config.web.js is properly configured');
  console.log('- Check that webpack.config.js uses the web Babel config');
  console.log('- Verify all Para dependencies are installed');
  console.log('- Set up environment variables for Para API keys');
}

console.log('\n📚 For more help, check the Para Wallet documentation:');
console.log('https://docs.getpara.com');
