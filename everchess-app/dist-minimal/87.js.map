{"version": 3, "file": "87.js", "mappings": "gLAOaA,EAAW,W,kFACtB,WACE,OACEC,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAC,IAAA,6CACHF,EAAAA,EAAAA,GAAA,OAAAE,IAAA,2CAAKC,MAAM,uBACTH,EAAAA,EAAAA,GAAA,QAAAE,IAAA,8C,IAIP,CATqB,G,QCPD,glB", "sources": ["webpack://everchess-app/src/components/cpsl-info-box/cpsl-info-box.tsx", "webpack://everchess-app/src/components/cpsl-info-box/cpsl-info-box.scss"], "sourcesContent": ["import { Component, Host, h } from '@stencil/core';\n\n@Component({\n  tag: 'cpsl-info-box',\n  styleUrl: 'cpsl-info-box.scss',\n  shadow: true,\n})\nexport class CpslInfoBox {\n  render() {\n    return (\n      <Host>\n        <div class=\"info-box-container\">\n          <slot></slot>\n        </div>\n      </Host>\n    );\n  }\n}\n", "@import '../../themes/capsule.globals';\n\n:host {\n  --box-border-radius: var(--cpsl-border-radius-info-box);\n  --box-padding-top: 16px;\n  --box-padding-bottom: 16px;\n  --box-padding-start: 16px;\n  --box-padding-end: 16px;\n  --box-border-width: 1px;\n}\n\n.info-box-container {\n  @include border-radius(var(--input-border-radius));\n  @include padding(var(--box-padding-top), var(--box-padding-end), var(--box-padding-bottom), var(--box-padding-start));\n\n  display: flex;\n  background: var(--cpsl-color-background-4);\n  border-radius: var(--box-border-radius);\n}\n"], "names": ["CpslInfoBox", "h", "Host", "key", "class"], "sourceRoot": ""}