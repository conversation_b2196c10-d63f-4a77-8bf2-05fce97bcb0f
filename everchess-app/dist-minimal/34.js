"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[34],{6034:(e,t,n)=>{n.r(t),n.d(t,{default:()=>w});var r,a=n(7528),s=n(296),d=n(4756),u=n(7556),i=n(4848),o=n(4656),l=n(6408),c=n(6540),p=n(169),f=n(7522),y=n(7581).Ay.div(r||(r=(0,a.A)(["\n  width: ",";\n  height: ",";\n\n  iframe {\n    border: 0 !important;\n  }\n"])),function(e){return e.isEmbedded?"100%":"100vw"},function(e){return e.isEmbedded?"640px":"100vh"}),w=function(e){var t=e.para,r=e.isDark,a=e.isEmbedded,w=e.onRampConfig,h=e.onRampPurchase,b=e.setOnRampPurchase,m=(0,c.useState)(null),v=(0,s.A)(m,2),A=v[0],x=v[1],C=(0,c.useState)(null),g=(0,s.A)(C,2),k=g[0],I=g[1],M=(0,c.useState)(null),P=(0,s.A)(M,2),S=P[0],E=P[1];(0,c.useEffect)(function(){var e=(0,c.lazy)(function(){return n.e(182).then(n.bind(n,6182)).then(function(e){return{default:e.MoonPayBuyWidget}})}),t=(0,c.lazy)(function(){return n.e(182).then(n.bind(n,6182)).then(function(e){return{default:e.MoonPaySellWidget}})}),r=(0,c.lazy)(function(){return n.e(182).then(n.bind(n,6182)).then(function(e){return{default:e.MoonPayProvider}})});x(e),I(t),E(r)},[]);var T=h.testMode?"pk_test_HYobzemmTBXxcSStVA4dSED6jT":"pk_live_EQva4LydtNDE0Rwd9X7SG9w58wqOzbux",R=(0,c.useCallback)(function(e){return(0,u.B)(void 0,null,d.mark(function n(){var r;return d.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t.getUserId()&&h.walletType){n.next=1;break}throw new Error("missing required fields");case 1:return n.next=2,t.ctx.client.signMoonPayUrl(t.getUserId(),{url:e,type:h.walletType,cosmosPrefix:(0,o.nA)(h.network),testMode:h.testMode,walletId:h.walletId||void 0,externalWalletAddress:h.externalWalletAddress||void 0});case 2:return r=n.sent,n.abrupt("return",r.data.signature);case 3:case"end":return n.stop()}},n)}))},[h.walletId,h.walletType,t.cosmosPrefix,h.testMode,t]),W=(0,c.useCallback)(function(e){return(0,u.B)(void 0,null,d.mark(function n(){var r,u,i,o,c,f;return d.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,r=(0,p.EF)(w.assetInfo,l.V4.MOONPAY,e.quoteCurrency.code),u=(0,s.A)(r,2),i=u[0],o=u[1],n.next=1,t.ctx.client.updateOnRampPurchase({userId:t.getUserId(),walletId:h.walletId,purchaseId:h.id,externalWalletAddress:h.externalWalletAddress,updates:{fiatQuantity:e.baseCurrencyAmount.toString(),fiat:e.baseCurrency.code,network:i,asset:o,assetQuantity:e.quoteCurrencyAmount.toString(),status:l.Pb.FINISHED}});case 1:c=n.sent,b(c),a||setTimeout(function(){"undefined"!=typeof window&&window.close()},5e3),n.next=3;break;case 2:n.prev=2,f=n.catch(0),console.error(f);case 3:case"end":return n.stop()}},n,null,[[0,2]])}))},[h.walletId,h.id,h.externalWalletAddress,a]),O=(0,c.useCallback)(function(e){return(0,u.B)(void 0,null,d.mark(function n(){var r;return d.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=1,(0,f.u)(t,h,b,{assetQuantity:e.cryptoCurrencyAmount,fiatQuantity:e.fiatCurrencyAmount||void 0,fiat:e.fiatCurrency.code.toUpperCase(),destinationAddress:e.depositWalletAddress,contractAddress:e.cryptoCurrency.contractAddress,chainId:e.cryptoCurrency.chainId});case 1:return r=n.sent,n.abrupt("return",{depositId:r,cancelTransactionOnError:!1});case 2:case"end":return n.stop()}},n)}))},[t,h.id,h.testMode,h.walletId,h.walletType,b]),U=(0,c.useMemo)(function(){if(!A||!k)return null;var e=(0,p.D9)(w,{network:h.network,asset:h.asset,provider:l.V4.MOONPAY});return"BUY"===h.type?(0,i.jsx)(A,{variant:"embedded",baseCurrencyCode:h.fiat,baseCurrencyAmount:h.fiatQuantity,currencyCode:e,walletAddress:h.address,visible:!0,theme:r?"dark":"light",style:{height:"100%",width:"100%",border:"none",borderRadius:0,margin:0},onTransactionCompleted:W,onUrlSignatureRequested:R}):(0,i.jsx)(k,{variant:"embedded",visible:!0,theme:r?"dark":"light",style:{height:"100%",width:"100%",border:"none",borderRadius:0,margin:0},baseCurrencyCode:e,refundWalletAddress:h.address,onInitiateDeposit:O,onTransactionCompleted:W,onUrlSignatureRequested:R})},[h.type,h.address,h.walletId,h.walletType,h.asset,O,W,R,r,A,k]);return S?(0,i.jsx)(y,{isEmbedded:a,children:(0,i.jsx)(S,{apiKey:T,debug:h.testMode,children:U})}):null}}}]);
//# sourceMappingURL=34.js.map