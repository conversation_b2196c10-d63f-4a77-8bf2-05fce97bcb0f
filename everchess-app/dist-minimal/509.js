"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[509],{8509:(i,d,n)=>{n.r(d),n.d(d,{cpsl_col:()=>e});var a=n(4467),p=n(3029),l=n(2901),s=n(9289),t={xs:"(min-width: 0px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)"},c=function(i){if(void 0===i||""===i)return!0;if("undefined"!=typeof window&&window.matchMedia){var d=t[i];return window.matchMedia(d).matches}return!1},r="undefined"!=typeof window?window:void 0,g=r&&!!(r.CSS&&r.CSS.supports&&r.CSS.supports("--a: 0")),o=["","xs","sm","md","lg","xl"],e=function(){return(0,l.A)(function i(d){(0,p.A)(this,i),(0,s.r)(this,d),this.align="center",this.justify="center",this.gap="8px",this.offset=void 0,this.offsetXs=void 0,this.offsetSm=void 0,this.offsetMd=void 0,this.offsetLg=void 0,this.offsetXl=void 0,this.pull=void 0,this.pullXs=void 0,this.pullSm=void 0,this.pullMd=void 0,this.pullLg=void 0,this.pullXl=void 0,this.push=void 0,this.pushXs=void 0,this.pushSm=void 0,this.pushMd=void 0,this.pushLg=void 0,this.pushXl=void 0,this.size=void 0,this.sizeXs=void 0,this.sizeSm=void 0,this.sizeMd=void 0,this.sizeLg=void 0,this.sizeXl=void 0},[{key:"onResize",value:function(){(0,s.f)(this)}},{key:"getColumns",value:function(i){for(var d,n=0,a=o;n<a.length;n++){var p=a[n],l=c(p),s=this[i+p.charAt(0).toUpperCase()+p.slice(1)];l&&void 0!==s&&(d=s)}return d}},{key:"calculateSize",value:function(){var i=this.getColumns("size");if(i&&""!==i){var d="auto"===i?"auto":g?"calc(calc(".concat(i," / var(--ion-grid-columns, 12)) * 100%)"):i/12*100+"%";return{flex:"0 0 ".concat(d),width:"".concat(d),"max-width":"".concat(d)}}}},{key:"calculatePosition",value:function(i,d){var n=this.getColumns(i);if(n){var p=g?"calc(calc(".concat(n," / var(--ion-grid-columns, 12)) * 100%)"):n>0&&n<12?n/12*100+"%":"auto";return(0,a.A)({},d,p)}}},{key:"calculateOffset",value:function(i){return this.calculatePosition("offset",i?"margin-right":"margin-left")}},{key:"calculatePull",value:function(i){return this.calculatePosition("pull",i?"left":"right")}},{key:"calculatePush",value:function(i){return this.calculatePosition("push",i?"right":"left")}},{key:"render",value:function(){var i="rtl"===document.dir;return(0,s.h)(s.H,{key:"f4939676b491eb1d76138f93495ca714338efa34",style:Object.assign(Object.assign(Object.assign(Object.assign({},this.calculateOffset(i)),this.calculatePull(i)),this.calculatePush(i)),this.calculateSize())},(0,s.h)("slot",{key:"58799b5710715af09184b88c94d46638e78e1f41"}))}}])}();e.style=":host{-webkit-padding-start:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));padding-inline-start:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));-webkit-padding-end:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));padding-inline-end:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));padding-top:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));padding-bottom:var(--cpsl-grid-column-padding-xs, var(--cpsl-grid-column-padding, 0px));margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;box-sizing:border-box;position:relative;flex-basis:0;flex-grow:1;width:100%;max-width:100%;min-height:1px}@media (min-width: 576px){:host{-webkit-padding-start:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px));padding-inline-start:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px));-webkit-padding-end:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px));padding-inline-end:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px));padding-top:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px));padding-bottom:var(--cpsl-grid-column-padding-sm, var(--cpsl-grid-column-padding, 0px))}}@media (min-width: 768px){:host{-webkit-padding-start:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px));padding-inline-start:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px));-webkit-padding-end:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px));padding-inline-end:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px));padding-top:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px));padding-bottom:var(--cpsl-grid-column-padding-md, var(--cpsl-grid-column-padding, 0px))}}@media (min-width: 992px){:host{-webkit-padding-start:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px));padding-inline-start:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px));-webkit-padding-end:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px));padding-inline-end:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px));padding-top:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px));padding-bottom:var(--cpsl-grid-column-padding-lg, var(--cpsl-grid-column-padding, 0px))}}@media (min-width: 1200px){:host{-webkit-padding-start:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px));padding-inline-start:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px));-webkit-padding-end:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px));padding-inline-end:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px));padding-top:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px));padding-bottom:var(--cpsl-grid-column-padding-xl, var(--cpsl-grid-column-padding, 0px))}}"}}]);
//# sourceMappingURL=509.js.map