"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[975],{9975:(e,t,i)=>{i.r(t),i.d(t,{cpsl_modal_v2:()=>a});var n=i(3029),s=i(2901),o=i(9289),a=function(){return(0,s.A)(function e(t){(0,n.A)(this,e),(0,o.r)(this,t),this.cpslModalEntering=(0,o.c)(this,"cpslModalEntering",7),this.cpslModalEntered=(0,o.c)(this,"cpslModalEntered",7),this.cpslModalExiting=(0,o.c)(this,"cpslModalExiting",7),this.cpslModalExited=(0,o.c)(this,"cpslModalExited",7),this.enterTransitionDuration=.15,this.exitTransitionDuration=.15,this.elevated=void 0,this.noOverlay=void 0,this.open=void 0,this.zIndexOverride=void 0},[{key:"toggleHeight",value:function(){var e=this;this.open?(this.cpslModalEntering.emit(),this.el.style.display="flex",setTimeout(function(){e.cpslModalEntered.emit()},1e3*this.enterTransitionDuration)):(this.cpslModalExiting.emit(),setTimeout(function(){e.el.style.display="none",e.cpslModalExited.emit()},1e3*this.exitTransitionDuration))}},{key:"componentDidLoad",value:function(){this.toggleHeight()}},{key:"render",value:function(){return(0,o.h)(o.H,{key:"54037c194116e64864a47ea8eeedee31f804a736",class:{open:this.open,elevated:this.elevated,"no-overlay":this.noOverlay}},!this.noOverlay&&(0,o.h)("cpsl-overlay",{key:"152760c8eabdfca0dda636d7d28adc3030532558",zIndexOverride:this.zIndexOverride?this.zIndexOverride:void 0,id:"overlay",open:this.open,enterTransitionDuration:this.enterTransitionDuration,exitTransitionDuration:this.exitTransitionDuration}),(0,o.h)("cpsl-card",{key:"3e062561091c6831ece6a1abfd2b2e1dd67baefb",class:"card",style:{transitionDuration:this.open?"".concat(this.exitTransitionDuration,"s"):"".concat(this.enterTransitionDuration,"s")}},(0,o.h)("slot",{key:"94e26e323e40fd5bcc899d0311c044483442f5a4"})))}},{key:"el",get:function(){return(0,o.g)(this)}}],[{key:"watchers",get:function(){return{open:["toggleHeight"]}}}])}();a.style=":host{display:none;position:absolute;top:0;left:0;width:100vw;height:100vh;justify-content:center;align-items:center;z-index:10005}:host .card{z-index:10005;opacity:0;position:relative;transition:all}:host(.no-overlay){position:relative;top:unset;left:unset;width:100%;height:auto}:host(.open) .card{opacity:1}:host(.elevated) .card{--card-border-width:0px}:host(.elevated) .card::part(card-container){box-shadow:0px 8px 16px 0px rgba(0, 0, 0, 0.12)}"}}]);
//# sourceMappingURL=975.js.map