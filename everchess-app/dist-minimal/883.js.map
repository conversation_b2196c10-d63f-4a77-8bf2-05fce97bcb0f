{"version": 3, "file": "883.js", "mappings": "wLAOaA,EAAO,W,sEACJ,E,WAEsC,S,aAEM,S,SAEf,K,uBAE3C,WACE,OACEC,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAC,IAAA,2CAACC,OAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAK,UAAYC,KAAKC,OAAQ,YAAcD,KAAKE,SAAU,QAAUF,KAAKG,IAAIC,YAAa,cAAgBJ,KAAKK,IAAM,SAAW,SAC1IV,EAAAA,EAAAA,GAAA,QAAAE,IAAA,6C,IAGL,CAfiB,G,QCPD,gJ", "sources": ["webpack://everchess-app/src/components/cpsl-row/cpsl-row.tsx", "webpack://everchess-app/src/components/cpsl-row/cpsl-row.scss"], "sourcesContent": ["import { Component, Host, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'cpsl-row',\n  styleUrl: 'cpsl-row.scss',\n  shadow: true,\n})\nexport class CpslRow {\n  @Prop() col = false;\n\n  @Prop() align?: React.CSSProperties['alignItems'] = 'center';\n\n  @Prop() justify?: React.CSSProperties['justifyContent'] = 'center';\n\n  @Prop() gap?: React.CSSProperties['gap'] = '8px';\n\n  render() {\n    return (\n      <Host style={{ ['--align']: this.align, ['--justify']: this.justify, ['--gap']: this.gap.toString(), ['--direction']: this.col ? 'column' : 'row' }}>\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n", "@import './cpsl-row.vars';\n\n// Row\n// --------------------------------------------------\n\n:host {\n  display: flex;\n  flex-direction: var(--direction, row);\n  align-items: var(--align);\n  justify-content: var(--justify);\n  gap: var(--gap);\n  flex-wrap: wrap;\n}\n"], "names": ["CpslRow", "h", "Host", "key", "style", "_defineProperty", "this", "align", "justify", "gap", "toString", "col"], "sourceRoot": ""}