{"version": 3, "file": "23.js", "mappings": "8KAOaA,EAAQ,W,wEAIH,C,uBAEhB,WACE,OACEC,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAC,IAAA,2CACHC,MAAO,CACL,aAAcC,KAAKC,SAGrBL,EAAAA,EAAAA,GAAA,QAAAE,IAAA,6C,IAGL,CAhBkB,G,QCPD,0vG", "sources": ["webpack://everchess-app/src/components/cpsl-grid/cpsl-grid.tsx", "webpack://everchess-app/src/components/cpsl-grid/cpsl-grid.scss"], "sourcesContent": ["import { Component, Host, Prop, h } from '@stencil/core';\n\n@Component({\n  tag: 'cpsl-grid',\n  styleUrl: 'cpsl-grid.scss',\n  shadow: true,\n})\nexport class CpslGrid {\n  /**\n   * If `true`, the grid will have a fixed width based on the screen size.\n   */\n  @Prop() fixed = false;\n\n  render() {\n    return (\n      <Host\n        class={{\n          'grid-fixed': this.fixed,\n        }}\n      >\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n", "@import './cpsl-grid.vars';\n\n// Grid\n// --------------------------------------------------\n\n:host {\n  /**\n   * @prop --cpsl-grid-padding: Padding for the Grid\n   * @prop --cpsl-grid-padding-xs: Padding for the Grid on xs screens\n   * @prop --cpsl-grid-padding-sm: Padding for the Grid on sm screens\n   * @prop --cpsl-grid-padding-md: Padding for the Grid on md screens\n   * @prop --cpsl-grid-padding-lg: Padding for the Grid on lg screens\n   * @prop --cpsl-grid-padding-xl: Padding for the Grid on xl screens\n   *\n   * @prop --cpsl-grid-width: Width of the fixed Grid\n   * @prop --cpsl-grid-width-xs: Width of the fixed Grid on xs screens\n   * @prop --cpsl-grid-width-sm: Width of the fixed Grid on sm screens\n   * @prop --cpsl-grid-width-md: Width of the fixed Grid on md screens\n   * @prop --cpsl-grid-width-lg: Width of the fixed Grid on lg screens\n   * @prop --cpsl-grid-width-xl: Width of the fixed Grid on xl screens\n   */\n\n  @include make-breakpoint-padding($grid-paddings);\n  @include margin-horizontal(auto);\n\n  display: block;\n\n  flex: 1;\n}\n\n// Fixed Grid\n// --------------------------------------------------\n\n:host(.grid-fixed) {\n  @include make-grid-widths();\n}\n\n// Grid Padding\n// --------------------------------------------------\n\n// Remove the padding from grid and all immediate children columns\n:host(.cpsl-no-padding) {\n  --cpsl-grid-column-padding: 0;\n  --cpsl-grid-column-padding-xs: 0;\n  --cpsl-grid-column-padding-sm: 0;\n  --cpsl-grid-column-padding-md: 0;\n  --cpsl-grid-column-padding-lg: 0;\n  --cpsl-grid-column-padding-xl: 0;\n}\n"], "names": ["CpslGrid", "h", "Host", "key", "class", "this", "fixed"], "sourceRoot": ""}