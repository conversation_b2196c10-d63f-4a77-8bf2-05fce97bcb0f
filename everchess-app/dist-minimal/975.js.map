{"version": 3, "file": "975.js", "mappings": "mLAOaA,EAAW,W,6TAOqB,I,4BAMD,I,mHA2C1C,WAAY,IAAAC,EAAA,KACLC,KAAKC,MAQRD,KAAKE,kBAAkBC,OACvBH,KAAKI,GAAGC,MAAMC,QAAU,OACxBC,WAAW,WACTR,EAAKS,iBAAiBL,M,EACU,IAA/BH,KAAKS,2BAXRT,KAAKU,iBAAiBP,OAEtBI,WAAW,WACTR,EAAKK,GAAGC,MAAMC,QAAU,OACxBP,EAAKY,gBAAgBR,M,EACU,IAA9BH,KAAKY,wB,GAQX,CAAAC,IAAA,mBAAAC,MAED,WACEd,KAAKe,c,GACN,CAAAF,IAAA,SAAAC,MAED,WACE,OACEE,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAJ,IAAA,2CAACK,MAAO,CAAE,KAAQlB,KAAKC,KAAM,SAAYD,KAAKmB,SAAU,aAAcnB,KAAKoB,aAC5EpB,KAAKoB,YACLJ,EAAAA,EAAAA,GAAA,gBAAAH,IAAA,2CACEQ,eAAgBrB,KAAKqB,eAAiBrB,KAAKqB,oBAAiBC,EAC5DC,GAAG,UACHtB,KAAMD,KAAKC,KACXQ,wBAAyBT,KAAKS,wBAC9BG,uBAAwBZ,KAAKY,0BAGjCI,EAAAA,EAAAA,GAAA,aAAAH,IAAA,2CAAWK,MAAM,OAAOb,MAAO,CAAEmB,mBAAoBxB,KAAKC,KAAO,GAAHwB,OAAMzB,KAAKY,uBAAsB,QAAAa,OAASzB,KAAKS,wBAAuB,QAClIO,EAAAA,EAAAA,GAAA,QAAAH,IAAA,8C,GAIP,CAAAA,IAAA,KAAAa,I,oGA9FqB,G,QCPD,oc", "sources": ["webpack://everchess-app/src/components/cpsl-modal-v2/cpsl-modal-v2.tsx", "webpack://everchess-app/src/components/cpsl-modal-v2/cpsl-modal-v2.scss"], "sourcesContent": ["import { Component, Host, Prop, Watch, Element, Event, h, EventEmitter } from '@stencil/core';\n\n@Component({\n  tag: 'cpsl-modal-v2',\n  styleUrl: 'cpsl-modal-v2.scss',\n  shadow: true,\n})\nexport class CpslModalV2 {\n  @Element() el!: HTMLCpslModalV2Element;\n\n  /**\n   * Duration in seconds of the modal entering.\n   * Default is .15.\n   */\n  @Prop() enterTransitionDuration?: number = 0.15;\n\n  /**\n   * Duration in seconds of the modal exiting.\n   * Default is .15.\n   */\n  @Prop() exitTransitionDuration?: number = 0.15;\n\n  /**\n   * Whether or not to show the modal with a box shadow.\n   */\n  @Prop() elevated: boolean;\n\n  /**\n   * Whether or not to show the overlay.\n   */\n  @Prop() noOverlay: boolean;\n\n  /**\n   * Whether or not to show the modal.\n   */\n  @Prop() open: boolean;\n\n  /**\n   * Override z-index.\n   */\n  @Prop() zIndexOverride?: number;\n\n  /**\n   * Emitted when enter animation starts.\n   */\n  @Event() cpslModalEntering!: EventEmitter<null>;\n\n  /**\n   * Emitted when enter animation finishes.\n   */\n  @Event() cpslModalEntered!: EventEmitter<null>;\n\n  /**\n   * Emitted when exit animation starts.\n   */\n  @Event() cpslModalExiting!: EventEmitter<null>;\n\n  /**\n   * Emitted when exit animation finishes.\n   */\n  @Event() cpslModalExited!: EventEmitter<null>;\n\n  @Watch('open')\n  toggleHeight() {\n    if (!this.open) {\n      this.cpslModalExiting.emit();\n      // Animate out before setting display to none\n      setTimeout(() => {\n        this.el.style.display = 'none';\n        this.cpslModalExited.emit();\n      }, this.exitTransitionDuration * 1000);\n    } else {\n      this.cpslModalEntering.emit();\n      this.el.style.display = 'flex';\n      setTimeout(() => {\n        this.cpslModalEntered.emit();\n      }, this.enterTransitionDuration * 1000);\n    }\n  }\n\n  componentDidLoad() {\n    this.toggleHeight();\n  }\n\n  render() {\n    return (\n      <Host class={{ 'open': this.open, 'elevated': this.elevated, 'no-overlay': this.noOverlay }}>\n        {!this.noOverlay && (\n          <cpsl-overlay\n            zIndexOverride={this.zIndexOverride ? this.zIndexOverride : undefined}\n            id=\"overlay\"\n            open={this.open}\n            enterTransitionDuration={this.enterTransitionDuration}\n            exitTransitionDuration={this.exitTransitionDuration}\n          />\n        )}\n        <cpsl-card class=\"card\" style={{ transitionDuration: this.open ? `${this.exitTransitionDuration}s` : `${this.enterTransitionDuration}s` }}>\n          <slot></slot>\n        </cpsl-card>\n      </Host>\n    );\n  }\n}\n", "@use 'sass:map';\n@import '../../themes/capsule.globals';\n\n:host {\n  display: none;\n\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  justify-content: center;\n  align-items: center;\n\n  z-index: map.get($z-indices, 'modal');\n\n  & .card {\n    z-index: map.get($z-indices, 'modal');\n    opacity: 0;\n\n    position: relative;\n\n    transition: all;\n  }\n}\n\n:host(.no-overlay) {\n  position: relative;\n  top: unset;\n  left: unset;\n  width: 100%;\n  height: auto;\n}\n\n:host(.open) {\n  & .card {\n    opacity: 1;\n  }\n}\n\n:host(.elevated) {\n  & .card {\n    --card-border-width: 0px;\n\n    &::part(card-container) {\n      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);\n    }\n  }\n}\n"], "names": ["CpslModalV2", "_this", "this", "open", "cpslModalEntering", "emit", "el", "style", "display", "setTimeout", "cpslModalEntered", "enterTransitionDuration", "cpslModalExiting", "cpslModalExited", "exitTransitionDuration", "key", "value", "toggleHeight", "h", "Host", "class", "elevated", "noOverlay", "zIndexOverride", "undefined", "id", "transitionDuration", "concat", "get"], "sourceRoot": ""}