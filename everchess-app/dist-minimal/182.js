"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[182],{6182:(e,n,o)=>{o.r(n),o.d(n,{MoonPayBalanceDepositWidget:()=>K,MoonPayBalanceWithdrawWidget:()=>x,MoonPayBuyWidget:()=>O,MoonPayConsumerKycWidget:()=>N,MoonPayContext:()=>S,MoonPayNftCheckoutWidget:()=>k,MoonPayNftClaimWidget:()=>L,MoonPayProvider:()=>T,MoonPaySellWidget:()=>q,MoonPaySwapWidget:()=>B,MoonPaySwapsCustomerSetupWidget:()=>M,MoonPayTopUpLedgerWidget:()=>P,MoonPayTopUpWidget:()=>D,MoonPayTransactionTrackerWidget:()=>$,MoonPayWithdrawLedgerWidget:()=>j});var t=o(6540),r=o(4848),s="moonpay-react-sdk-embedded-frame",a=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{currencyCode:a,defaultCurrencyCode:i,walletAddress:u,walletAddressTag:l,walletAddresses:d,walletAddressTags:c,colorCode:p,theme:m,themeId:C,language:g,baseCurrencyCode:y,baseCurrencyAmount:h,quoteCurrencyAmount:v,lockAmount:f,email:b,externalTransactionId:w,externalCustomerId:R,paymentMethod:S,redirectURL:T,showAllCurrencies:A,showOnlyCurrencies:W,showWalletAddressForm:I,unsupportedRegionRedirectUrl:U,skipUnsupportedRegionScreen:K,baseOrigin:x,onClose:O,onAuthToken:N,onInitiateDeposit:k,onKmsWalletsCreated:L,onLogin:q,onTransactionCompleted:M,onUnsupportedRegion:B,onSwapsCustomerSetupComplete:P,onCloseOverlay:D,onTransactionCreated:$,auth:j,useWarnBeforeRefresh:E,overlayNode:F}=e,_={apiKey:n,signature:r,currencyCode:a,defaultCurrencyCode:i,walletAddress:u,walletAddressTag:l,walletAddresses:d,walletAddressTags:c,colorCode:p,theme:m,themeId:C,language:g,baseCurrencyCode:y,baseCurrencyAmount:h,quoteCurrencyAmount:v,lockAmount:f,email:b,externalTransactionId:w,externalCustomerId:R,paymentMethod:S,redirectURL:T,showAllCurrencies:A,showOnlyCurrencies:W,showWalletAddressForm:I,unsupportedRegionRedirectUrl:U,skipUnsupportedRegionScreen:K},V={onClose:O,onAuthToken:N,onInitiateDeposit:k,onKmsWalletsCreated:L,onLogin:q,onTransactionCompleted:M,onUnsupportedRegion:B,onSwapsCustomerSetupComplete:P,onCloseOverlay:D,onTransactionCreated:$};return{flow:"buy",variant:e.variant,environment:t,params:_,debug:o,baseOrigin:x,handlers:V,auth:j,useWarnBeforeRefresh:E,containerNodeSelector:`#${s}`,overlayNode:F}},i=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{quoteCurrencyCode:a,defaultCurrencyCode:i,defaultBaseCurrencyCode:u,walletAddress:l,walletAddressTag:d,walletAddresses:c,walletAddressTags:p,colorCode:m,theme:C,themeId:g,language:y,baseCurrencyCode:h,baseCurrencyAmount:v,quoteCurrencyAmount:f,lockAmount:b,email:w,externalTransactionId:R,externalCustomerId:S,paymentMethod:T,redirectURL:A,showAllCurrencies:W,showOnlyCurrencies:I,showWalletAddressForm:U,unsupportedRegionRedirectUrl:K,skipUnsupportedRegionScreen:x,mpWalletId:O,baseOrigin:N,refundWalletAddress:k,refundWalletAddresses:L,onClose:q,onAuthToken:M,onInitiateDeposit:B,onKmsWalletsCreated:P,onLogin:D,onTransactionCompleted:$,onUnsupportedRegion:j,onSwapsCustomerSetupComplete:E,onCloseOverlay:F,onTransactionCreated:_,auth:V,useWarnBeforeRefresh:Y}=e,z={apiKey:n,signature:r,quoteCurrencyCode:a,defaultCurrencyCode:i,defaultBaseCurrencyCode:u,walletAddress:l,walletAddressTag:d,walletAddresses:c,walletAddressTags:p,colorCode:m,theme:C,themeId:g,language:y,baseCurrencyCode:h,baseCurrencyAmount:v,quoteCurrencyAmount:f,lockAmount:b,email:w,externalTransactionId:R,externalCustomerId:S,paymentMethod:T,redirectURL:A,showAllCurrencies:W,showOnlyCurrencies:I,showWalletAddressForm:U,unsupportedRegionRedirectUrl:K,skipUnsupportedRegionScreen:x,mpWalletId:O,refundWalletAddress:k,refundWalletAddresses:L},G={onClose:q,onAuthToken:M,onInitiateDeposit:B,onKmsWalletsCreated:P,onLogin:D,onTransactionCompleted:$,onUnsupportedRegion:j,onSwapsCustomerSetupComplete:E,onCloseOverlay:F,onTransactionCreated:_};return{flow:"sell",variant:e.variant,environment:t,params:z,debug:o,baseOrigin:N,handlers:G,auth:V,useWarnBeforeRefresh:Y,containerNodeSelector:`#${s}`}},u=({props:e,apiKey:n,debug:o,environment:t})=>{const{amount:r,amountCurrencyCode:a,theme:i,themeId:u,externalCustomerId:l,baseOrigin:d,onClose:c,onAuthToken:p,onLogin:m,onInitiateDeposit:C,onSwapsCustomerSetupComplete:g,onCloseOverlay:y,onTransactionCreated:h,onKmsWalletsCreated:v,onTransactionCompleted:f,onUnsupportedRegion:b,auth:w,useWarnBeforeRefresh:R}=e,S={apiKey:n,amount:r,amountCurrencyCode:a,theme:i,themeId:u,externalCustomerId:l},T={onClose:c,onAuthToken:p,onLogin:m,onInitiateDeposit:C,onSwapsCustomerSetupComplete:g,onCloseOverlay:y,onTransactionCreated:h,onKmsWalletsCreated:v,onTransactionCompleted:f,onUnsupportedRegion:b};return{flow:"swapsCustomerSetup",variant:e.variant,environment:t,params:S,debug:o,handlers:T,baseOrigin:d,auth:w,useWarnBeforeRefresh:R,containerNodeSelector:`#${s}`}},l=({props:e,apiKey:n,debug:o,environment:t})=>{const{amount:r,amountCurrencyCode:a,theme:i,themeId:u,baseOrigin:l,onClose:d,onLogin:c,onInitiateDeposit:p,onConsumerKycComplete:m,onKmsWalletsCreated:C,onTransactionCompleted:g,onUnsupportedRegion:y,onCloseOverlay:h,auth:v,useWarnBeforeRefresh:f}=e,b={apiKey:n,amount:r,amountCurrencyCode:a,theme:i,themeId:u},w={onClose:d,onLogin:c,onInitiateDeposit:p,onConsumerKycComplete:m,onKmsWalletsCreated:C,onTransactionCompleted:g,onUnsupportedRegion:y,onCloseOverlay:h};return{flow:"consumerKyc",variant:e.variant,environment:t,params:b,debug:o,handlers:w,baseOrigin:l,auth:v,useWarnBeforeRefresh:f,containerNodeSelector:`#${s}`}},d=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{contractAddress:a,tokenId:i,listingId:u,externalTransactionId:l,baseCurrencyCode:d,redirectURL:c,walletAddress:p,subPartnerName:m,metadata:C,dynamicAssetInfo:g,sellType:y,email:h,quantity:v,baseOrigin:f,onClose:b,onAuthToken:w,onLogin:R,onTransactionCompleted:S,onUnsupportedRegion:T,onInitiateDeposit:A,onKmsWalletsCreated:W,onCloseOverlay:I,onTransactionCreated:U,auth:K,useWarnBeforeRefresh:x}=e,O={apiKey:n,signature:r,contractAddress:a,tokenId:i,listingId:u,externalTransactionId:l,baseCurrencyCode:d,redirectURL:c,walletAddress:p,subPartnerName:m,metadata:C,dynamicAssetInfo:g,sellType:y,email:h,quantity:v},N={onClose:b,onAuthToken:w,onLogin:R,onTransactionCompleted:S,onUnsupportedRegion:T,onInitiateDeposit:A,onKmsWalletsCreated:W,onCloseOverlay:I,onTransactionCreated:U};return{flow:"nft",variant:e.variant,environment:t,params:O,debug:o,handlers:N,auth:K,baseOrigin:f,useWarnBeforeRefresh:x,containerNodeSelector:`#${s}`}},c=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{contractId:a,tokenId:i,initialScreen:u,onClose:l,onAuthToken:d,onLogin:c,onTransactionCompleted:p,onUnsupportedRegion:m,onInitiateDeposit:C,onKmsWalletsCreated:g,onCloseOverlay:y,onTransactionCreated:h,baseOrigin:v,auth:f,useWarnBeforeRefresh:b}=e,w={apiKey:n,signature:r,contractId:a,tokenId:i,initialScreen:u},R={onClose:l,onAuthToken:d,onLogin:c,onTransactionCompleted:p,onUnsupportedRegion:m,onInitiateDeposit:C,onKmsWalletsCreated:g,onCloseOverlay:y,onTransactionCreated:h};return{flow:"claim",variant:e.variant,environment:t,params:w,debug:o,handlers:R,baseOrigin:v,auth:f,useWarnBeforeRefresh:b,containerNodeSelector:`#${s}`}},p=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{externalCustomerId:a,theme:i,themeId:u,currencyCode:l,baseOrigin:d,onClose:c,onAuthToken:p,onInitiateDeposit:m,onKmsWalletsCreated:C,onLogin:g,onTransactionCompleted:y,onUnsupportedRegion:h,onSwapsCustomerSetupComplete:v,onCloseOverlay:f,onTransactionCreated:b,auth:w,useWarnBeforeRefresh:R}=e,S={apiKey:n,signature:r,externalCustomerId:a,theme:i,themeId:u,currencyCode:l},T={onClose:c,onAuthToken:p,onInitiateDeposit:m,onKmsWalletsCreated:C,onLogin:g,onTransactionCompleted:y,onUnsupportedRegion:h,onSwapsCustomerSetupComplete:v,onCloseOverlay:f,onTransactionCreated:b};return{flow:"topup",variant:e.variant,environment:t,params:S,debug:o,handlers:T,baseOrigin:d,auth:w,useWarnBeforeRefresh:R,containerNodeSelector:`#${s}`}},m=({props:e,debug:n,environment:o})=>{const{theme:t,themeId:r,baseOrigin:a,onClose:i,onLogin:u,onTransactionCompleted:l,onUnsupportedRegion:d,onCloseOverlay:c,onTransactionCreated:p,onBalanceDepositTransferConfirmed:m,auth:C,useWarnBeforeRefresh:g}=e,y={theme:t,themeId:r},h={onClose:i,onLogin:u,onTransactionCompleted:l,onUnsupportedRegion:d,onCloseOverlay:c,onTransactionCreated:p,onBalanceDepositTransferConfirmed:m};return{flow:"moonPayBalanceDeposit",variant:e.variant,environment:o,params:y,debug:n,handlers:h,baseOrigin:a,auth:C,useWarnBeforeRefresh:g,containerNodeSelector:`#${s}`}},C=({props:e,debug:n,environment:o})=>{const{theme:t,themeId:r,baseOrigin:a,onClose:i,onLogin:u,onTransactionCompleted:l,onUnsupportedRegion:d,onCloseOverlay:c,onTransactionCreated:p,auth:m,useWarnBeforeRefresh:C}=e,g={theme:t,themeId:r},y={onClose:i,onLogin:u,onTransactionCompleted:l,onUnsupportedRegion:d,onCloseOverlay:c,onTransactionCreated:p};return{flow:"moonPayBalanceWithdraw",variant:e.variant,environment:o,params:g,debug:n,baseOrigin:a,handlers:y,auth:m,useWarnBeforeRefresh:C,containerNodeSelector:`#${s}`}},g=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{externalCustomerId:a,theme:i,themeId:u,baseCurrencyCode:l,baseOrigin:d,onClose:c,onAuthToken:p,onInitiateDeposit:m,onKmsWalletsCreated:C,onLogin:g,onTransactionCompleted:y,onUnsupportedRegion:h,onSwapsCustomerSetupComplete:v,onCloseOverlay:f,onTransactionCreated:b,auth:w,useWarnBeforeRefresh:R}=e,S={apiKey:n,signature:r,externalCustomerId:a,theme:i,themeId:u,baseCurrencyCode:l},T={onClose:c,onAuthToken:p,onInitiateDeposit:m,onKmsWalletsCreated:C,onLogin:g,onTransactionCompleted:y,onUnsupportedRegion:h,onSwapsCustomerSetupComplete:v,onCloseOverlay:f,onTransactionCreated:b};return{flow:"withdraw",variant:e.variant,environment:t,params:S,debug:o,handlers:T,baseOrigin:d,auth:w,useWarnBeforeRefresh:R,containerNodeSelector:`#${s}`}},y=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{walletAddress:a,walletAddressTag:i,refundWalletAddress:u,baseCurrencyCode:l,baseCurrencyAmount:d,quoteCurrencyCode:c,quoteCurrencyAmount:p,colorCode:m,theme:C,themeId:g,externalCustomerId:y,baseOrigin:h,onClose:v,onAuthToken:f,onInitiateDeposit:b,onKmsWalletsCreated:w,onLogin:R,onTransactionCompleted:S,onUnsupportedRegion:T,onSwapsCustomerSetupComplete:A,onCloseOverlay:W,onTransactionCreated:I,auth:U,useWarnBeforeRefresh:K}=e,x={apiKey:n,signature:r,walletAddress:a,walletAddressTag:i,refundWalletAddress:u,baseCurrencyCode:l,baseCurrencyAmount:d,quoteCurrencyCode:c,quoteCurrencyAmount:p,colorCode:m,theme:C,themeId:g,externalCustomerId:y},O={onClose:v,onAuthToken:f,onInitiateDeposit:b,onKmsWalletsCreated:w,onLogin:R,onTransactionCompleted:S,onUnsupportedRegion:T,onSwapsCustomerSetupComplete:A,onCloseOverlay:W,onTransactionCreated:I};return{flow:"swap",variant:e.variant,environment:t,params:x,debug:o,handlers:O,baseOrigin:h,auth:U,useWarnBeforeRefresh:K,containerNodeSelector:`#${s}`}},h=({props:e,debug:n,environment:o})=>{const{transactionId:t,colorCode:r,theme:a,themeId:i,baseOrigin:u,onClose:l,onAuthToken:d,onInitiateDeposit:c,onKmsWalletsCreated:p,onLogin:m,onTransactionCompleted:C,onUnsupportedRegion:g,onSwapsCustomerSetupComplete:y,onCloseOverlay:h,onTransactionCreated:v,auth:f,useWarnBeforeRefresh:b}=e,w={transactionId:t,colorCode:r,theme:a,themeId:i},R={onClose:l,onAuthToken:d,onInitiateDeposit:c,onKmsWalletsCreated:p,onLogin:m,onTransactionCompleted:C,onUnsupportedRegion:g,onSwapsCustomerSetupComplete:y,onCloseOverlay:h,onTransactionCreated:v};return{flow:"transactionTracker",variant:e.variant,environment:o,params:w,baseOrigin:u,debug:n,handlers:R,auth:f,containerNodeSelector:`#${s}`,useWarnBeforeRefresh:b}},v=({props:e,apiKey:n,debug:o,environment:t,signature:r})=>{const{enabledMethods:s,cryptoAmount:i,fromDefaultCurrencyCode:u,fromChainName:l,toChainName:d,testLocation:c}=e,p=a({props:e,apiKey:n,debug:o,environment:t,signature:r}),m={...p.params,enabledMethods:s,cryptoAmount:i,fromDefaultCurrencyCode:u,fromChainName:l,toChainName:d,testLocation:c};return{...p,flow:"partnerTopup",params:m}},f={isLoading:!1,isLoaded:!1},b=()=>{},w={info:b,warn:b,error:b},R=class e{prefix;logger;constructor({logger:e,prefix:n}){this.logger=e,this.prefix=n??["Logger"]}if(n){return n?this:new e({logger:w})}info(...e){this.logger.info(`[${this.prefix}]`,...e)}warn(...e){this.logger.warn(`[${this.prefix}]`,...e)}error(...e){this.logger.error(`[${this.prefix}]`,...e)}},S=(void 0!==window.crypto&&window.crypto.getRandomValues,(0,t.createContext)({apiKey:"",environment:"sandbox",debug:!1})),T=({children:e,apiKey:n,debug:o=!1})=>{const s=function(e){return e.startsWith("pk_live")||e.startsWith("sk_live")?"production":"sandbox"}(n),[a,i]=(0,t.useState)({apiKey:n,environment:s,debug:o}),u=(0,t.useCallback)(async()=>{const e=await async function(e="v1"){return new Promise((n,o)=>{const t=`https://static.moonpay.com/web-sdk/${e}/moonpay-web-sdk.min.js`,r=document.querySelector(`script[src="${t}"]`);f.isLoading=!0;const s=(e=0)=>window.MoonPayWebSdk?(f.isLoading=!1,f.isLoaded=!0,void n(window.MoonPayWebSdk.init)):e>100?(f.isLoading=!1,void o(new Error("Loading MoonPayWebSdk script timed out."))):void setTimeout(()=>s(e+1),100);if(r)s();else{const e=document.createElement("script");e.async=!0,e.src=t,e.addEventListener("load",()=>{f.isLoading=!1,f.isLoaded=!0,n(window.MoonPayWebSdk?.init)}),e.addEventListener("error",()=>{f.isLoading=!1,f.isLoaded=!1,o(new Error("Failed to load MoonPayWebSdk script."))}),document.body.appendChild(e)}})}();i(n=>({...n,init:e}))},[]);return(0,t.useEffect)(()=>{a.init||u()},[u,a.init]),(0,r.jsx)(S.Provider,{value:a,children:e})},A=class extends R{constructor(){super({logger:console,prefix:["MoonPay React SDK"]})}},W=(e,n,o)=>{const r=(0,t.useRef)(null),{init:s,debug:a}=(0,t.useContext)(S),i=(0,t.useRef)((new A).if(a||!1)),u=(0,t.useCallback)(async()=>{if(r.current){if(o||!("walletAddress"in e)&&!("walletAddresses"in e)||i.current.warn("You must provide an `onUrlSignatureRequested` function to sign the URL when using the `walletAddress` or `walletAddresses` prop"),o){const e=r.current.generateUrlForSigning();try{const n=await(o?.(e));if(!n)return;r.current?.updateSignature(n)}catch(e){i.current.error("Something went wrong calling the provided `onUrlSignatureRequested` function:",e)}}n&&r.current?.show()}},[o,n,e]);(0,t.useEffect)(()=>{r.current&&r.current.close();const n=s?.(e)||null;r.current=n,u()},[s,e,u])},I={width:"424px",height:"656px",margin:"32px 28px 32px 28px",border:"1px solid #E5E7EB",borderRadius:"16px",overflow:"hidden"};function U({config:e,onUrlSignatureRequested:n,visible:o=!0,className:t,style:a}){return W(e,o,n),o?(0,r.jsx)("div",{id:s,className:t,style:"embedded"===e.variant?{...I,...a}:void 0}):null}function K(e){const{environment:n}=(0,t.useContext)(S),o=(0,t.useMemo)(()=>m({props:e,environment:n,debug:!1}),[e,n]);return(0,r.jsx)(U,{config:o,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function x(e){const{environment:n}=(0,t.useContext)(S),o=(0,t.useMemo)(()=>C({props:e,environment:n,debug:!1}),[e,n]);return(0,r.jsx)(U,{config:o,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function O(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>a({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function N(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>l({props:e,apiKey:n,environment:o,debug:!1,signature:""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function k(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>d({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function L(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>c({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function q(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>i({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function M(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>u({props:e,apiKey:n,environment:o,debug:!1,signature:""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function B(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>y({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function P(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>p({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function D(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),{visible:s,className:a,style:i,onUrlSignatureRequested:u,signature:l}=e,d=(0,t.useMemo)(()=>v({props:e,apiKey:n,environment:o,debug:!1,signature:l||""}),[e,n,o,l]);return(0,r.jsx)(U,{config:d,onUrlSignatureRequested:u,visible:s,className:a,style:i})}function $(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>h({props:e,apiKey:n,environment:o,debug:!1,signature:""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}function j(e){const{apiKey:n,environment:o}=(0,t.useContext)(S),s=(0,t.useMemo)(()=>g({props:e,apiKey:n,environment:o,debug:!1,signature:e.signature||""}),[e,n,o]);return(0,r.jsx)(U,{config:s,visible:e.visible,onUrlSignatureRequested:e.onUrlSignatureRequested,className:e.className,style:e.style})}}}]);
//# sourceMappingURL=182.js.map