"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[167],{2167:(o,r,c)=>{c.r(r),c.d(r,{cpsl_nav_button:()=>n});var t=c(3029),a=c(2901),l=c(9289),n=function(){return(0,a.A)(function o(r){var c=this;(0,t.A)(this,o),(0,l.r)(this,r),this.cpslNavButtonClick=(0,l.c)(this,"cpslNavButtonClick",7),this.cpslNavButtonSubRouteClick=(0,l.c)(this,"cpslNavButtonSubRouteClick",7),this.handleNavButtonClick=function(){c.cpslNavButtonClick.emit(c.route)},this.handleSubRouteClick=function(o){return function(){c.cpslNavButtonSubRouteClick.emit(o)}},this.disabled=!1,this.exactMainRouteMatch=void 0,this.exactSubRouteMatch=void 0,this.route=void 0,this.subRoutes=void 0,this.path=void 0},[{key:"render",value:function(){var o,r,c=this,t=null===(o=this.subRoutes)||void 0===o?void 0:o.find(function(o){return c.exactSubRouteMatch?c.path==="".concat(c.route,"/").concat(o.value):c.path.includes("".concat(c.route,"/").concat(o.value))}),a=this.exactMainRouteMatch?this.path===this.route:this.path.includes(this.route);return(0,l.h)(l.H,{key:"502ee237751a59264f26e3cc0bb1d98c3b27416c"},(0,l.h)("cpsl-button",{key:"bd3ad1ae9cf793a62c706912cabcf944b1519684",id:this.route,class:{"main-route":!0,selected:!!t||a},fullWidth:!0,variant:"primary",disabled:this.disabled,onClick:this.handleNavButtonClick},(0,l.h)("slot",{key:"45f67ecbb581fda045d786bf4d82ef7740fd4ee7",name:"start"}),(0,l.h)("slot",{key:"53ef86ef731b99344e398162429217adc8807b3f"}),(0,l.h)("slot",{key:"248fdf014c63862a45a9faa5b03a9994c9ddd50c",name:"end"})),(!!t||a)&&!!(null===(r=this.subRoutes)||void 0===r?void 0:r.length)&&(0,l.h)("div",{key:"766e04ef327cb4291a5cde6c38e267b97703dbb0",class:"sub-route-container"},this.subRoutes.map(function(o){return(0,l.h)("cpsl-button",{key:o.value,id:"".concat(c.route,"/").concat(o.value),class:{"sub-route":!0,selected:o.value===(null==t?void 0:t.value)},onClick:c.handleSubRouteClick("".concat(c.route,"/").concat(o.value)),fullWidth:!0,variant:"primary"},o.label)})))}},{key:"el",get:function(){return(0,l.g)(this)}}])}();n.style=":host{display:flex;flex-direction:column;gap:8px;padding:0px;box-sizing:border-box;width:100%;background:var(--cpsl-color-background-4);border-radius:var(--cpsl-border-radius-primary-button);overflow:hidden}.sub-route-container{display:flex;flex-direction:column;gap:8px;border-left:1px solid var(--cpsl-color-background-16);padding-left:16px;margin-left:16px;margin-bottom:8px;margin-right:8px;background:var(--cpsl-color-background-4)}.sub-route{--button-padding-start:8px;--button-padding-end:8px;--button-padding-top:8px;--button-padding-bottom:8px;--button-justify-content:start;--button-primary-color:var(--cpsl-color-background-48);--button-primary-background-color:var(--cpsl-color-background-4);--button-primary-border-color:var(--cpsl-color-background-4);--button-primary-icon-color:var(--cpsl-color-text-primary);--button-primary-disabled-color:var(--cpsl-color-text-primary);--button-primary-disabled-background-color:var(--cpsl-color-background-4);--button-primary-disabled-border-color:var(--cpsl-color-background-4);--button-primary-disabled-icon-color:var(--cpsl-color-text-primary);--button-primary-hover-color:var(--cpsl-color-background-48);--button-primary-hover-background-color:var(--cpsl-color-background-0);--button-primary-hover-border-color:var(--cpsl-color-background-4);--button-primary-hover-icon-color:var(--cpsl-color-background-48);--button-primary-active-color:var(--cpsl-color-text-primary);--button-primary-active-background-color:var(--cpsl-color-background-4);--button-primary-active-border-color:var(--cpsl-color-background-4);--button-primary-active-icon-color:var(--cpsl-color-text-primary)}.sub-route.selected{--button-primary-color:var(--cpsl-color-text-primary);--button-primary-background-color:var(--cpsl-color-background-0);--button-primary-border-color:var(--cpsl-color-background-0);--button-primary-icon-color:var(--cpsl-color-background-48);--button-primary-disabled-color:var(--cpsl-color-background-48);--button-primary-disabled-background-color:var(--cpsl-color-background-0);--button-primary-disabled-border-color:var(--cpsl-color-background-0);--button-primary-disabled-icon-color:var(--cpsl-color-background-48);--button-primary-hover-color:var(--cpsl-color-text-primary);--button-primary-hover-background-color:var(--cpsl-color-background-0);--button-primary-hover-border-color:var(--cpsl-color-background-4);--button-primary-hover-icon-color:var(--cpsl-color-background-48);--button-primary-active-color:var(--cpsl-color-background-48);--button-primary-active-background-color:var(--cpsl-color-background-4);--button-primary-active-border-color:var(--cpsl-color-background-4);--button-primary-active-icon-color:var(--cpsl-color-background-48)}.main-route{--button-padding-start:8px;--button-padding-end:8px;--button-padding-top:8px;--button-padding-bottom:8px;--button-justify-content:start;--button-primary-color:var(--cpsl-color-background-48);--button-primary-background-color:var(--cpsl-color-background-0);--button-primary-border-color:var(--cpsl-color-background-0);--button-primary-icon-color:var(--cpsl-color-background-48);--button-primary-disabled-color:var(--cpsl-color-background-48);--button-primary-disabled-background-color:var(--cpsl-color-background-0);--button-primary-disabled-border-color:var(--cpsl-color-background-0);--button-primary-disabled-icon-color:var(--cpsl-color-background-48);--button-primary-hover-color:var(--cpsl-color-background-48);--button-primary-hover-background-color:var(--cpsl-color-background-4);--button-primary-hover-border-color:var(--cpsl-color-background-4);--button-primary-hover-icon-color:var(--cpsl-color-background-48);--button-primary-active-color:var(--cpsl-color-background-48);--button-primary-active-background-color:var(--cpsl-color-background-4);--button-primary-active-border-color:var(--cpsl-color-background-4);--button-primary-active-icon-color:var(--cpsl-color-background-48)}.main-route.selected{--button-primary-color:var(--cpsl-color-text-primary);--button-primary-background-color:var(--cpsl-color-background-4);--button-primary-border-color:var(--cpsl-color-background-4);--button-primary-icon-color:var(--cpsl-color-text-primary);--button-primary-disabled-color:var(--cpsl-color-text-primary);--button-primary-disabled-background-color:var(--cpsl-color-background-4);--button-primary-disabled-border-color:var(--cpsl-color-background-4);--button-primary-disabled-icon-color:var(--cpsl-color-text-primary);--button-primary-hover-color:var(--cpsl-color-text-primary);--button-primary-hover-background-color:var(--cpsl-color-background-4);--button-primary-hover-border-color:var(--cpsl-color-background-4);--button-primary-hover-icon-color:var(--cpsl-color-text-primary);--button-primary-active-color:var(--cpsl-color-text-primary);--button-primary-active-background-color:var(--cpsl-color-background-4);--button-primary-active-border-color:var(--cpsl-color-background-4);--button-primary-active-icon-color:var(--cpsl-color-text-primary)}"}}]);
//# sourceMappingURL=167.js.map