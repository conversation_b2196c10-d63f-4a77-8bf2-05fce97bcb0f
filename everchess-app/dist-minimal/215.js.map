{"version": 3, "file": "215.js", "mappings": "4LAwBqBA,EAAM,WAezB,SAAAA,EAAYC,IAAsBC,EAAAA,EAAAA,GAAA,KAAAF,GAV1B,KAAAG,OAASC,IAabC,KAAKC,MAFa,iBAATL,EAEII,KAAKE,SAASN,GACF,iBAATA,EAEHI,KAAKG,YAAYP,GAGjBI,KAAKG,YAAYR,EAAOS,IAAMC,KAAKC,OAAOX,EAAOY,IAAMZ,EAAOS,KAAOC,KAAKG,WAEzFR,KAAKS,O,oCAaA,WAA2B,IAAtBC,EAAGC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAAGG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAE/B,OADAX,KAAKe,cACEf,KAAKgB,IAAIhB,KAAKF,OAAQH,EAAOS,IAAKT,EAAOY,IAAKG,EAAKI,E,wBAUrD,WAA2B,IAAnBJ,EAAGC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIM,EAAGN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IAE7B,OADAX,KAAKe,cACEV,KAAKC,MAAMN,KAAKgB,IAAIhB,KAAKF,OAAQH,EAAOS,IAAKT,EAAOY,IAAKG,EAAKO,EAAM,G,2BActE,WAEL,IAFqG,IAArFL,EAAMD,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIO,EAAKP,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,iEACjCQ,EAAM,GACHA,EAAIP,OAASA,GAClBO,GAAOnB,KAAKoB,SAASF,GAEvB,OAAOC,C,yBASF,WAAiF,IAAxED,EAAKP,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,iEACtB,OAAOO,EAAMG,OAAOrB,KAAKsB,QAAQ,EAAGJ,EAAMN,OAAS,GAAI,E,8BAclD,SAAiBW,GACtB,OAAOA,EAAMvB,KAAKsB,QAAQ,EAAGC,EAAMX,OAAS,G,4BAQvC,WAEL,OADAZ,KAAKe,cACEf,KAAKF,OAAS,E,qBAUhB,WACL,IADwB,IAAd0B,EAAUb,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAChBa,KAAe,GACpBxB,KAAKe,a,sBAiBF,WACLf,KAAKF,OAASE,KAAKC,K,4BAMb,WACND,KAAKF,OAASE,KAAKyB,SAASzB,KAAKF,O,GAClC,CAAA4B,IAAA,WAAAC,MAEO,SAASA,GAMf,OAHAA,GAASA,GAAS,IAClBA,GAASA,GAAS,IACTA,GAAS,C,GAEnB,CAAAD,IAAA,MAAAC,MAEO,SAAIC,EAAaC,EAAiBC,EAAiBC,EAAeC,GACxE,OAASJ,EAAMC,IAAYC,EAAUD,IAAaG,EAAQD,GAASA,C,GACpE,CAAAL,IAAA,WAAAC,MAEO,SAASR,GACf,IAAIc,EAAO,EACX,GAAId,EAEF,IADA,IAAMe,EAAIf,EAAIP,OACLuB,EAAI,EAAGA,EAAID,EAAGC,IACrBF,GAAQA,GAAQ,GAAKA,EAAOd,EAAIiB,WAAWD,GAC3CF,GAAQ,EACRA,EAAOjC,KAAKyB,SAASQ,GAGzB,OAAOjC,KAAKG,YAAY8B,E,GACzB,CAAAP,IAAA,cAAAC,MAEO,SAAY/B,GAClB,OAAa,IAATA,EAAmB,EAChBA,C,IACR,CAhLwB,GACDD,EAAAS,KAAe,WACfT,EAAAY,IAAc,W,ICtB3B8B,EAAkB,CAAC,MAAO,SAAU,SAAU,QAAS,OAAQ,UCuDtEC,EAAgB,CACpB,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,IAEZ,CACE,CAAC,EAAG,EAAG,EAAG,GACV,CAAC,EAAG,EAAG,EAAG,KASDC,EAAa,W,uFAOD,O,aAEiB,S,uBAExC,WACE,IAAIC,EAiCmB5C,EACnB6C,EAENC,EAAsFC,EAA/EC,EAAQC,EAAUC,EAEnBC,EACCC,EAAgBC,EAnCrB,OAHiBjD,KAAKiC,OAgCCrC,EA/BiBI,KAAKiC,KAgCzCQ,EAAM,IAAI9C,EAAOC,GAEvB8C,EAAuCQ,EAAYlC,IAAI,SAAAmC,GAAG,OAAIV,EAAInB,QAAQ,EAAG6B,EAAM,EAAE,GAA9EP,GAA+ED,GAAAS,EAAAA,EAAAA,GAAAV,EAAA,IAAzE,GAAEG,EAAQF,EAAA,GAAEG,EAAUH,EAAA,GAE7BI,EAAiB1C,KAAKC,MAAMwC,EAAa,GACxCE,EAAkCF,EAAa,GAAM,EAArCG,EAAwCH,EAAa,GAAK,EArCjEN,EAuCT,CACLa,MAAOhB,EAAOO,GACdU,OAAQhB,EAAcO,GAAU,GAAG7B,IAAI,SAACuC,EAAGpB,GACzC,OAAOA,IAAMY,GAAkBC,EAAwB,IAANO,EAA2C,IAANA,C,GAExFC,UAAWlB,EAAcO,GAAU,GAAG7B,IAAI,SAACyC,EAAGtB,GAAC,OAAMA,IAAMY,GAAkBE,GAAiBQ,EAAI,GAAK,EAASA,CAAC,MAzC/GC,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAjC,IAAA,2CACHkC,MAAO,CACLC,IAAsB,SAAjBrB,aAAK,EAALA,EAAOa,OACZS,OAAyB,YAAjBtB,aAAK,EAALA,EAAOa,OACfU,OAAyB,YAAjBvB,aAAK,EAALA,EAAOa,OACfW,MAAwB,WAAjBxB,aAAK,EAALA,EAAOa,OACdY,KAAuB,UAAjBzB,aAAK,EAALA,EAAOa,OACba,OAAyB,YAAjB1B,aAAK,EAALA,EAAOa,OACfc,QAAQ3B,aAAK,EAALA,EAAOa,SAAUrD,KAAKiC,KAC9BmC,OAAyB,WAAjBpE,KAAKqE,SAEfC,MAAO,CACLC,MAAOvE,KAAKwE,KACZC,OAAQzE,KAAKwE,QAGdhC,aAAK,EAALA,EAAOc,UACNd,aAAK,EAALA,EAAOgB,YACPhB,EAAMc,OAAOtC,IAAI,SAAC0D,EAAUC,GAC1B,OAAOD,GA9GAE,EA8GqBpC,EAAMgB,UAAUmB,IA7GtDjB,EAAAA,EAAAA,GAAA,OACEE,MAAO,CACLiB,SAAuB,IAAbD,EACVE,UAAwB,IAAbF,EACXG,UAAwB,IAAbH,GAEbI,QAAQ,YACRC,MAAM,+BAENvB,EAAAA,EAAAA,GAAA,iBAAa,wBACXA,EAAAA,EAAAA,GAAA,oBACY,UAAS,YACT,UACVwB,EAAE,wOAEJxB,EAAAA,EAAAA,GAAA,oBACY,UAAS,YACT,UACVwB,EAAE,yOAGNxB,EAAAA,EAAAA,GAAA,aACEA,EAAAA,EAAAA,GAAA,YAAUyB,GAAG,kBACXzB,EAAAA,EAAAA,GAAA,QAAMa,MAAM,KAAKE,OAAO,WAjDd,SAACG,GAAgB,OACjClB,EAAAA,EAAAA,GAAA,OACEE,MAAO,CACLiB,SAAuB,IAAbD,EACVE,UAAwB,IAAbF,EACXG,UAAwB,IAAbH,GAEbI,QAAQ,YACRC,MAAM,+BAENvB,EAAAA,EAAAA,GAAA,iBAAa,uBACXA,EAAAA,EAAAA,GAAA,oBACY,UAAS,YACT,UACVwB,EAAE,yOAGNxB,EAAAA,EAAAA,GAAA,aACEA,EAAAA,EAAAA,GAAA,YAAUyB,GAAG,iBACXzB,EAAAA,EAAAA,GAAA,QAAMa,MAAM,KAAKE,OAAO,SAGxB,CAiH0DW,CAAU5C,EAAMgB,UAAUmB,IA9G1E,IAACC,C,OAkHhB,CAxCuB,GA2CpB1B,EAAc,CAACb,EAAOzB,OAAQ0B,EAAc1B,OAAQ,I,QClJjC,6/C", "sources": ["webpack://everchess-app/src/lib/prando.ts", "webpack://everchess-app/src/utils/prand.ts", "webpack://everchess-app/src/components/cpsl-identicon/cpsl-identicon.tsx", "webpack://everchess-app/src/components/cpsl-identicon/cpsl-identicon.scss"], "sourcesContent": ["/*\n * This file contains code from prando, which is licensed under the MIT License:\n *\n * Copyright (c) 2016 <PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\nexport default class Prando {\n  private static readonly MIN: number = -**********; // Int32 min\n  private static readonly MAX: number = **********; // Int32 max\n\n  private _seed: number;\n  private _value = NaN;\n\n  // ================================================================================================================\n  // CONSTRUCTOR ----------------------------------------------------------------------------------------------------\n\n  /**\n   * Generate a new Prando pseudo-random number generator.\n   *\n   * @param seed - A number or string seed that determines which pseudo-random number sequence will be created. Defaults to a random seed based on `Math.random()`.\n   */\n  constructor(seed?: number | string) {\n    if (typeof seed === 'string') {\n      // String seed\n      this._seed = this.hashCode(seed);\n    } else if (typeof seed === 'number') {\n      // Numeric seed\n      this._seed = this.getSafeSeed(seed);\n    } else {\n      // Pseudo-random seed\n      this._seed = this.getSafeSeed(Prando.MIN + Math.floor((Prando.MAX - Prando.MIN) * Math.random()));\n    }\n    this.reset();\n  }\n\n  // ================================================================================================================\n  // PUBLIC INTERFACE -----------------------------------------------------------------------------------------------\n\n  /**\n   * Generates a pseudo-random number between a lower (inclusive) and a higher (exclusive) bounds.\n   *\n   * @param min - The minimum number that can be randomly generated.\n   * @param pseudoMax - The maximum number that can be randomly generated (exclusive).\n   * @return The generated pseudo-random number.\n   */\n  public next(min = 0, pseudoMax = 1): number {\n    this.recalculate();\n    return this.map(this._value, Prando.MIN, Prando.MAX, min, pseudoMax);\n  }\n\n  /**\n   * Generates a pseudo-random integer number in a range (inclusive).\n   *\n   * @param min - The minimum number that can be randomly generated.\n   * @param max - The maximum number that can be randomly generated.\n   * @return The generated pseudo-random number.\n   */\n  public nextInt(min = 10, max = 100): number {\n    this.recalculate();\n    return Math.floor(this.map(this._value, Prando.MIN, Prando.MAX, min, max + 1));\n  }\n\n  /**\n   * Generates a pseudo-random string sequence of a particular length from a specific character range.\n   *\n   * Note: keep in mind that creating a random string sequence does not guarantee uniqueness; there is always a\n   * 1 in (char_length^string_length) chance of collision. For real unique string ids, always check for\n   * pre-existing ids, or employ a robust GUID/UUID generator.\n   *\n   * @param length - Length of the string to be generated.\n   * @param chars - Characters that are used when creating the random string. Defaults to all alphanumeric chars (A-Z, a-z, 0-9).\n   * @return The generated string sequence.\n   */\n  public nextString(length = 16, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {\n    let str = '';\n    while (str.length < length) {\n      str += this.nextChar(chars);\n    }\n    return str;\n  }\n\n  /**\n   * Generates a pseudo-random string of 1 character specific character range.\n   *\n   * @param chars - Characters that are used when creating the random string. Defaults to all alphanumeric chars (A-Z, a-z, 0-9).\n   * @return The generated character.\n   */\n  public nextChar(chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {\n    return chars.substr(this.nextInt(0, chars.length - 1), 1);\n  }\n\n  /**\n   * Picks a pseudo-random item from an array. The array is left unmodified.\n   *\n   * Note: keep in mind that while the returned item will be random enough, picking one item from the array at a time\n   * does not guarantee nor imply that a sequence of random non-repeating items will be picked. If you want to\n   * *pick items in a random order* from an array, instead of *pick one random item from an array*, it's best to\n   * apply a *shuffle* transformation to the array instead, then read it linearly.\n   *\n   * @param array - Array of any type containing one or more candidates for random picking.\n   * @return An item from the array.\n   */\n  public nextArrayItem<T>(array: T[]): T {\n    return array[this.nextInt(0, array.length - 1)];\n  }\n\n  /**\n   * Generates a pseudo-random boolean.\n   *\n   * @return A value of true or false.\n   */\n  public nextBoolean(): boolean {\n    this.recalculate();\n    return this._value > 0.5;\n  }\n\n  /**\n   * Skips ahead in the sequence of numbers that are being generated. This is equivalent to\n   * calling next() a specified number of times, but faster since it doesn't need to map the\n   * new random numbers to a range and return it.\n   *\n   * @param iterations - The number of items to skip ahead.\n   */\n  public skip(iterations = 1): void {\n    while (iterations-- > 0) {\n      this.recalculate();\n    }\n  }\n\n  /**\n   * Reset the pseudo-random number sequence back to its starting seed. Further calls to next()\n   * will then produce the same sequence of numbers it had produced before. This is equivalent to\n   * creating a new Prando instance with the same seed as another Prando instance.\n   *\n   * Example:\n   * let rng = new Prando(12345678);\n   * console.log(rng.next()); // 0.6177754114889017\n   * console.log(rng.next()); // 0.5784605181725837\n   * rng.reset();\n   * console.log(rng.next()); // 0.6177754114889017 again\n   * console.log(rng.next()); // 0.5784605181725837 again\n   */\n  public reset(): void {\n    this._value = this._seed;\n  }\n\n  // ================================================================================================================\n  // PRIVATE INTERFACE ----------------------------------------------------------------------------------------------\n\n  private recalculate(): void {\n    this._value = this.xorshift(this._value);\n  }\n\n  private xorshift(value: number): number {\n    // Xorshift*32\n    // Based on George Marsaglia's work: http://www.jstatsoft.org/v08/i14/paper\n    value ^= value << 13;\n    value ^= value >> 17;\n    value ^= value << 5;\n    return value;\n  }\n\n  private map(val: number, minFrom: number, maxFrom: number, minTo: number, maxTo: number): number {\n    return ((val - minFrom) / (maxFrom - minFrom)) * (maxTo - minTo) + minTo;\n  }\n\n  private hashCode(str: string): number {\n    let hash = 0;\n    if (str) {\n      const l = str.length;\n      for (let i = 0; i < l; i++) {\n        hash = (hash << 5) - hash + str.charCodeAt(i);\n        hash |= 0;\n        hash = this.xorshift(hash);\n      }\n    }\n    return this.getSafeSeed(hash);\n  }\n\n  private getSafeSeed(seed: number): number {\n    if (seed === 0) return 1;\n    return seed;\n  }\n}\n", "import Prando from '../lib/prando.js';\n\nexport type Color = 'red' | 'orange' | 'yellow' | 'green' | 'blue' | 'purple';\n\nexport const COLORS: Color[] = ['red', 'orange', 'yellow', 'green', 'blue', 'purple'];\n\nconst GRADIENTS = {\n  red: ['#FF4270', '#FF7C7C'],\n  orange: ['#F45532', '#FF9B63'],\n  yellow: ['#FFA756', '#FBFF47'],\n  green: ['#0CAE60', '#7BFFD0'],\n  blue: ['#476FFF', '#47C8FF'],\n  purple: ['#9747FF', '#DA47FF'],\n};\n\nexport function getPseudoRandomBackground(seed: string): string {\n  const rng = new Prando(seed);\n\n  const [start, stop] = GRADIENTS[COLORS[rng.nextInt(0, COLORS.length - 1)]];\n\n  return `linear-gradient(136deg, ${start} 6.86%, ${stop} 93.78%)`;\n}\n", "import { Component, Host, Prop, h } from '@stencil/core';\nimport Prando from '../../lib/prando.js';\nimport { Color, COLORS } from '../../utils/prand.js';\n\nconst SingleArc = (rotation: number) => (\n  <svg\n    class={{\n      rotate90: rotation === 1,\n      rotate180: rotation === 2,\n      rotate270: rotation === 3,\n    }}\n    viewBox=\"0 0 12 12\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g clip-path=\"url(#clip0_674_66)\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\"\n      />\n    </g>\n    <defs>\n      <clipPath id=\"clip0_674_66\">\n        <rect width=\"12\" height=\"12\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\n\nconst DoubleArc = (rotation: number) => (\n  <svg\n    class={{\n      rotate90: rotation === 1,\n      rotate180: rotation === 2,\n      rotate270: rotation === 3,\n    }}\n    viewBox=\"0 0 12 12\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g clip-path=\"url(#clip0_674_255)\">\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z\"\n      />\n      <path\n        fill-rule=\"evenodd\"\n        clip-rule=\"evenodd\"\n        d=\"M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18ZM12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\"\n      />\n    </g>\n    <defs>\n      <clipPath id=\"clip0_674_255\">\n        <rect width=\"12\" height=\"12\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\n\nconst BASE_PATTERNS = [\n  [\n    [0, 0, 0, 0],\n    [0, 1, 3, 2],\n  ],\n  [\n    [1, 1, 0, 0],\n    [0, 1, 3, 2],\n  ],\n  [\n    [0, 1, 0, 1],\n    [0, 1, 3, 2],\n  ],\n  [\n    [0, 0, 1, 1],\n    [0, 1, 3, 2],\n  ],\n  [\n    [1, 0, 1, 0],\n    [0, 1, 3, 2],\n  ],\n  [\n    [1, 1, 1, 1],\n    [0, 1, 3, 2],\n  ],\n  [\n    [0, 0, 0, 0],\n    [2, 3, 1, 0],\n  ],\n  [\n    [1, 1, 1, 1],\n    [2, 3, 1, 0],\n  ],\n  [\n    [1, 1, 1, 1],\n    [0, 1, 2, 3],\n  ],\n];\n\n@Component({\n  tag: 'cpsl-identicon',\n  styleUrl: 'cpsl-identicon.scss',\n  shadow: true,\n})\nexport class CpslIdenticon {\n  @Prop() hash?: string | undefined;\n\n  /**\n   *  The CSS width and height of the identicon.\n   *  Default is: 40px.\n   */\n  @Prop() size: string = '40px';\n\n  @Prop() variant: 'default' | 'avatar' = 'default';\n\n  render() {\n    let props;\n    const isEmpty = !this.hash;\n    if (!isEmpty) props = getIdenticonProps(this.hash);\n\n    return (\n      <Host\n        class={{\n          red: props?.color === 'red',\n          orange: props?.color === 'orange',\n          yellow: props?.color === 'yellow',\n          green: props?.color === 'green',\n          blue: props?.color === 'blue',\n          purple: props?.color === 'purple',\n          empty: !props?.color && !this.hash,\n          avatar: this.variant === 'avatar',\n        }}\n        style={{\n          width: this.size,\n          height: this.size,\n        }}\n      >\n        {props?.shapes &&\n          props?.rotations &&\n          props.shapes.map((isDouble, index) => {\n            return isDouble ? DoubleArc(props.rotations[index]) : SingleArc(props.rotations[index]);\n          })}\n      </Host>\n    );\n  }\n}\n\nconst PRANDO_INTS = [COLORS.length, BASE_PATTERNS.length, 16];\n\nfunction getIdenticonProps(seed: string): { color: Color; shapes: boolean[]; rotations: number[] } {\n  const rng = new Prando(seed);\n\n  const [iColor, iPattern, iDeviation] = PRANDO_INTS.map(len => rng.nextInt(0, len - 1));\n\n  const deviationIndex = Math.floor(iDeviation / 4);\n  const [isDeviateShape, isDeviateFlip] = [iDeviation % 2 === 1, iDeviation % 4 >= 2];\n\n  return {\n    color: COLORS[iColor],\n    shapes: BASE_PATTERNS[iPattern][0].map((s, i) => {\n      return i === deviationIndex ? (isDeviateShape ? (s === 1 ? false : true) : s === 1) : s === 1;\n    }),\n    rotations: BASE_PATTERNS[iPattern][1].map((r, i) => (i === deviationIndex ? (isDeviateFlip ? (r + 2) % 4 : r) : r)),\n  };\n}\n", ":host {\n  --identicon-background-red: linear-gradient(136deg, #ff4270 6.86%, #ff7c7c 93.78%);\n  --identicon-background-orange: linear-gradient(136deg, #f45532 6.86%, #ff9b63 93.78%);\n  --identicon-background-yellow: linear-gradient(136deg, #ffa756 6.86%, #fbff47 93.78%);\n  --identicon-background-green: linear-gradient(136deg, #0cae60 6.86%, #7bffd0 93.78%);\n  --identicon-background-blue: linear-gradient(136deg, #476fff 6.86%, #47c8ff 93.78%);\n  --identicon-background-purple: linear-gradient(136deg, #9747ff 6.86%, #da47ff 93.78%);\n  --identicon-background-empty: linear-gradient(136deg, #aaaaaa 6.86%, #999999 93.78%);\n\n  display: block;\n  aspect-ratio: 1;\n  border-radius: 25%;\n  position: relative;\n  border: 1px solid var(--cpsl-color-background-8);\n\n  & > svg {\n    fill: rgba(255, 255, 255, 0.6);\n    position: absolute;\n    width: 30%;\n  }\n\n  & > svg.rotate90 {\n    transform: rotate(0.25turn);\n  }\n\n  & > svg.rotate180 {\n    transform: rotate(0.5turn);\n  }\n\n  & > svg.rotate270 {\n    transform: rotate(0.75turn);\n  }\n\n  & > svg:nth-child(1) {\n    right: 50%;\n    bottom: 50%;\n  }\n\n  & > svg:nth-child(2) {\n    left: 50%;\n    bottom: 50%;\n  }\n\n  & > svg:nth-child(3) {\n    right: 50%;\n    top: 50%;\n  }\n\n  & > svg:nth-child(4) {\n    left: 50%;\n    top: 50%;\n  }\n}\n\n:host(.red) {\n  background: var(--identicon-background-red);\n}\n\n:host(.orange) {\n  background: var(--identicon-background-orange);\n}\n\n:host(.green) {\n  background: var(--identicon-background-green);\n}\n\n:host(.yellow) {\n  background: var(--identicon-background-yellow);\n}\n\n:host(.blue) {\n  background: var(--identicon-background-blue);\n}\n\n:host(.purple) {\n  background: var(--identicon-background-purple);\n}\n\n:host(.empty) {\n  background: var(--identicon-background-empty);\n}\n\n:host(.avatar) {\n  border-radius: 1000px;\n}\n"], "names": ["P<PERSON><PERSON>", "seed", "_classCallCheck", "_value", "NaN", "this", "_seed", "hashCode", "getSafeSeed", "MIN", "Math", "floor", "MAX", "random", "reset", "min", "arguments", "length", "undefined", "pseudoMax", "recalculate", "map", "max", "chars", "str", "nextChar", "substr", "nextInt", "array", "iterations", "xorshift", "key", "value", "val", "minFrom", "maxFrom", "minTo", "maxTo", "hash", "l", "i", "charCodeAt", "COLORS", "BASE_PATTERNS", "CpslIdenticon", "props", "rng", "_PRANDO_INTS$map", "_PRANDO_INTS$map2", "iColor", "iPattern", "iDeviation", "deviationIndex", "isDeviateShape", "isDeviateFlip", "PRANDO_INTS", "len", "_slicedToArray", "color", "shapes", "s", "rotations", "r", "h", "Host", "class", "red", "orange", "yellow", "green", "blue", "purple", "empty", "avatar", "variant", "style", "width", "size", "height", "isDouble", "index", "rotation", "rotate90", "rotate180", "rotate270", "viewBox", "xmlns", "d", "id", "SingleArc"], "sourceRoot": ""}