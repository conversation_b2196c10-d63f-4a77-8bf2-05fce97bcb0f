"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[883],{1883:(e,t,i)=>{i.r(t),i.d(t,{cpsl_row:()=>c});var s=i(4467),a=i(3029),r=i(2901),n=i(9289),c=function(){return(0,r.A)(function e(t){(0,a.A)(this,e),(0,n.r)(this,t),this.col=!1,this.align="center",this.justify="center",this.gap="8px"},[{key:"render",value:function(){return(0,n.h)(n.H,{key:"355dfc5748c22b4569db7835e8f2f82e45fdff2e",style:(0,s.A)((0,s.A)((0,s.A)((0,s.A)({},"--align",this.align),"--justify",this.justify),"--gap",this.gap.toString()),"--direction",this.col?"column":"row")},(0,n.h)("slot",{key:"1025e7cc78b6a9faee3c7154a14f3218959dabe6"}))}}])}();c.style=":host{display:flex;flex-direction:var(--direction, row);align-items:var(--align);justify-content:var(--justify);gap:var(--gap);flex-wrap:wrap}"}}]);
//# sourceMappingURL=883.js.map