"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[87],{87:(d,a,r)=>{r.r(a),r.d(a,{cpsl_info_box:()=>i});var e=r(3029),o=r(2901),n=r(9289),i=function(){return(0,o.A)(function d(a){(0,e.A)(this,d),(0,n.r)(this,a)},[{key:"render",value:function(){return(0,n.h)(n.H,{key:"93726eb6ef05c505f409ec5b313e7db50c0d0a78"},(0,n.h)("div",{key:"4cfe6525e289eca68319c9651efe20b640da05d3",class:"info-box-container"},(0,n.h)("slot",{key:"81955b1e2f49839cddd514f00777de3870846929"})))}}])}();i.style=":host{--box-border-radius:var(--cpsl-border-radius-info-box);--box-padding-top:16px;--box-padding-bottom:16px;--box-padding-start:16px;--box-padding-end:16px;--box-border-width:1px}.info-box-container{border-radius:var(--input-border-radius);-webkit-padding-start:var(--box-padding-start);padding-inline-start:var(--box-padding-start);-webkit-padding-end:var(--box-padding-end);padding-inline-end:var(--box-padding-end);padding-top:var(--box-padding-top);padding-bottom:var(--box-padding-bottom);display:flex;background:var(--cpsl-color-background-4);border-radius:var(--box-border-radius)}"}}]);
//# sourceMappingURL=87.js.map