{"version": 3, "file": "182.js", "mappings": "4kBAIIA,EAA8B,mCAG9BC,EAAwB,EAC1BC,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,aAEJC,EAAY,oBACZC,EAAmB,cACnBC,EAAa,iBACbC,EAAgB,gBAChBC,EAAe,kBACfC,EAAiB,UACjBC,EAAS,MACTC,EAAK,QACLC,EAAO,SACPC,EAAQ,iBACRC,EAAgB,mBAChBC,EAAkB,oBAClBC,EAAmB,WACnBC,EAAU,MACVC,EAAK,sBACLC,EAAqB,mBACrBC,EAAkB,cAClBC,EAAa,YACbC,EAAW,kBACXC,EAAiB,mBACjBC,EAAkB,sBAClBC,EAAqB,6BACrBC,EAA4B,4BAC5BC,EAA2B,WAC3BC,EAAU,QAEVC,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,EAAoB,YAEpBC,GACE1C,EACE2C,EAAS,CACb1C,SACAG,YACAC,eACAC,sBACAC,gBACAC,mBACAC,kBACAC,oBACAC,YACAC,QACAC,UACAC,WACAC,mBACAC,qBACAC,sBACAC,aACAC,QACAC,wBACAC,qBACAC,gBACAC,cACAC,oBACAC,qBACAC,wBACAC,+BACAC,+BAEIgB,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,MACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA2B,aACAe,WACAJ,OACAC,uBACAM,sBAAuB,IAAIjD,IAC3B4C,gBAGAM,EAAyB,EAC3BhD,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,kBAEJ6C,EAAiB,oBACjB3C,EAAmB,wBACnB4C,EAAuB,cACvB3C,EAAa,iBACbC,EAAgB,gBAChBC,EAAe,kBACfC,EAAiB,UACjBC,EAAS,MACTC,EAAK,QACLC,EAAO,SACPC,EAAQ,iBACRC,EAAgB,mBAChBC,EAAkB,oBAClBC,EAAmB,WACnBC,EAAU,MACVC,EAAK,sBACLC,EAAqB,mBACrBC,EAAkB,cAClBC,EAAa,YACbC,EAAW,kBACXC,EAAiB,mBACjBC,EAAkB,sBAClBC,EAAqB,6BACrBC,EAA4B,4BAC5BC,EAA2B,WAC3BuB,EAAU,WACVtB,EAAU,oBACVuB,EAAmB,sBACnBC,EAAqB,QAErBvB,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACA6C,oBACA3C,sBACA4C,0BACA3C,gBACAC,mBACAC,kBACAC,oBACAC,YACAC,QACAC,UACAC,WACAC,mBACAC,qBACAC,sBACAC,aACAC,QACAC,wBACAC,qBACAC,gBACAC,cACAC,oBACAC,qBACAC,wBACAC,+BACAC,8BACAuB,aACAC,sBACAC,yBAEIT,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,OACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA2B,aACAe,WACAJ,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3BwD,EAAuC,EACzCtD,QACAC,SACAC,QACAC,kBAEA,MAAM,OACJoD,EAAM,mBACNC,EAAkB,MAClB5C,EAAK,QACLC,EAAO,mBACPQ,EAAkB,WAClBQ,EAAU,QAEVC,EAAO,YACPC,EAAW,QACXG,EAAO,kBACPF,EAAiB,6BACjBK,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,oBACpBN,EAAmB,uBACnBE,EAAsB,oBACtBC,EAAmB,KAEnBI,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAsD,SACAC,qBACA5C,QACAC,UACAQ,sBAEIuB,EAAW,CACfd,UACAC,cACAG,UACAF,oBACAK,+BACAC,iBACAC,uBACAN,sBACAE,yBACAC,uBAEF,MAAO,CACLS,KAAM,qBACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B2D,EAAgC,EAClCzD,QACAC,SACAC,QACAC,kBAEA,MAAM,OACJoD,EAAM,mBACNC,EAAkB,MAClB5C,EAAK,QACLC,EAAO,WACPgB,EAAU,QAEVC,EAAO,QACPI,EAAO,kBACPF,EAAiB,sBACjB0B,EAAqB,oBACrBzB,EAAmB,uBACnBE,EAAsB,oBACtBC,EAAmB,eACnBE,EAAc,KAEdE,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAsD,SACAC,qBACA5C,QACAC,WAEI+B,EAAW,CACfd,UACAI,UACAF,oBACA0B,wBACAzB,sBACAE,yBACAC,sBACAE,kBAEF,MAAO,CACLO,KAAM,cACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B6D,EAAwB,EAC1B3D,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,gBAEJwD,EAAe,QACfC,EAAO,UACPC,EAAS,sBACT1C,EAAqB,iBACrBL,EAAgB,YAChBQ,EAAW,cACXhB,EAAa,eACbwD,EAAc,SACdC,EAAQ,iBACRC,EAAgB,SAChBC,EAAQ,MACR/C,EAAK,SACLgD,EAAQ,WACRtC,EAAU,QAEVC,EAAO,YACPC,EAAW,QACXG,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,kBACnBJ,EAAiB,oBACjBC,EAAmB,eACnBK,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACAwD,kBACAC,UACAC,YACA1C,wBACAL,mBACAQ,cACAhB,gBACAwD,iBACAC,WACAC,mBACAC,WACA/C,QACAgD,YAEIvB,EAAW,CACfd,UACAC,cACAG,UACAC,yBACAC,sBACAJ,oBACAC,sBACAK,iBACAC,wBAEF,MAAO,CACLM,KAAM,MACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAJ,OACAX,aACAY,uBACAM,sBAAuB,IAAIjD,MAG3BsE,EAA0B,EAC5BpE,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,WACJiE,EAAU,QACVR,EAAO,cACPS,EAAa,QAEbxC,EAAO,YACPC,EAAW,QACXG,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,kBACnBJ,EAAiB,oBACjBC,EAAmB,eACnBK,EAAc,qBACdC,EAAoB,WACpBV,EAAU,KAEVW,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACAiE,aACAR,UACAS,iBAEI1B,EAAW,CACfd,UACAC,cACAG,UACAC,yBACAC,sBACAJ,oBACAC,sBACAK,iBACAC,wBAEF,MAAO,CACLM,KAAM,QACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3ByE,EAAgC,EAClCvE,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,mBAEJiB,EAAkB,MAClBT,EAAK,QACLC,EAAO,aACPR,EAAY,WACZwB,EAAU,QAEVC,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACAiB,qBACAT,QACAC,UACAR,gBAEIuC,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,QACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B0E,EAA0C,EAC5CxE,QACAE,QACAC,kBAEA,MAAM,MACJS,EAAK,QACLC,EAAO,WACPgB,EAAU,QAEVC,EAAO,QACPI,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,eACnBE,EAAc,qBACdC,EAAoB,kCACpBkC,EAAiC,KAEjCjC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb/B,QACAC,WAEI+B,EAAW,CACfd,UACAI,UACAC,yBACAC,sBACAE,iBACAC,uBACAkC,qCAEF,MAAO,CACL5B,KAAM,wBACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B4E,EAA2C,EAC7C1E,QACAE,QACAC,kBAEA,MAAM,MACJS,EAAK,QACLC,EAAO,WACPgB,EAAU,QAEVC,EAAO,QACPI,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,eACnBE,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb/B,QACAC,WAEI+B,EAAW,CACfd,UACAI,UACAC,yBACAC,sBACAE,iBACAC,wBAEF,MAAO,CACLM,KAAM,yBACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA2B,aACAe,WACAJ,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B6E,EAAmC,EACrC3E,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,mBAEJiB,EAAkB,MAClBT,EAAK,QACLC,EAAO,iBACPE,EAAgB,WAChBc,EAAU,QAEVC,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACAiB,qBACAT,QACAC,UACAE,oBAEI6B,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,WACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B8E,EAAyB,EAC3B5E,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,cAEJG,EAAa,iBACbC,EAAgB,oBAChB4C,EAAmB,iBACnBrC,EAAgB,mBAChBC,EAAkB,kBAClBiC,EAAiB,oBACjBhC,EAAmB,UACnBN,EAAS,MACTC,EAAK,QACLC,EAAO,mBACPQ,EAAkB,WAClBQ,EAAU,QAEVC,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACb1C,SACAG,YACAG,gBACAC,mBACA4C,sBACArC,mBACAC,qBACAiC,oBACAhC,sBACAN,YACAC,QACAC,UACAQ,sBAEIuB,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,OACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAzC,QACA0C,WACAf,aACAW,OACAC,uBACAM,sBAAuB,IAAIjD,MAG3B+E,EAAuC,EACzC7E,QACAE,QACAC,kBAEA,MAAM,cAEJ2E,EAAa,UACbnE,EAAS,MACTC,EAAK,QACLC,EAAO,WACPgB,EAAU,QAEVC,EAAO,YACPC,EAAW,kBACXC,EAAiB,oBACjBC,EAAmB,QACnBC,EAAO,uBACPC,EAAsB,oBACtBC,EAAmB,6BACnBC,EAA4B,eAC5BC,EAAc,qBACdC,EAAoB,KAEpBC,EAAI,qBACJC,GACEzC,EACE2C,EAAS,CACbmC,gBACAnE,YACAC,QACAC,WAEI+B,EAAW,CACfd,UACAC,cACAC,oBACAC,sBACAC,UACAC,yBACAC,sBACAC,+BACAC,iBACAC,wBAEF,MAAO,CACLM,KAAM,qBACNC,QAAS9C,EAAM8C,QACf3C,cACAwC,SACAd,aACA3B,QACA0C,WACAJ,OACAO,sBAAuB,IAAIjD,IAC3B2C,yBAGAsC,EAAiC,EACnC/E,QACAC,SACAC,QACAC,cACAC,gBAEA,MAAM,eACJ4E,EAAc,aACdC,EAAY,wBACZC,EAAuB,cACvBC,EAAa,YACbC,EAAW,aACXC,GACErF,EACEsF,EAAiBvF,EAAsB,CAC3CC,QACAC,SACAC,QACAC,cACAC,cAEIuC,EAAS,IACV2C,EAAe3C,OAClBqC,iBACAC,eACAC,0BACAC,gBACAC,cACAC,gBAEF,MAAO,IACFC,EACHzC,KAAM,eACNF,WAKA4C,EAAsB,CACxBC,WAAW,EACXC,UAAU,GAwDRC,EAAO,OACPC,EAAa,CACfC,KAAMF,EACNG,KAAMH,EACNI,MAAOJ,GAELK,EAAS,MAAMC,EACjBC,OACAC,OACA,WAAAC,EAAY,OACVD,EAAM,OACND,IAEAG,KAAKF,OAASA,EACdE,KAAKH,OAASA,GAAU,CAAC,SAC3B,CACA,GAAGI,GACD,OAAOA,EAAYD,KAAO,IAAIJ,EAAQ,CAAEE,OAAQP,GAClD,CACA,IAAAC,IAAQU,GACNF,KAAKF,OAAON,KAAK,IAAIQ,KAAKH,aAAcK,EAC1C,CACA,IAAAT,IAAQS,GACNF,KAAKF,OAAOL,KAAK,IAAIO,KAAKH,aAAcK,EAC1C,CACA,KAAAR,IAASQ,GACPF,KAAKF,OAAOJ,MAAM,IAAIM,KAAKH,aAAcK,EAC3C,GA+BEC,QAfkC,IAAlBC,OAAOC,QAAiCD,OAAOC,OAAOC,iBAerD,IAAAC,eAAc,CACjC1G,OAAQ,GACRE,YAAa,UACbD,OAAO,KAEL0G,EAAkB,EACpBC,WACA5G,SACAC,SAAQ,MAER,MAAMC,EAzER,SAAkCF,GAChC,OAAOA,EAAO6G,WAAW,YAAc7G,EAAO6G,WAAW,WAAa,aAAe,SACvF,CAuEsBC,CAAyB9G,IACtC+G,EAAcC,IAAmB,IAAAC,UAAU,CAChDjH,SACAE,cACAD,UAEIiH,GAAO,IAAAC,aAAaC,UACxB,MAAMC,QAjIVD,eAA2BE,EAAU,MACnC,OAAO,IAAIC,QAAQ,CAACC,EAASC,KAC3B,MAAMC,EAAY,sCAAsCJ,2BAClDK,EAAiBC,SAASC,cAAc,eAAeH,OAC7DpC,EAAoBC,WAAY,EAChC,MAAMuC,EAAc,CAACC,EAAQ,IACvBxB,OAAOyB,eACT1C,EAAoBC,WAAY,EAChCD,EAAoBE,UAAW,OAC/BgC,EAAQjB,OAAOyB,cAAcC,OAG3BF,EAAQ,KACVzC,EAAoBC,WAAY,OAChCkC,EAAO,IAAIS,MAAM,kDAGnBC,WAAW,IAAML,EAAYC,EAAQ,GAAI,KAE3C,GAAIJ,EACFG,QACK,CACL,MAAMM,EAASR,SAASS,cAAc,UACtCD,EAAOhB,OAAQ,EACfgB,EAAOE,IAAMZ,EACbU,EAAOG,iBAAiB,OAAQ,KAC9BjD,EAAoBC,WAAY,EAChCD,EAAoBE,UAAW,EAC/BgC,EAAQjB,OAAOyB,eAAeC,QAEhCG,EAAOG,iBAAiB,QAAS,KAC/BjD,EAAoBC,WAAY,EAChCD,EAAoBE,UAAW,EAC/BiC,EAAO,IAAIS,MAAM,2CAEnBN,SAASY,KAAKC,YAAYL,EAC5B,GAEJ,CA2F0BM,GACtB1B,EAAiB2B,IAAU,IAAMA,EAAOV,KAAMZ,MAC7C,IAIH,OAHA,IAAAuB,WAAW,KACJ7B,EAAakB,MAAMf,KACvB,CAACA,EAAMH,EAAakB,QACA,IAAAY,KAAIvC,EAAewC,SAAU,CAAEC,MAAOhC,EAAcH,cAOzEoC,EAAU,cAAclD,EAC1B,WAAAI,GACE+C,MAAM,CAAEhD,OAAQiD,QAASlD,OAAQ,CAAC,sBACpC,GAIEmD,EAAS,CAACC,EAAQC,EAASC,KAC7B,MAAMC,GAAM,IAAAC,QAAQ,OACd,KAAEvB,EAAI,MAAEhI,IAAU,IAAAwJ,YAAWnD,GAC7BL,GAAS,IAAAuD,SAAQ,IAAIR,GAAUU,GAAGzJ,IAAS,IAC3C0J,GAA+B,IAAAxC,aAAaC,UAChD,GAAKmC,EAAIK,QAAT,CAQA,GALKN,KAA4B,kBAAmBF,MAAU,oBAAqBA,IACjFnD,EAAO2D,QAAQhE,KACb,mIAGA0D,EAAyB,CAC3B,MAAMO,EAAMN,EAAIK,QAAQE,wBACxB,IACE,MAAMC,QAAyBT,IAA0BO,IACzD,IAAKE,EACH,OAEFR,EAAIK,SAASI,gBAAgBD,EAC/B,CAAE,MAAOE,GACPhE,EAAO2D,QAAQ/D,MACb,gFACAoE,EAEJ,CACF,CACIZ,GACFE,EAAIK,SAASM,MAtBf,GAwBC,CAACZ,EAAyBD,EAASD,KACtC,IAAAR,WAAW,KACLW,EAAIK,SACNL,EAAIK,QAAQO,QAEd,MAAMC,EAASnC,IAAOmB,IAAW,KACjCG,EAAIK,QAAUQ,EACdT,KACC,CAAC1B,EAAMmB,EAAQO,KAKhBU,EAAgB,CAClBC,MAAO,QACPC,OAAQ,QACRC,OAAQ,sBACRC,OAAQ,oBACRC,aAAc,OACdC,SAAU,UAEZ,SAASC,GAAc,OACrBxB,EAAM,wBACNE,EAAuB,QACvBD,GAAU,EAAI,UACdwB,EAAS,MACTC,IAGA,OADA3B,EAAOC,EAAQC,EAASC,GACpBD,GACqB,IAAAR,KACrB,MACA,CACEkC,GAAIlL,EACJgL,YACAC,MAA0B,aAAnB1B,EAAOvG,QAAyB,IAClCwH,KACAS,QACD,IAIH,IACT,CAIA,SAASE,EAA4BjL,GACnC,MAAM,YAAEG,IAAgB,IAAAuJ,YAAYnD,GAC9B8C,GAAS,IAAA6B,SACb,IAAM1G,EAAwC,CAC5CxE,QACAG,cACAD,OAAO,IAET,CAACF,EAAOG,IAEV,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASI,EAA6BnL,GACpC,MAAM,YAAEG,IAAgB,IAAAuJ,YAAYnD,GAC9B8C,GAAS,IAAA6B,SACb,IAAMxG,EAAyC,CAC7C1E,QACAG,cACAD,OAAO,IAET,CAACF,EAAOG,IAEV,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASK,EAAiBpL,GACxB,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAMnL,EAAsB,CAC1BC,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASM,EAAyBrL,GAChC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAMzH,EAA8B,CAClCzD,QACAC,SACAE,cACAD,OAAO,EACPE,UAAW,KAEb,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASO,EAAyBtL,GAChC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAMvH,EAAsB,CAC1B3D,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASQ,EAAsBvL,GAC7B,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAM9G,EAAwB,CAC5BpE,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASS,EAAkBxL,GACzB,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAMlI,EAAuB,CAC3BhD,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASU,EAAgCzL,GACvC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAYnD,GACtC8C,GAAS,IAAA6B,SACb,IAAM5H,EAAqC,CACzCtD,QACAC,SACAE,cACAD,OAAO,EACPE,UAAW,KAEb,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASW,EAAkB1L,GACzB,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAanD,GACvC8C,GAAS,IAAA6B,SACb,IAAMtG,EAAuB,CAC3B5E,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASY,EAAyB3L,GAChC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAanD,GACvC8C,GAAS,IAAA6B,SACb,IAAM3G,EAA8B,CAClCvE,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASa,EAAmB5L,GAC1B,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAanD,IACvC,QACJ+C,EAAO,UACPwB,EAAS,MACTC,EAAK,wBACLxB,EAAuB,UACvBnJ,GACEJ,EACEqJ,GAAS,IAAA6B,SACb,IAAMnG,EAA+B,CACnC/E,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWA,GAAa,KAE1B,CAACJ,EAAOC,EAAQE,EAAaC,IAE/B,OAAuB,IAAA0I,KACrB+B,EACA,CACExB,SACAE,0BACAD,UACAwB,YACAC,SAGN,CAKA,SAASc,EAAgC7L,GACvC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAanD,GACvC8C,GAAS,IAAA6B,SACb,IAAMrG,EAAqC,CACzC7E,QACAC,SACAE,cACAD,OAAO,EACPE,UAAW,KAEb,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,CAKA,SAASe,EAA4B9L,GACnC,MAAM,OAAEC,EAAM,YAAEE,IAAgB,IAAAuJ,YAAanD,GACvC8C,GAAS,IAAA6B,SACb,IAAMvG,EAAiC,CACrC3E,QACAC,SACAE,cACAD,OAAO,EACPE,UAAWJ,EAAMI,WAAa,KAEhC,CAACJ,EAAOC,EAAQE,IAElB,OAAuB,IAAA2I,KACrB+B,EACA,CACExB,SACAC,QAAStJ,EAAMsJ,QACfC,wBAAyBvJ,EAAMuJ,wBAC/BuB,UAAW9K,EAAM8K,UACjBC,MAAO/K,EAAM+K,OAGnB,C", "sources": ["webpack://everchess-app/./node_modules/@moonpay/moonpay-react/dist/index.js"], "sourcesContent": ["// src/components/MoonPayBalanceDepositWidget.tsx\nimport { useContext as useContext2, useMemo } from \"react\";\n\n// src/constants.ts\nvar MOONPAY_WIDGET_CONTAINER_ID = \"moonpay-react-sdk-embedded-frame\";\n\n// src/helpers.ts\nvar getBuyConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    currencyCode,\n    defaultCurrencyCode,\n    walletAddress,\n    walletAddressTag,\n    walletAddresses,\n    walletAddressTags,\n    colorCode,\n    theme,\n    themeId,\n    language,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyAmount,\n    lockAmount,\n    email,\n    externalTransactionId,\n    externalCustomerId,\n    paymentMethod,\n    redirectURL,\n    showAllCurrencies,\n    showOnlyCurrencies,\n    showWalletAddressForm,\n    unsupportedRegionRedirectUrl,\n    skipUnsupportedRegionScreen,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh,\n    // other\n    overlayNode\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    currencyCode,\n    defaultCurrencyCode,\n    walletAddress,\n    walletAddressTag,\n    walletAddresses,\n    walletAddressTags,\n    colorCode,\n    theme,\n    themeId,\n    language,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyAmount,\n    lockAmount,\n    email,\n    externalTransactionId,\n    externalCustomerId,\n    paymentMethod,\n    redirectURL,\n    showAllCurrencies,\n    showOnlyCurrencies,\n    showWalletAddressForm,\n    unsupportedRegionRedirectUrl,\n    skipUnsupportedRegionScreen\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"buy\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    baseOrigin,\n    handlers,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`,\n    overlayNode\n  };\n};\nvar getSellConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    quoteCurrencyCode,\n    defaultCurrencyCode,\n    defaultBaseCurrencyCode,\n    walletAddress,\n    walletAddressTag,\n    walletAddresses,\n    walletAddressTags,\n    colorCode,\n    theme,\n    themeId,\n    language,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyAmount,\n    lockAmount,\n    email,\n    externalTransactionId,\n    externalCustomerId,\n    paymentMethod,\n    redirectURL,\n    showAllCurrencies,\n    showOnlyCurrencies,\n    showWalletAddressForm,\n    unsupportedRegionRedirectUrl,\n    skipUnsupportedRegionScreen,\n    mpWalletId,\n    baseOrigin,\n    refundWalletAddress,\n    refundWalletAddresses,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    quoteCurrencyCode,\n    defaultCurrencyCode,\n    defaultBaseCurrencyCode,\n    walletAddress,\n    walletAddressTag,\n    walletAddresses,\n    walletAddressTags,\n    colorCode,\n    theme,\n    themeId,\n    language,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyAmount,\n    lockAmount,\n    email,\n    externalTransactionId,\n    externalCustomerId,\n    paymentMethod,\n    redirectURL,\n    showAllCurrencies,\n    showOnlyCurrencies,\n    showWalletAddressForm,\n    unsupportedRegionRedirectUrl,\n    skipUnsupportedRegionScreen,\n    mpWalletId,\n    refundWalletAddress,\n    refundWalletAddresses\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"sell\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    baseOrigin,\n    handlers,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getSwapsCustomerSetupConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment\n}) => {\n  const {\n    amount,\n    amountCurrencyCode,\n    theme,\n    themeId,\n    externalCustomerId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onLogin,\n    onInitiateDeposit,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    onKmsWalletsCreated,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    amount,\n    amountCurrencyCode,\n    theme,\n    themeId,\n    externalCustomerId\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onLogin,\n    onInitiateDeposit,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    onKmsWalletsCreated,\n    onTransactionCompleted,\n    onUnsupportedRegion\n  };\n  return {\n    flow: \"swapsCustomerSetup\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getConsumerKycConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment\n}) => {\n  const {\n    amount,\n    amountCurrencyCode,\n    theme,\n    themeId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onLogin,\n    onInitiateDeposit,\n    onConsumerKycComplete,\n    onKmsWalletsCreated,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    amount,\n    amountCurrencyCode,\n    theme,\n    themeId\n  };\n  const handlers = {\n    onClose,\n    onLogin,\n    onInitiateDeposit,\n    onConsumerKycComplete,\n    onKmsWalletsCreated,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay\n  };\n  return {\n    flow: \"consumerKyc\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getNftConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    contractAddress,\n    tokenId,\n    listingId,\n    externalTransactionId,\n    baseCurrencyCode,\n    redirectURL,\n    walletAddress,\n    subPartnerName,\n    metadata,\n    dynamicAssetInfo,\n    sellType,\n    email,\n    quantity,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    contractAddress,\n    tokenId,\n    listingId,\n    externalTransactionId,\n    baseCurrencyCode,\n    redirectURL,\n    walletAddress,\n    subPartnerName,\n    metadata,\n    dynamicAssetInfo,\n    sellType,\n    email,\n    quantity\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"nft\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    auth,\n    baseOrigin,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getClaimConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    contractId,\n    tokenId,\n    initialScreen,\n    // handlers\n    onClose,\n    onAuthToken,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onCloseOverlay,\n    onTransactionCreated,\n    baseOrigin,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    contractId,\n    tokenId,\n    initialScreen\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"claim\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getTopUpLedgerConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    externalCustomerId,\n    theme,\n    themeId,\n    currencyCode,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    externalCustomerId,\n    theme,\n    themeId,\n    currencyCode\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"topup\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getMoonPayBalanceDepositConfigFromProps = ({\n  props,\n  debug,\n  environment\n}) => {\n  const {\n    theme,\n    themeId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay,\n    onTransactionCreated,\n    onBalanceDepositTransferConfirmed,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    theme,\n    themeId\n  };\n  const handlers = {\n    onClose,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay,\n    onTransactionCreated,\n    onBalanceDepositTransferConfirmed\n  };\n  return {\n    flow: \"moonPayBalanceDeposit\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getMoonPayBalanceWithdrawConfigFromProps = ({\n  props,\n  debug,\n  environment\n}) => {\n  const {\n    theme,\n    themeId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    theme,\n    themeId\n  };\n  const handlers = {\n    onClose,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"moonPayBalanceWithdraw\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    baseOrigin,\n    handlers,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getWithdrawLedgerConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    externalCustomerId,\n    theme,\n    themeId,\n    baseCurrencyCode,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    externalCustomerId,\n    theme,\n    themeId,\n    baseCurrencyCode\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"withdraw\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getSwapConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    // params\n    walletAddress,\n    walletAddressTag,\n    refundWalletAddress,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyCode,\n    quoteCurrencyAmount,\n    colorCode,\n    theme,\n    themeId,\n    externalCustomerId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    apiKey,\n    signature,\n    walletAddress,\n    walletAddressTag,\n    refundWalletAddress,\n    baseCurrencyCode,\n    baseCurrencyAmount,\n    quoteCurrencyCode,\n    quoteCurrencyAmount,\n    colorCode,\n    theme,\n    themeId,\n    externalCustomerId\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"swap\",\n    variant: props.variant,\n    environment,\n    params,\n    debug,\n    handlers,\n    baseOrigin,\n    auth,\n    useWarnBeforeRefresh,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`\n  };\n};\nvar getTransactionTrackerConfigFromProps = ({\n  props,\n  debug,\n  environment\n}) => {\n  const {\n    // params\n    transactionId,\n    colorCode,\n    theme,\n    themeId,\n    baseOrigin,\n    // handlers\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated,\n    // auth\n    auth,\n    useWarnBeforeRefresh\n  } = props;\n  const params = {\n    transactionId,\n    colorCode,\n    theme,\n    themeId\n  };\n  const handlers = {\n    onClose,\n    onAuthToken,\n    onInitiateDeposit,\n    onKmsWalletsCreated,\n    onLogin,\n    onTransactionCompleted,\n    onUnsupportedRegion,\n    onSwapsCustomerSetupComplete,\n    onCloseOverlay,\n    onTransactionCreated\n  };\n  return {\n    flow: \"transactionTracker\",\n    variant: props.variant,\n    environment,\n    params,\n    baseOrigin,\n    debug,\n    handlers,\n    auth,\n    containerNodeSelector: `#${MOONPAY_WIDGET_CONTAINER_ID}`,\n    useWarnBeforeRefresh\n  };\n};\nvar getPartnerTopupConfigFromProps = ({\n  props,\n  apiKey,\n  debug,\n  environment,\n  signature\n}) => {\n  const {\n    enabledMethods,\n    cryptoAmount,\n    fromDefaultCurrencyCode,\n    fromChainName,\n    toChainName,\n    testLocation\n  } = props;\n  const buyConfigProps = getBuyConfigFromProps({\n    props,\n    apiKey,\n    debug,\n    environment,\n    signature\n  });\n  const params = {\n    ...buyConfigProps.params,\n    enabledMethods,\n    cryptoAmount,\n    fromDefaultCurrencyCode,\n    fromChainName,\n    toChainName,\n    testLocation\n  };\n  return {\n    ...buyConfigProps,\n    flow: \"partnerTopup\",\n    params\n  };\n};\n\n// ../js-sdk/src/loader.ts\nvar scriptLoadingStatus = {\n  isLoading: false,\n  isLoaded: false\n};\nasync function loadMoonPay(version = \"v1\") {\n  return new Promise((resolve, reject) => {\n    const scriptSrc = `https://static.moonpay.com/web-sdk/${version}/moonpay-web-sdk.min.js`;\n    const existingScript = document.querySelector(`script[src=\"${scriptSrc}\"]`);\n    scriptLoadingStatus.isLoading = true;\n    const checkLoaded = (count = 0) => {\n      if (window.MoonPayWebSdk) {\n        scriptLoadingStatus.isLoading = false;\n        scriptLoadingStatus.isLoaded = true;\n        resolve(window.MoonPayWebSdk.init);\n        return;\n      }\n      if (count > 100) {\n        scriptLoadingStatus.isLoading = false;\n        reject(new Error(\"Loading MoonPayWebSdk script timed out.\"));\n        return;\n      }\n      setTimeout(() => checkLoaded(count + 1), 100);\n    };\n    if (existingScript) {\n      checkLoaded();\n    } else {\n      const script = document.createElement(\"script\");\n      script.async = true;\n      script.src = scriptSrc;\n      script.addEventListener(\"load\", () => {\n        scriptLoadingStatus.isLoading = false;\n        scriptLoadingStatus.isLoaded = true;\n        resolve(window.MoonPayWebSdk?.init);\n      });\n      script.addEventListener(\"error\", () => {\n        scriptLoadingStatus.isLoading = false;\n        scriptLoadingStatus.isLoaded = false;\n        reject(new Error(\"Failed to load MoonPayWebSdk script.\"));\n      });\n      document.body.appendChild(script);\n    }\n  });\n}\n\n// src/components/MoonPayProvider.tsx\nimport {\n  createContext,\n  useCallback as useCallback2,\n  useEffect as useEffect3,\n  useState as useState3\n} from \"react\";\n\n// ../common/src/environment.ts\nfunction getEnvironmentFromApiKey(apiKey) {\n  return apiKey.startsWith(\"pk_live\") || apiKey.startsWith(\"sk_live\") ? \"production\" : \"sandbox\";\n}\n\n// ../common/src/Logger.ts\nvar NOOP = () => void 0;\nvar noopLogger = {\n  info: NOOP,\n  warn: NOOP,\n  error: NOOP\n};\nvar Logger = class _Logger {\n  prefix;\n  logger;\n  constructor({\n    logger,\n    prefix\n  }) {\n    this.logger = logger;\n    this.prefix = prefix ?? [\"Logger\"];\n  }\n  if(condition) {\n    return condition ? this : new _Logger({ logger: noopLogger });\n  }\n  info(...data) {\n    this.logger.info(`[${this.prefix}]`, ...data);\n  }\n  warn(...data) {\n    this.logger.warn(`[${this.prefix}]`, ...data);\n  }\n  error(...data) {\n    this.logger.error(`[${this.prefix}]`, ...data);\n  }\n};\n\n// ../common/src/react/useHasOverflow.tsx\nimport { useLayoutEffect, useState } from \"react\";\n\n// ../common/src/react/useIsMounted.tsx\nimport { useRef, useEffect, useCallback } from \"react\";\n\n// ../common/src/react/useRenderCount.ts\nimport { useRef as useRef2 } from \"react\";\n\n// ../common/src/react/useScript.ts\nimport { useEffect as useEffect2, useState as useState2 } from \"react\";\n\n// ../common/src/uuid.ts\nvar uuidV4 = typeof window.crypto !== \"undefined\" && typeof window.crypto.getRandomValues !== \"undefined\" ? () => {\n  return (\"10000000-1000-4000-8000\" + -1e11).replace(\n    /[018]/g,\n    (c) => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)\n  );\n} : () => {\n  return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c) => {\n    const r = Math.random() * 16 | 0;\n    const v = c === \"x\" ? r : r & 3 | 8;\n    return v.toString(16);\n  });\n};\n\n// src/components/MoonPayProvider.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar MoonPayContext = createContext({\n  apiKey: \"\",\n  environment: \"sandbox\",\n  debug: false\n});\nvar MoonPayProvider = ({\n  children,\n  apiKey,\n  debug = false\n}) => {\n  const environment = getEnvironmentFromApiKey(apiKey);\n  const [contextState, setContextState] = useState3({\n    apiKey,\n    environment,\n    debug\n  });\n  const load = useCallback2(async () => {\n    const moonPay = await loadMoonPay();\n    setContextState((state) => ({ ...state, init: moonPay }));\n  }, []);\n  useEffect3(() => {\n    if (!contextState.init) load();\n  }, [load, contextState.init]);\n  return /* @__PURE__ */ jsx(MoonPayContext.Provider, { value: contextState, children });\n};\n\n// src/useSdk.tsx\nimport { useCallback as useCallback3, useContext, useEffect as useEffect4, useRef as useRef3 } from \"react\";\n\n// src/logger.ts\nvar Logger2 = class extends Logger {\n  constructor() {\n    super({ logger: console, prefix: [\"MoonPay React SDK\"] });\n  }\n};\n\n// src/useSdk.tsx\nvar useSdk = (config, visible, onUrlSignatureRequested) => {\n  const sdk = useRef3(null);\n  const { init, debug } = useContext(MoonPayContext);\n  const logger = useRef3(new Logger2().if(debug || false));\n  const handleUpdateSignatureAndShow = useCallback3(async () => {\n    if (!sdk.current) {\n      return;\n    }\n    if (!onUrlSignatureRequested && (\"walletAddress\" in config || \"walletAddresses\" in config)) {\n      logger.current.warn(\n        \"You must provide an `onUrlSignatureRequested` function to sign the URL when using the `walletAddress` or `walletAddresses` prop\"\n      );\n    }\n    if (onUrlSignatureRequested) {\n      const url = sdk.current.generateUrlForSigning();\n      try {\n        const updatedSignature = await onUrlSignatureRequested?.(url);\n        if (!updatedSignature) {\n          return;\n        }\n        sdk.current?.updateSignature(updatedSignature);\n      } catch (e) {\n        logger.current.error(\n          \"Something went wrong calling the provided `onUrlSignatureRequested` function:\",\n          e\n        );\n      }\n    }\n    if (visible) {\n      sdk.current?.show();\n    }\n  }, [onUrlSignatureRequested, visible, config]);\n  useEffect4(() => {\n    if (sdk.current) {\n      sdk.current.close();\n    }\n    const newSdk = init?.(config) || null;\n    sdk.current = newSdk;\n    handleUpdateSignatureAndShow();\n  }, [init, config, handleUpdateSignatureAndShow]);\n};\n\n// src/components/MoonPayWidget.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar defaultStyles = {\n  width: \"424px\",\n  height: \"656px\",\n  margin: \"32px 28px 32px 28px\",\n  border: \"1px solid #E5E7EB\",\n  borderRadius: \"16px\",\n  overflow: \"hidden\"\n};\nfunction MoonPayWidget({\n  config,\n  onUrlSignatureRequested,\n  visible = true,\n  className,\n  style\n}) {\n  useSdk(config, visible, onUrlSignatureRequested);\n  if (visible) {\n    return /* @__PURE__ */ jsx2(\n      \"div\",\n      {\n        id: MOONPAY_WIDGET_CONTAINER_ID,\n        className,\n        style: config.variant === \"embedded\" ? {\n          ...defaultStyles,\n          ...style\n        } : void 0\n      }\n    );\n  }\n  return null;\n}\n\n// src/components/MoonPayBalanceDepositWidget.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nfunction MoonPayBalanceDepositWidget(props) {\n  const { environment } = useContext2(MoonPayContext);\n  const config = useMemo(\n    () => getMoonPayBalanceDepositConfigFromProps({\n      props,\n      environment,\n      debug: false\n    }),\n    [props, environment]\n  );\n  return /* @__PURE__ */ jsx3(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayBalanceWithdrawWidget.tsx\nimport { useContext as useContext3, useMemo as useMemo2 } from \"react\";\nimport { jsx as jsx4 } from \"react/jsx-runtime\";\nfunction MoonPayBalanceWithdrawWidget(props) {\n  const { environment } = useContext3(MoonPayContext);\n  const config = useMemo2(\n    () => getMoonPayBalanceWithdrawConfigFromProps({\n      props,\n      environment,\n      debug: false\n    }),\n    [props, environment]\n  );\n  return /* @__PURE__ */ jsx4(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayBuyWidget.tsx\nimport { useContext as useContext4, useMemo as useMemo3 } from \"react\";\nimport { jsx as jsx5 } from \"react/jsx-runtime\";\nfunction MoonPayBuyWidget(props) {\n  const { apiKey, environment } = useContext4(MoonPayContext);\n  const config = useMemo3(\n    () => getBuyConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx5(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayConsumerKycWidget.tsx\nimport { useContext as useContext5, useMemo as useMemo4 } from \"react\";\nimport { jsx as jsx6 } from \"react/jsx-runtime\";\nfunction MoonPayConsumerKycWidget(props) {\n  const { apiKey, environment } = useContext5(MoonPayContext);\n  const config = useMemo4(\n    () => getConsumerKycConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx6(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayNftCheckoutWidget.tsx\nimport { useContext as useContext6, useMemo as useMemo5 } from \"react\";\nimport { jsx as jsx7 } from \"react/jsx-runtime\";\nfunction MoonPayNftCheckoutWidget(props) {\n  const { apiKey, environment } = useContext6(MoonPayContext);\n  const config = useMemo5(\n    () => getNftConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx7(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayNftClaimWidget.tsx\nimport { useContext as useContext7, useMemo as useMemo6 } from \"react\";\nimport { jsx as jsx8 } from \"react/jsx-runtime\";\nfunction MoonPayNftClaimWidget(props) {\n  const { apiKey, environment } = useContext7(MoonPayContext);\n  const config = useMemo6(\n    () => getClaimConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx8(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPaySellWidget.tsx\nimport { useContext as useContext8, useMemo as useMemo7 } from \"react\";\nimport { jsx as jsx9 } from \"react/jsx-runtime\";\nfunction MoonPaySellWidget(props) {\n  const { apiKey, environment } = useContext8(MoonPayContext);\n  const config = useMemo7(\n    () => getSellConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx9(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPaySwapsCustomerSetupWidget.tsx\nimport { useContext as useContext9, useMemo as useMemo8 } from \"react\";\nimport { jsx as jsx10 } from \"react/jsx-runtime\";\nfunction MoonPaySwapsCustomerSetupWidget(props) {\n  const { apiKey, environment } = useContext9(MoonPayContext);\n  const config = useMemo8(\n    () => getSwapsCustomerSetupConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx10(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPaySwapWidget.tsx\nimport { useContext as useContext10, useMemo as useMemo9 } from \"react\";\nimport { jsx as jsx11 } from \"react/jsx-runtime\";\nfunction MoonPaySwapWidget(props) {\n  const { apiKey, environment } = useContext10(MoonPayContext);\n  const config = useMemo9(\n    () => getSwapConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx11(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayTopUpLedgerWidget.tsx\nimport { useContext as useContext11, useMemo as useMemo10 } from \"react\";\nimport { jsx as jsx12 } from \"react/jsx-runtime\";\nfunction MoonPayTopUpLedgerWidget(props) {\n  const { apiKey, environment } = useContext11(MoonPayContext);\n  const config = useMemo10(\n    () => getTopUpLedgerConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx12(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayTopupWidget.tsx\nimport { useContext as useContext12, useMemo as useMemo11 } from \"react\";\nimport { jsx as jsx13 } from \"react/jsx-runtime\";\nfunction MoonPayTopUpWidget(props) {\n  const { apiKey, environment } = useContext12(MoonPayContext);\n  const {\n    visible,\n    className,\n    style,\n    onUrlSignatureRequested,\n    signature\n  } = props;\n  const config = useMemo11(\n    () => getPartnerTopupConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: signature || \"\"\n    }),\n    [props, apiKey, environment, signature]\n  );\n  return /* @__PURE__ */ jsx13(\n    MoonPayWidget,\n    {\n      config,\n      onUrlSignatureRequested,\n      visible,\n      className,\n      style\n    }\n  );\n}\n\n// src/components/MoonPayTransactionTrackerWidget.tsx\nimport { useContext as useContext13, useMemo as useMemo12 } from \"react\";\nimport { jsx as jsx14 } from \"react/jsx-runtime\";\nfunction MoonPayTransactionTrackerWidget(props) {\n  const { apiKey, environment } = useContext13(MoonPayContext);\n  const config = useMemo12(\n    () => getTransactionTrackerConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx14(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\n\n// src/components/MoonPayWithdrawLedgerWidget.tsx\nimport { useContext as useContext14, useMemo as useMemo13 } from \"react\";\nimport { jsx as jsx15 } from \"react/jsx-runtime\";\nfunction MoonPayWithdrawLedgerWidget(props) {\n  const { apiKey, environment } = useContext14(MoonPayContext);\n  const config = useMemo13(\n    () => getWithdrawLedgerConfigFromProps({\n      props,\n      apiKey,\n      environment,\n      debug: false,\n      signature: props.signature || \"\"\n    }),\n    [props, apiKey, environment]\n  );\n  return /* @__PURE__ */ jsx15(\n    MoonPayWidget,\n    {\n      config,\n      visible: props.visible,\n      onUrlSignatureRequested: props.onUrlSignatureRequested,\n      className: props.className,\n      style: props.style\n    }\n  );\n}\nexport {\n  MoonPayBalanceDepositWidget,\n  MoonPayBalanceWithdrawWidget,\n  MoonPayBuyWidget,\n  MoonPayConsumerKycWidget,\n  MoonPayContext,\n  MoonPayNftCheckoutWidget,\n  MoonPayNftClaimWidget,\n  MoonPayProvider,\n  MoonPaySellWidget,\n  MoonPaySwapWidget,\n  MoonPaySwapsCustomerSetupWidget,\n  MoonPayTopUpLedgerWidget,\n  MoonPayTopUpWidget,\n  MoonPayTransactionTrackerWidget,\n  MoonPayWithdrawLedgerWidget\n};\n//# sourceMappingURL=index.js.map"], "names": ["MOONPAY_WIDGET_CONTAINER_ID", "getBuyConfigFromProps", "props", "<PERSON><PERSON><PERSON><PERSON>", "debug", "environment", "signature", "currencyCode", "defaultCurrencyCode", "wallet<PERSON>ddress", "walletAddressTag", "walletAddresses", "walletAddressTags", "colorCode", "theme", "themeId", "language", "baseCurrencyCode", "baseCurrencyAmount", "quoteCurrencyAmount", "lockAmount", "email", "externalTransactionId", "externalCustomerId", "paymentMethod", "redirectURL", "showAllCurrencies", "showOnlyCurrencies", "showWalletAddressForm", "unsupportedRegionRedirectUrl", "skipUnsupportedRegionScreen", "baseOrigin", "onClose", "onAuthToken", "onInitiateDeposit", "onKmsWalletsCreated", "onLogin", "onTransactionCompleted", "onUnsupportedRegion", "onSwapsCustomerSetupComplete", "onCloseOverlay", "onTransactionCreated", "auth", "useWarnBeforeRefresh", "overlayNode", "params", "handlers", "flow", "variant", "containerNodeSelector", "getSellConfigFromProps", "quoteCurrencyCode", "defaultBaseCurrencyCode", "mpWalletId", "refundWalletAddress", "refundWalletAddresses", "getSwapsCustomerSetupConfigFromProps", "amount", "amountCurrencyCode", "getConsumerKycConfigFromProps", "onConsumerKycComplete", "getNftConfigFromProps", "contractAddress", "tokenId", "listingId", "subPartnerName", "metadata", "dynamicAssetInfo", "sellType", "quantity", "getClaimConfigFromProps", "contractId", "initialScreen", "getTopUpLedgerConfigFromProps", "getMoonPayBalanceDepositConfigFromProps", "onBalanceDepositTransferConfirmed", "getMoonPayBalanceWithdrawConfigFromProps", "getWithdrawLedgerConfigFromProps", "getSwapConfigFromProps", "getTransactionTrackerConfigFromProps", "transactionId", "getPartnerTopupConfigFromProps", "enabledMethods", "cryptoAmount", "fromDefaultCurrencyCode", "fromChainName", "toChainName", "testLocation", "buyConfigProps", "scriptLoadingStatus", "isLoading", "isLoaded", "NOOP", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "warn", "error", "<PERSON><PERSON>", "_<PERSON>gger", "prefix", "logger", "constructor", "this", "condition", "data", "MoonPayContext", "window", "crypto", "getRandomValues", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "startsWith", "getEnvironmentFromApiKey", "contextState", "setContextState", "useState", "load", "useCallback", "async", "moonPay", "version", "Promise", "resolve", "reject", "scriptSrc", "existingScript", "document", "querySelector", "checkLoaded", "count", "MoonPayWebSdk", "init", "Error", "setTimeout", "script", "createElement", "src", "addEventListener", "body", "append<PERSON><PERSON><PERSON>", "loadMoonPay", "state", "useEffect", "jsx", "Provider", "value", "Logger2", "super", "console", "useSdk", "config", "visible", "onUrlSignatureRequested", "sdk", "useRef", "useContext", "if", "handleUpdateSignatureAndShow", "current", "url", "generateUrlForSigning", "updatedSignature", "updateSignature", "e", "show", "close", "newSdk", "defaultStyles", "width", "height", "margin", "border", "borderRadius", "overflow", "MoonPayWidget", "className", "style", "id", "MoonPayBalanceDepositWidget", "useMemo", "MoonPayBalanceWithdrawWidget", "MoonPayBuyWidget", "MoonPayConsumerKycWidget", "MoonPayNftCheckoutWidget", "MoonPayNftClaimWidget", "MoonPaySellWidget", "MoonPaySwapsCustomerSetupWidget", "MoonPaySwapWidget", "MoonPayTopUpLedgerWidget", "MoonPayTopUpWidget", "MoonPayTransactionTrackerWidget", "MoonPayWithdrawLedgerWidget"], "sourceRoot": ""}