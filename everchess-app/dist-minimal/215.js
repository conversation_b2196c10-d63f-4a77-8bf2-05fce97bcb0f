"use strict";(self.webpackChunkeverchess_app=self.webpackChunkeverchess_app||[]).push([[215],{215:(e,t,n)=>{n.r(t),n.d(t,{cpsl_identicon:()=>s});var r=n(296),o=n(3029),a=n(2901),i=n(9289),l=function(){function e(t){(0,o.A)(this,e),this._value=NaN,this._seed="string"==typeof t?this.hashCode(t):"number"==typeof t?this.getSafeSeed(t):this.getSafeSeed(e.MIN+Math.floor((e.MAX-e.MIN)*Math.random())),this.reset()}return(0,a.A)(e,[{key:"next",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return this.recalculate(),this.map(this._value,e.MIN,e.MAX,t,n)}},{key:"nextInt",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return this.recalculate(),Math.floor(this.map(this._value,e.MIN,e.MAX,t,n+1))}},{key:"nextString",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="";n.length<e;)n+=this.nextChar(t);return n}},{key:"nextChar",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return e.substr(this.nextInt(0,e.length-1),1)}},{key:"nextArrayItem",value:function(e){return e[this.nextInt(0,e.length-1)]}},{key:"nextBoolean",value:function(){return this.recalculate(),this._value>.5}},{key:"skip",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;e-- >0;)this.recalculate()}},{key:"reset",value:function(){this._value=this._seed}},{key:"recalculate",value:function(){this._value=this.xorshift(this._value)}},{key:"xorshift",value:function(e){return e^=e<<13,(e^=e>>17)^e<<5}},{key:"map",value:function(e,t,n,r,o){return(e-t)/(n-t)*(o-r)+r}},{key:"hashCode",value:function(e){var t=0;if(e)for(var n=e.length,r=0;r<n;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0,t=this.xorshift(t);return this.getSafeSeed(t)}},{key:"getSafeSeed",value:function(e){return 0===e?1:e}}])}();l.MIN=-2147483648,l.MAX=2147483647;var h=["red","orange","yellow","green","blue","purple"],u=[[[0,0,0,0],[0,1,3,2]],[[1,1,0,0],[0,1,3,2]],[[0,1,0,1],[0,1,3,2]],[[0,0,1,1],[0,1,3,2]],[[1,0,1,0],[0,1,3,2]],[[1,1,1,1],[0,1,3,2]],[[0,0,0,0],[2,3,1,0]],[[1,1,1,1],[2,3,1,0]],[[1,1,1,1],[0,1,2,3]]],s=function(){return(0,a.A)(function e(t){(0,o.A)(this,e),(0,i.r)(this,t),this.hash=void 0,this.size="40px",this.variant="default"},[{key:"render",value:function(){var e,t,n,o,a,s,c,g,v,f,p;return!this.hash||(t=this.hash,n=new l(t),o=d.map(function(e){return n.nextInt(0,e-1)}),s=(a=(0,r.A)(o,3))[0],c=a[1],g=a[2],v=Math.floor(g/4),f=g%2==1,p=g%4>=2,e={color:h[s],shapes:u[c][0].map(function(e,t){return t===v&&f?1!==e:1===e}),rotations:u[c][1].map(function(e,t){return t===v&&p?(e+2)%4:e})}),(0,i.h)(i.H,{key:"55072485ad5993ad8eb075a9d7d83e7e137ec9cd",class:{red:"red"===(null==e?void 0:e.color),orange:"orange"===(null==e?void 0:e.color),yellow:"yellow"===(null==e?void 0:e.color),green:"green"===(null==e?void 0:e.color),blue:"blue"===(null==e?void 0:e.color),purple:"purple"===(null==e?void 0:e.color),empty:!(null==e?void 0:e.color)&&!this.hash,avatar:"avatar"===this.variant},style:{width:this.size,height:this.size}},(null==e?void 0:e.shapes)&&(null==e?void 0:e.rotations)&&e.shapes.map(function(t,n){return t?(r=e.rotations[n],(0,i.h)("svg",{class:{rotate90:1===r,rotate180:2===r,rotate270:3===r},viewBox:"0 0 12 12",xmlns:"http://www.w3.org/2000/svg"},(0,i.h)("g",{"clip-path":"url(#clip0_674_255)"},(0,i.h)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"}),(0,i.h)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18ZM12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"})),(0,i.h)("defs",null,(0,i.h)("clipPath",{id:"clip0_674_255"},(0,i.h)("rect",{width:"12",height:"12"}))))):function(e){return(0,i.h)("svg",{class:{rotate90:1===e,rotate180:2===e,rotate270:3===e},viewBox:"0 0 12 12",xmlns:"http://www.w3.org/2000/svg"},(0,i.h)("g",{"clip-path":"url(#clip0_674_66)"},(0,i.h)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24ZM12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"})),(0,i.h)("defs",null,(0,i.h)("clipPath",{id:"clip0_674_66"},(0,i.h)("rect",{width:"12",height:"12"}))))}(e.rotations[n]);var r}))}}])}(),d=[h.length,u.length,16];s.style=":host{--identicon-background-red:linear-gradient(136deg, #ff4270 6.86%, #ff7c7c 93.78%);--identicon-background-orange:linear-gradient(136deg, #f45532 6.86%, #ff9b63 93.78%);--identicon-background-yellow:linear-gradient(136deg, #ffa756 6.86%, #fbff47 93.78%);--identicon-background-green:linear-gradient(136deg, #0cae60 6.86%, #7bffd0 93.78%);--identicon-background-blue:linear-gradient(136deg, #476fff 6.86%, #47c8ff 93.78%);--identicon-background-purple:linear-gradient(136deg, #9747ff 6.86%, #da47ff 93.78%);--identicon-background-empty:linear-gradient(136deg, #aaaaaa 6.86%, #999999 93.78%);display:block;aspect-ratio:1;border-radius:25%;position:relative;border:1px solid var(--cpsl-color-background-8)}:host>svg{fill:rgba(255, 255, 255, 0.6);position:absolute;width:30%}:host>svg.rotate90{transform:rotate(0.25turn)}:host>svg.rotate180{transform:rotate(0.5turn)}:host>svg.rotate270{transform:rotate(0.75turn)}:host>svg:nth-child(1){right:50%;bottom:50%}:host>svg:nth-child(2){left:50%;bottom:50%}:host>svg:nth-child(3){right:50%;top:50%}:host>svg:nth-child(4){left:50%;top:50%}:host(.red){background:var(--identicon-background-red)}:host(.orange){background:var(--identicon-background-orange)}:host(.green){background:var(--identicon-background-green)}:host(.yellow){background:var(--identicon-background-yellow)}:host(.blue){background:var(--identicon-background-blue)}:host(.purple){background:var(--identicon-background-purple)}:host(.empty){background:var(--identicon-background-empty)}:host(.avatar){border-radius:1000px}"}}]);
//# sourceMappingURL=215.js.map