{"version": 3, "file": "167.js", "mappings": "qLAOaA,EAAa,W,iNAuChB,KAAAC,qBAAuB,WAC7BC,EAAKC,mBAAmBC,KAAKF,EAAKG,M,EAQ5B,KAAAC,oBAAsB,SAACD,GAAa,OAAK,WAC/CH,EAAKK,2BAA2BH,KAAKC,E,CACtC,E,eA3C6C,E,8IA6C9C,WAAM,I,IAAAG,EAAA,KACEC,EAAiC,QAAdC,EAAAC,KAAKC,iBAAS,IAAAF,OAAA,EAAAA,EAAEG,KAAK,SAAAC,GAAE,OAAKN,EAAKO,mBAAqBP,EAAKQ,OAAS,GAALC,OAAQT,EAAKH,MAAK,KAAAY,OAAIH,EAAGI,OAAUV,EAAKQ,KAAKG,SAAS,GAADF,OAAIT,EAAKH,MAAK,KAAAY,OAAIH,EAAGI,OAAQ,GACpKE,EAAsBT,KAAKU,oBAAsBV,KAAKK,OAASL,KAAKN,MAAQM,KAAKK,KAAKG,SAASR,KAAKN,OAE1G,OACEiB,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAC,IAAA,6CACHF,EAAAA,EAAAA,GAAA,eAAAE,IAAA,2CACEC,GAAId,KAAKN,MACTqB,MAAO,CAAE,cAAc,EAAM,WAAcjB,GAAoBW,GAC/DO,WAAS,EACTC,QAAQ,UACRC,SAAUlB,KAAKkB,SACfC,QAASnB,KAAKV,uBAEdqB,EAAAA,EAAAA,GAAA,QAAAE,IAAA,2CAAMO,KAAK,WACXT,EAAAA,EAAAA,GAAA,QAAAE,IAAA,8CACAF,EAAAA,EAAAA,GAAA,QAAAE,IAAA,2CAAMO,KAAK,YAETtB,GAAoBW,OAAwC,QAAdY,EAAArB,KAAKC,iBAAS,IAAAoB,OAAA,EAAAA,EAAEC,UAChEX,EAAAA,EAAAA,GAAA,OAAAE,IAAA,2CAAKE,MAAM,uBACRf,KAAKC,UAAUsB,IAAI,SAAApB,GAAE,OACpBQ,EAAAA,EAAAA,GAAA,eACEE,IAAKV,EAAGI,MACRO,GAAE,GAAAR,OAAKT,EAAKH,MAAK,KAAAY,OAAIH,EAAGI,OACxBQ,MAAO,CAAE,aAAa,EAAM,SAAYZ,EAAGI,SAAUT,aAAgB,EAAhBA,EAAkBS,QACvEY,QAAStB,EAAKF,oBAAoB,GAADW,OAAIT,EAAKH,MAAK,KAAAY,OAAIH,EAAGI,QACtDS,WAAS,EACTC,QAAQ,WAEPd,EAAGqB,MACQ,I,GAMzB,CAAAX,IAAA,KAAAY,I,mCAxFuB,G,QCPD,w2J", "sources": ["webpack://everchess-app/src/components/cpsl-nav-button/cpsl-nav-button.tsx", "webpack://everchess-app/src/components/cpsl-nav-button/cpsl-nav-button.scss"], "sourcesContent": ["import { Component, EventEmitter, Host, Prop, h, Event, Element } from '@stencil/core';\n\n@Component({\n  tag: 'cpsl-nav-button',\n  styleUrl: 'cpsl-nav-button.scss',\n  shadow: true,\n})\nexport class CpslNavButton {\n  @Element() el!: HTMLCpslNavButtonElement;\n\n  /**\n   * If the button is disabled.\n   * Default is: false.\n   */\n  @Prop({ reflect: true }) disabled?: boolean = false;\n\n  /**\n   * Whether or not to use exact matching for the selected main route.\n   */\n  @Prop() exactMainRouteMatch?: boolean;\n\n  /**\n   * Whether or not to use exact matching for the selected sub route.\n   */\n  @Prop() exactSubRouteMatch?: boolean;\n\n  /**\n   * The route for the button.\n   */\n  @Prop() route: string;\n\n  /**\n   * The id of the selected button.\n   */\n  @Prop() subRoutes?: { label: string; value: string }[];\n\n  /**\n   * Path used to determine what button is selected\n   */\n  @Prop() path?: string;\n\n  /**\n   * Called when the nav button is clicked.\n   */\n  @Event() cpslNavButtonClick: EventEmitter<string>;\n\n  private handleNavButtonClick = () => {\n    this.cpslNavButtonClick.emit(this.route);\n  };\n\n  /**\n   * Called when a nav button sub route is clicked.\n   */\n  @Event() cpslNavButtonSubRouteClick: EventEmitter<string>;\n\n  private handleSubRouteClick = (route: string) => () => {\n    this.cpslNavButtonSubRouteClick.emit(route);\n  };\n\n  render() {\n    const selectedSubRoute = this.subRoutes?.find(sr => (this.exactSubRouteMatch ? this.path === `${this.route}/${sr.value}` : this.path.includes(`${this.route}/${sr.value}`)));\n    const isMainRouteSelected = this.exactMainRouteMatch ? this.path === this.route : this.path.includes(this.route);\n\n    return (\n      <Host>\n        <cpsl-button\n          id={this.route}\n          class={{ 'main-route': true, 'selected': !!selectedSubRoute || isMainRouteSelected }}\n          fullWidth\n          variant=\"primary\"\n          disabled={this.disabled}\n          onClick={this.handleNavButtonClick}\n        >\n          <slot name=\"start\"></slot>\n          <slot></slot>\n          <slot name=\"end\"></slot>\n        </cpsl-button>\n        {(!!selectedSubRoute || isMainRouteSelected) && !!this.subRoutes?.length && (\n          <div class=\"sub-route-container\">\n            {this.subRoutes.map(sr => (\n              <cpsl-button\n                key={sr.value}\n                id={`${this.route}/${sr.value}`}\n                class={{ 'sub-route': true, 'selected': sr.value === selectedSubRoute?.value }}\n                onClick={this.handleSubRouteClick(`${this.route}/${sr.value}`)}\n                fullWidth\n                variant=\"primary\"\n              >\n                {sr.label}\n              </cpsl-button>\n            ))}\n          </div>\n        )}\n      </Host>\n    );\n  }\n}\n", ":host {\n  display: flex;\n  flex-direction: column;\n\n  gap: 8px;\n  padding: 0px;\n  box-sizing: border-box;\n  width: 100%;\n\n  background: var(--cpsl-color-background-4);\n  border-radius: var(--cpsl-border-radius-primary-button);\n  overflow: hidden;\n}\n\n.sub-route-container {\n  display: flex;\n  flex-direction: column;\n\n  gap: 8px;\n  border-left: 1px solid var(--cpsl-color-background-16);\n  padding-left: 16px;\n  margin-left: 16px;\n  margin-bottom: 8px;\n  margin-right: 8px;\n  background: var(--cpsl-color-background-4);\n}\n\n.sub-route {\n  --button-padding-start: 8px;\n  --button-padding-end: 8px;\n  --button-padding-top: 8px;\n  --button-padding-bottom: 8px;\n  --button-justify-content: start;\n\n  // DEFAULT\n  --button-primary-color: var(--cpsl-color-background-48);\n  --button-primary-background-color: var(--cpsl-color-background-4);\n  --button-primary-border-color: var(--cpsl-color-background-4);\n  --button-primary-icon-color: var(--cpsl-color-text-primary);\n  // DISABLED\n  --button-primary-disabled-color: var(--cpsl-color-text-primary);\n  --button-primary-disabled-background-color: var(--cpsl-color-background-4);\n  --button-primary-disabled-border-color: var(--cpsl-color-background-4);\n  --button-primary-disabled-icon-color: var(--cpsl-color-text-primary);\n  // HOVER\n  --button-primary-hover-color: var(--cpsl-color-background-48);\n  --button-primary-hover-background-color: var(--cpsl-color-background-0);\n  --button-primary-hover-border-color: var(--cpsl-color-background-4);\n  --button-primary-hover-icon-color: var(--cpsl-color-background-48);\n  // ACTIVE\n  --button-primary-active-color: var(--cpsl-color-text-primary);\n  --button-primary-active-background-color: var(--cpsl-color-background-4);\n  --button-primary-active-border-color: var(--cpsl-color-background-4);\n  --button-primary-active-icon-color: var(--cpsl-color-text-primary);\n\n  &.selected {\n    // DEFAULT\n    --button-primary-color: var(--cpsl-color-text-primary);\n    --button-primary-background-color: var(--cpsl-color-background-0);\n    --button-primary-border-color: var(--cpsl-color-background-0);\n    --button-primary-icon-color: var(--cpsl-color-background-48);\n    // DISABLED\n    --button-primary-disabled-color: var(--cpsl-color-background-48);\n    --button-primary-disabled-background-color: var(--cpsl-color-background-0);\n    --button-primary-disabled-border-color: var(--cpsl-color-background-0);\n    --button-primary-disabled-icon-color: var(--cpsl-color-background-48);\n    // HOVER\n    --button-primary-hover-color: var(--cpsl-color-text-primary);\n    --button-primary-hover-background-color: var(--cpsl-color-background-0);\n    --button-primary-hover-border-color: var(--cpsl-color-background-4);\n    --button-primary-hover-icon-color: var(--cpsl-color-background-48);\n    // ACTIVE\n    --button-primary-active-color: var(--cpsl-color-background-48);\n    --button-primary-active-background-color: var(--cpsl-color-background-4);\n    --button-primary-active-border-color: var(--cpsl-color-background-4);\n    --button-primary-active-icon-color: var(--cpsl-color-background-48);\n  }\n}\n\n.main-route {\n  --button-padding-start: 8px;\n  --button-padding-end: 8px;\n  --button-padding-top: 8px;\n  --button-padding-bottom: 8px;\n  --button-justify-content: start;\n\n  // DEFAULT\n  --button-primary-color: var(--cpsl-color-background-48);\n  --button-primary-background-color: var(--cpsl-color-background-0);\n  --button-primary-border-color: var(--cpsl-color-background-0);\n  --button-primary-icon-color: var(--cpsl-color-background-48);\n  // DISABLED\n  --button-primary-disabled-color: var(--cpsl-color-background-48);\n  --button-primary-disabled-background-color: var(--cpsl-color-background-0);\n  --button-primary-disabled-border-color: var(--cpsl-color-background-0);\n  --button-primary-disabled-icon-color: var(--cpsl-color-background-48);\n  // HOVER\n  --button-primary-hover-color: var(--cpsl-color-background-48);\n  --button-primary-hover-background-color: var(--cpsl-color-background-4);\n  --button-primary-hover-border-color: var(--cpsl-color-background-4);\n  --button-primary-hover-icon-color: var(--cpsl-color-background-48);\n  // ACTIVE\n  --button-primary-active-color: var(--cpsl-color-background-48);\n  --button-primary-active-background-color: var(--cpsl-color-background-4);\n  --button-primary-active-border-color: var(--cpsl-color-background-4);\n  --button-primary-active-icon-color: var(--cpsl-color-background-48);\n\n  &.selected {\n    // DEFAULT\n    --button-primary-color: var(--cpsl-color-text-primary);\n    --button-primary-background-color: var(--cpsl-color-background-4);\n    --button-primary-border-color: var(--cpsl-color-background-4);\n    --button-primary-icon-color: var(--cpsl-color-text-primary);\n    // DISABLED\n    --button-primary-disabled-color: var(--cpsl-color-text-primary);\n    --button-primary-disabled-background-color: var(--cpsl-color-background-4);\n    --button-primary-disabled-border-color: var(--cpsl-color-background-4);\n    --button-primary-disabled-icon-color: var(--cpsl-color-text-primary);\n    // HOVER\n    --button-primary-hover-color: var(--cpsl-color-text-primary);\n    --button-primary-hover-background-color: var(--cpsl-color-background-4);\n    --button-primary-hover-border-color: var(--cpsl-color-background-4);\n    --button-primary-hover-icon-color: var(--cpsl-color-text-primary);\n    // ACTIVE\n    --button-primary-active-color: var(--cpsl-color-text-primary);\n    --button-primary-active-background-color: var(--cpsl-color-background-4);\n    --button-primary-active-border-color: var(--cpsl-color-background-4);\n    --button-primary-active-icon-color: var(--cpsl-color-text-primary);\n  }\n}\n"], "names": ["CpslNavButton", "handleNavButtonClick", "_this", "cpslNavButtonClick", "emit", "route", "handleSubRouteClick", "cpslNavButtonSubRouteClick", "_this2", "selectedSubRoute", "_a", "this", "subRoutes", "find", "sr", "exactSubRouteMatch", "path", "concat", "value", "includes", "isMainRouteSelected", "exactMainRouteMatch", "h", "Host", "key", "id", "class", "fullWidth", "variant", "disabled", "onClick", "name", "_b", "length", "map", "label", "get"], "sourceRoot": ""}