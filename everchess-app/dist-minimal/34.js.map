{"version": 3, "file": "34.js", "mappings": "8IAAaA,E,kGA4KPC,E,QAAYC,GAAOC,IAAGH,IAAAA,GAAAI,EAAAA,EAAAA,GAAA,sFACjB,SAAAC,GAAa,OAAAA,EAAVC,WAA8B,OAAS,OAAO,EAChD,SAAAC,GAAa,OAAAA,EAAVD,WAA8B,QAAU,OAAO,GC3K1DE,EDQiB,SAAHC,GAAsF,IAAhFC,EAAID,EAAJC,KAAMC,EAAMF,EAANE,OAAQL,EAAUG,EAAVH,WAAYM,EAAYH,EAAZG,aAAcC,EAAcJ,EAAdI,eAAgBC,EAAiBL,EAAjBK,kBAC9EC,GAAwDC,EAAAA,EAAAA,UAAS,MAAKC,GAAAC,EAAAA,EAAAA,GAAAH,EAAA,GAA/DI,EAAoBF,EAAA,GAAEG,EAAuBH,EAAA,GACpDI,GAA0DL,EAAAA,EAAAA,UAAS,MAAKM,GAAAJ,EAAAA,EAAAA,GAAAG,EAAA,GAAjEE,EAAqBD,EAAA,GAAEE,EAAwBF,EAAA,GACtDG,GAAsDT,EAAAA,EAAAA,UAAS,MAAKU,GAAAR,EAAAA,EAAAA,GAAAO,EAAA,GAA7DE,EAAmBD,EAAA,GAAEE,EAAsBF,EAAA,IAClDG,EAAAA,EAAAA,WAAU,WACR,IAAMC,GAAwBC,EAAAA,EAAAA,MAC5B,kBAAM,8BAAiCC,KAAK,SAACC,GAAG,MAAM,CAAEC,QAASD,EAAIE,iBAAkB,EAAE,GAErFC,GAAyBL,EAAAA,EAAAA,MAC7B,kBAAM,8BAAiCC,KAAK,SAACC,GAAG,MAAM,CAAEC,QAASD,EAAII,kBAAmB,EAAE,GAEtFC,GAAuBP,EAAAA,EAAAA,MAC3B,kBAAM,8BAAiCC,KAAK,SAACC,GAAG,MAAM,CAAEC,QAASD,EAAIM,gBAAiB,EAAE,GAE1FnB,EAAwBU,GACxBN,EAAyBY,GACzBR,EAAuBU,EACzB,EAAG,IACH,IAAME,EAAS3B,EAAe4B,SAnBK,qCADL,2CAqBxBC,GAA0BC,EAAAA,EAAAA,aAC9B,SAACC,GAAG,OAAKC,EAAAA,EAAAA,QAAQ,EAAQ,KAAIC,EAAAA,KAAE,SAAAC,IAAA,IAAAC,EAAA,OAAAF,EAAAA,KAAA,SAAAG,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,UACxBzC,EAAK0C,aAAgBvC,EAAewC,WAAU,CAAAJ,EAAAE,KAAA,cAC3C,IAAIG,MAAM,2BAA0B,OAEhC,OAFgCL,EAAAE,KAAA,EAE1BzC,EAAK6C,IAAIC,OAAOC,eAAe/C,EAAK0C,YAAa,CACjER,IAAAA,EACAc,KAAM7C,EAAewC,WACrBM,cAAcC,EAAAA,EAAAA,IAAiB/C,EAAegD,SAC9CpB,SAAU5B,EAAe4B,SACzBqB,SAAUjD,EAAeiD,eAAY,EACrCC,sBAAuBlD,EAAekD,4BAAyB,IAC/D,OAPO,OAAHf,EAAGC,EAAAe,KAAAf,EAAAgB,OAAA,SAQFjB,EAAIkB,KAAKC,WAAS,wBAAAlB,EAAAmB,OAAA,EAAArB,EAAA,GACzB,EACF,CAAClC,EAAeiD,SAAUjD,EAAewC,WAAY3C,EAAKiD,aAAc9C,EAAe4B,SAAU/B,IAE7F2D,GAAyB1B,EAAAA,EAAAA,aAC7B,SAAC2B,GAAO,OAAKzB,EAAAA,EAAAA,QAAQ,EAAQ,KAAIC,EAAAA,KAAE,SAAAyB,IAAA,IAAAC,EAAAC,EAAAZ,EAAAa,EAAAC,EAAAC,EAAA,OAAA9B,EAAAA,KAAA,SAAA+B,GAAA,cAAAA,EAAA3B,KAAA2B,EAAA1B,MAAA,OAOf,OAPe0B,EAAA3B,KAAA,EAAAsB,GAENM,EAAAA,EAAAA,IACvBlE,EAAamE,UACbC,EAAAA,GAAeC,QACfX,EAAQY,cAAcC,MACvBV,GAAAvD,EAAAA,EAAAA,GAAAsD,EAAA,GAJMX,EAAOY,EAAA,GAAEC,EAAKD,EAAA,GAAAI,EAAA1B,KAAA,EAKCzC,EAAK6C,IAAIC,OAAO4B,qBAAqB,CACzDC,OAAQ3E,EAAK0C,YACbU,SAAUjD,EAAeiD,SACzBwB,WAAYzE,EAAe0E,GAC3BxB,sBAAuBlD,EAAekD,sBACtCyB,QAAS,CACPC,aAAcnB,EAAQoB,mBAAmBC,WACzCC,KAAMtB,EAAQuB,aAAaV,KAC3BtB,QAAAA,EACAa,MAAAA,EACAoB,cAAexB,EAAQyB,oBAAoBJ,WAC3CK,OAAQC,EAAAA,GAAqBC,YAE/B,OAbIvB,EAAOE,EAAAb,KAcblD,EAAkB6D,GACbrE,GACH6F,WAAW,WACa,oBAAXC,QACTA,OAAOC,OAEX,EAAG,KACJxB,EAAA1B,KAAA,eAAA0B,EAAA3B,KAAA,EAAA0B,EAAAC,EAAA,SAEDyB,QAAQC,MAAK3B,GAAI,wBAAAC,EAAAT,OAAA,EAAAG,EAAA,gBAEnB,EACF,CAAC1D,EAAeiD,SAAUjD,EAAe0E,GAAI1E,EAAekD,sBAAuBzD,IAE/EkG,GAAoB7D,EAAAA,EAAAA,aACxB,SAAC2B,GAAO,OAAKzB,EAAAA,EAAAA,QAAQ,EAAQ,KAAIC,EAAAA,KAAE,SAAA2D,IAAA,IAAAC,EAAA,OAAA5D,EAAAA,KAAA,SAAA6D,GAAA,cAAAA,EAAAzD,KAAAyD,EAAAxD,MAAA,OAClB,OADkBwD,EAAAxD,KAAA,GACZyD,EAAAA,EAAAA,GAAYlG,EAAMG,EAAgBC,EAAmB,CACxEgF,cAAexB,EAAQuC,qBACvBpB,aAAcnB,EAAQwC,yBAAsB,EAC5ClB,KAAMtB,EAAQyC,aAAa5B,KAAK6B,cAChCC,mBAAoB3C,EAAQ4C,qBAC5BC,gBAAiB7C,EAAQ8C,eAAeD,gBACxCE,QAAS/C,EAAQ8C,eAAeC,UAChC,OAPU,OAANX,EAAMC,EAAA3C,KAAA2C,EAAA1C,OAAA,SAQL,CAAEqD,UAAWZ,EAAQa,0BAA0B,IAAO,wBAAAZ,EAAAvC,OAAA,EAAAqC,EAAA,GAC7D,EACF,CACE/F,EACAG,EAAe0E,GACf1E,EAAe4B,SACf5B,EAAeiD,SACfjD,EAAewC,WACfvC,IAGE0G,GAAQC,EAAAA,EAAAA,SAAQ,WACpB,IAAKtG,IAAyBI,EAC5B,OAAO,KAET,IAAMmG,GAAeC,EAAAA,EAAAA,IAAgB/G,EAAc,CACjDiD,QAAShD,EAAegD,QACxBa,MAAO7D,EAAe6D,MACtBkD,SAAU5C,EAAAA,GAAeC,UAE3B,MAA+B,QAAxBpE,EAAe6C,MAAiCmE,EAAAA,EAAAA,KACrD1G,EACA,CACE2G,QAAS,WACTC,iBAAkBlH,EAAe+E,KACjCF,mBAAoB7E,EAAe4E,aACnCiC,aAAAA,EACAM,cAAenH,EAAeoH,QAC9BC,SAAS,EACTC,MAAOxH,EAAS,OAAS,QACzByH,MAAO,CACLC,OAAQ,OACRC,MAAO,OACPC,OAAQ,OACRC,aAAc,EACdC,OAAQ,GAEVpE,uBAAAA,EACA3B,wBAAAA,KAEgBmF,EAAAA,EAAAA,KAClBtG,EACA,CACEuG,QAAS,WACTI,SAAS,EACTC,MAAOxH,EAAS,OAAS,QACzByH,MAAO,CACLC,OAAQ,OACRC,MAAO,OACPC,OAAQ,OACRC,aAAc,EACdC,OAAQ,GAEVV,iBAAkBL,EAClBgB,oBAAqB7H,EAAeoH,QACpCzB,kBAAAA,EACAnC,uBAAAA,EACA3B,wBAAAA,GAGN,EAAG,CACD7B,EAAe6C,KACf7C,EAAeoH,QACfpH,EAAeiD,SACfjD,EAAewC,WACfxC,EAAe6D,MACf8B,EACAnC,EACA3B,EACA/B,EACAQ,EACAI,IAEF,OAAKI,GAGkBkG,EAAAA,EAAAA,KAAI5H,EAAW,CAAEK,WAAAA,EAAYqI,UAA0Bd,EAAAA,EAAAA,KAAIlG,EAAqB,CAAEa,OAAAA,EAAQoG,MAAO/H,EAAe4B,SAAUkG,SAAUnB,MAFlJ,IAGX,C", "sources": ["webpack://everchess-app/./node_modules/@getpara/react-common/dist/components/MoonPayEmbed.js", "webpack://everchess-app/./node_modules/@getpara/react-sdk/dist/modal/components/AddFunds/MoonPayEmbed.js"], "sourcesContent": ["\"use client\";\nimport {\n  __async\n} from \"../chunk-GOCCUU3Z.js\";\nimport { jsx } from \"react/jsx-runtime\";\nimport { getNetworkPrefix, OnRampProvider, OnRampPurchaseStatus } from \"@getpara/web-sdk\";\nimport { lazy, useCallback, useEffect, useMemo, useState } from \"react\";\nimport { reverseCurrencyLookup, offRampSend, getCurrencyCode } from \"../utils/index.js\";\nimport styled from \"styled-components\";\nconst MOONPAY_PUBLISHABLE_KEY = \"pk_live_EQva4LydtNDE0Rwd9X7SG9w58wqOzbux\";\nconst MOONPAY_PUBLISHABLE_KEY_TEST = \"pk_test_HYobzemmTBXxcSStVA4dSED6jT\";\nconst MoonPayEmbed = ({ para, isDark, isEmbedded, onRampConfig, onRampPurchase, setOnRampPurchase }) => {\n  const [LazyMoonPayBuyWidget, setLazyMoonPayBuyWidget] = useState(null);\n  const [LazyMoonPaySellWidget, setLazyMoonPaySellWidget] = useState(null);\n  const [LazyMoonPayProvider, setLazyMoonPayProvider] = useState(null);\n  useEffect(() => {\n    const _LazyMoonPayBuyWidget = lazy(\n      () => import(\"@moonpay/moonpay-react\").then((mod) => ({ default: mod.MoonPayBuyWidget }))\n    );\n    const _LazyMoonPaySellWidget = lazy(\n      () => import(\"@moonpay/moonpay-react\").then((mod) => ({ default: mod.MoonPaySellWidget }))\n    );\n    const _LazyMoonPayProvider = lazy(\n      () => import(\"@moonpay/moonpay-react\").then((mod) => ({ default: mod.MoonPayProvider }))\n    );\n    setLazyMoonPayBuyWidget(_LazyMoonPayBuyWidget);\n    setLazyMoonPaySellWidget(_LazyMoonPaySellWidget);\n    setLazyMoonPayProvider(_LazyMoonPayProvider);\n  }, []);\n  const apiKey = onRampPurchase.testMode ? MOONPAY_PUBLISHABLE_KEY_TEST : MOONPAY_PUBLISHABLE_KEY;\n  const onUrlSignatureRequested = useCallback(\n    (url) => __async(void 0, null, function* () {\n      if (!para.getUserId() || !onRampPurchase.walletType) {\n        throw new Error(\"missing required fields\");\n      }\n      const res = yield para.ctx.client.signMoonPayUrl(para.getUserId(), {\n        url,\n        type: onRampPurchase.walletType,\n        cosmosPrefix: getNetworkPrefix(onRampPurchase.network),\n        testMode: onRampPurchase.testMode,\n        walletId: onRampPurchase.walletId || void 0,\n        externalWalletAddress: onRampPurchase.externalWalletAddress || void 0\n      });\n      return res.data.signature;\n    }),\n    [onRampPurchase.walletId, onRampPurchase.walletType, para.cosmosPrefix, onRampPurchase.testMode, para]\n  );\n  const onTransactionCompleted = useCallback(\n    (payload) => __async(void 0, null, function* () {\n      try {\n        const [network, asset] = reverseCurrencyLookup(\n          onRampConfig.assetInfo,\n          OnRampProvider.MOONPAY,\n          payload.quoteCurrency.code\n        );\n        const updated = yield para.ctx.client.updateOnRampPurchase({\n          userId: para.getUserId(),\n          walletId: onRampPurchase.walletId,\n          purchaseId: onRampPurchase.id,\n          externalWalletAddress: onRampPurchase.externalWalletAddress,\n          updates: {\n            fiatQuantity: payload.baseCurrencyAmount.toString(),\n            fiat: payload.baseCurrency.code,\n            network,\n            asset,\n            assetQuantity: payload.quoteCurrencyAmount.toString(),\n            status: OnRampPurchaseStatus.FINISHED\n          }\n        });\n        setOnRampPurchase(updated);\n        if (!isEmbedded) {\n          setTimeout(() => {\n            if (typeof window !== \"undefined\") {\n              window.close();\n            }\n          }, 5e3);\n        }\n      } catch (e) {\n        console.error(e);\n      }\n    }),\n    [onRampPurchase.walletId, onRampPurchase.id, onRampPurchase.externalWalletAddress, isEmbedded]\n  );\n  const onInitiateDeposit = useCallback(\n    (payload) => __async(void 0, null, function* () {\n      const txHash = yield offRampSend(para, onRampPurchase, setOnRampPurchase, {\n        assetQuantity: payload.cryptoCurrencyAmount,\n        fiatQuantity: payload.fiatCurrencyAmount || void 0,\n        fiat: payload.fiatCurrency.code.toUpperCase(),\n        destinationAddress: payload.depositWalletAddress,\n        contractAddress: payload.cryptoCurrency.contractAddress,\n        chainId: payload.cryptoCurrency.chainId\n      });\n      return { depositId: txHash, cancelTransactionOnError: false };\n    }),\n    [\n      para,\n      onRampPurchase.id,\n      onRampPurchase.testMode,\n      onRampPurchase.walletId,\n      onRampPurchase.walletType,\n      setOnRampPurchase\n    ]\n  );\n  const embed = useMemo(() => {\n    if (!LazyMoonPayBuyWidget || !LazyMoonPaySellWidget) {\n      return null;\n    }\n    const currencyCode = getCurrencyCode(onRampConfig, {\n      network: onRampPurchase.network,\n      asset: onRampPurchase.asset,\n      provider: OnRampProvider.MOONPAY\n    });\n    return onRampPurchase.type === \"BUY\" ? /* @__PURE__ */ jsx(\n      LazyMoonPayBuyWidget,\n      {\n        variant: \"embedded\",\n        baseCurrencyCode: onRampPurchase.fiat,\n        baseCurrencyAmount: onRampPurchase.fiatQuantity,\n        currencyCode,\n        walletAddress: onRampPurchase.address,\n        visible: true,\n        theme: isDark ? \"dark\" : \"light\",\n        style: {\n          height: \"100%\",\n          width: \"100%\",\n          border: \"none\",\n          borderRadius: 0,\n          margin: 0\n        },\n        onTransactionCompleted,\n        onUrlSignatureRequested\n      }\n    ) : /* @__PURE__ */ jsx(\n      LazyMoonPaySellWidget,\n      {\n        variant: \"embedded\",\n        visible: true,\n        theme: isDark ? \"dark\" : \"light\",\n        style: {\n          height: \"100%\",\n          width: \"100%\",\n          border: \"none\",\n          borderRadius: 0,\n          margin: 0\n        },\n        baseCurrencyCode: currencyCode,\n        refundWalletAddress: onRampPurchase.address,\n        onInitiateDeposit,\n        onTransactionCompleted,\n        onUrlSignatureRequested\n      }\n    );\n  }, [\n    onRampPurchase.type,\n    onRampPurchase.address,\n    onRampPurchase.walletId,\n    onRampPurchase.walletType,\n    onRampPurchase.asset,\n    onInitiateDeposit,\n    onTransactionCompleted,\n    onUrlSignatureRequested,\n    isDark,\n    LazyMoonPayBuyWidget,\n    LazyMoonPaySellWidget\n  ]);\n  if (!LazyMoonPayProvider) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(Container, { isEmbedded, children: /* @__PURE__ */ jsx(LazyMoonPayProvider, { apiKey, debug: onRampPurchase.testMode, children: embed }) });\n};\nvar MoonPayEmbed_default = MoonPayEmbed;\nconst Container = styled.div`\n  width: ${({ isEmbedded }) => isEmbedded ? \"100%\" : \"100vw\"};\n  height: ${({ isEmbedded }) => isEmbedded ? \"640px\" : \"100vh\"};\n\n  iframe {\n    border: 0 !important;\n  }\n`;\nexport {\n  MoonPayEmbed,\n  MoonPayEmbed_default as default\n};\n", "\"use client\";\nimport \"../../../chunk-MMUBH76A.js\";\nimport { MoonPayEmbed } from \"@getpara/react-common\";\nvar MoonPayEmbed_default = MoonPayEmbed;\nexport {\n  MoonPayEmbed_default as default\n};\n"], "names": ["_templateObject", "Container", "styled", "div", "_taggedTemplateLiteral", "_ref2", "isEmbedded", "_ref3", "MoonPayEmbed_default", "_ref", "para", "isDark", "onRampConfig", "onRampPurchase", "setOnRampPurchase", "_useState", "useState", "_useState2", "_slicedToArray", "LazyMoonPayBuyWidget", "setLazyMoonPayBuyWidget", "_useState3", "_useState4", "LazyMoonPaySellWidget", "setLazyMoonPaySellWidget", "_useState5", "_useState6", "LazyMoonPayProvider", "setLazyMoonPayProvider", "useEffect", "_LazyMoonPayBuyWidget", "lazy", "then", "mod", "default", "MoonPayBuyWidget", "_LazyMoonPaySellWidget", "MoonPaySellWidget", "_LazyMoonPayProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "testMode", "onUrlSignatureRequested", "useCallback", "url", "__async", "_regeneratorRuntime", "_callee", "res", "_context", "prev", "next", "getUserId", "walletType", "Error", "ctx", "client", "signMoonPayUrl", "type", "cosmosPrefix", "getNetworkPrefix", "network", "walletId", "externalWalletAddress", "sent", "abrupt", "data", "signature", "stop", "onTransactionCompleted", "payload", "_callee2", "_reverseCurrency<PERSON><PERSON>u", "_reverseCurrencyLooku2", "asset", "updated", "_t", "_context2", "reverseCurrencyLookup", "assetInfo", "OnRampProvider", "MOONPAY", "quoteCurrency", "code", "updateOnRampPurchase", "userId", "purchaseId", "id", "updates", "fiatQuantity", "baseCurrencyAmount", "toString", "fiat", "baseCurrency", "assetQuantity", "quoteCurrencyAmount", "status", "OnRampPurchaseStatus", "FINISHED", "setTimeout", "window", "close", "console", "error", "onInitiateDeposit", "_callee3", "txHash", "_context3", "offRampSend", "cryptoCurrencyAmount", "fiatCurrencyAmount", "fiatCurrency", "toUpperCase", "destinationAddress", "depositWalletAddress", "contractAddress", "cryptoCurrency", "chainId", "depositId", "cancelTransactionOnError", "embed", "useMemo", "currencyCode", "getCurrencyCode", "provider", "jsx", "variant", "baseCurrencyCode", "wallet<PERSON>ddress", "address", "visible", "theme", "style", "height", "width", "border", "borderRadius", "margin", "refundWalletAddress", "children", "debug"], "sourceRoot": ""}