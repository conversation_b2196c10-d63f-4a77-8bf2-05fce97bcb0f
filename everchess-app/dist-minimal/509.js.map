{"version": 3, "file": "509.js", "mappings": "wLAAaA,EAAqB,CAChCC,GAAI,mBACJC,GAAI,qBACJC,GAAI,qBACJC,GAAI,qBACJC,GAAI,uBAMOC,EAAkB,SAACC,GAC9B,QAAmBC,IAAfD,GAA2C,KAAfA,EAC9B,OAAO,EAET,GAAsB,oBAAXE,QAA2BA,OAAeC,WAAY,CAC/D,IAAMC,EAAaX,EAAcO,GACjC,OAAOE,OAAOC,WAAWC,GAAYC,O,CAEvC,OAAO,CACT,ECjBMC,EAAiC,oBAAnBJ,OAAkCA,YAAiBD,EACjEM,EAAgBD,MAAUA,EAAIE,KAAOF,EAAIE,IAAIC,UAAYH,EAAIE,IAAIC,SAAS,WAC1EC,EAAc,CAAC,GAAI,KAAM,KAAM,KAAM,KAAM,MAOpCC,EAAO,W,uEACkC,S,aAEM,S,SAEf,M,oeA+I3C,YACEC,EAAAA,EAAAA,GAAYC,K,2BAKN,SAAWC,GAGjB,IAFA,IAAIC,EAEJC,EAAA,EAAAC,EAAyBP,EAAWM,EAAAC,EAAAC,OAAAF,IAAE,CAAjC,IAAMhB,EAAUiB,EAAAD,GACbX,EAAUN,EAAgBC,GAI1BmB,EAAWN,KAAaC,EAAWd,EAAWoB,OAAO,GAAGC,cAAgBrB,EAAWsB,MAAM,IAE3FjB,QAAuBJ,IAAZkB,IACbJ,EAAUI,E,CAMd,OAAOJ,C,GACR,CAAAQ,IAAA,gBAAAC,MAEO,WACN,IAAML,EAAUN,KAAKY,WAAW,QAMhC,GAAKN,GAAuB,KAAZA,EAAhB,CAKA,IAAMO,EACQ,SAAZP,EACI,OAEAZ,EAAa,aAAAoB,OACER,EAAO,2CAGnBA,EAAU,GAAM,IAAM,IAE/B,MAAO,CACL,KAAQ,OAAFQ,OAASD,GACf,MAAS,GAAFC,OAAKD,GACZ,YAAa,GAAFC,OAAKD,G,mCAKZ,SAAkBZ,EAAkBc,GAC1C,IAAMT,EAAUN,KAAKY,WAAWX,GAEhC,GAAKK,EAAL,CAMA,IAAMU,EAAStB,E,oBAEEY,EAAO,2CAGpBA,EAAU,GAAKA,EAAU,GACtBA,EAAU,GAAM,IAAM,IACvB,OAEN,OAAAW,EAAAA,EAAAA,GAAA,GACGF,EAAWC,E,IAEf,CAAAN,IAAA,kBAAAC,MAEO,SAAgBO,GACtB,OAAOlB,KAAKmB,kBAAkB,SAAUD,EAAQ,eAAiB,c,GAClE,CAAAR,IAAA,gBAAAC,MAEO,SAAcO,GACpB,OAAOlB,KAAKmB,kBAAkB,OAAQD,EAAQ,OAAS,Q,GACxD,CAAAR,IAAA,gBAAAC,MAEO,SAAcO,GACpB,OAAOlB,KAAKmB,kBAAkB,OAAQD,EAAQ,QAAU,O,GACzD,CAAAR,IAAA,SAAAC,MAED,WACE,IAAMO,EAAyB,QAAjBE,SAASC,IACvB,OACEC,EAAAA,EAAAA,GAACC,EAAAA,EAAI,CAAAb,IAAA,2CACHc,MAAKC,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,GACA1B,KAAK2B,gBAAgBT,IACrBlB,KAAK4B,cAAcV,IACnBlB,KAAK6B,cAAcX,IACnBlB,KAAK8B,mBAGVR,EAAAA,EAAAA,GAAA,QAAAZ,IAAA,6C,IAGL,CA7PiB,G,QCZD,g+F", "sources": ["webpack://everchess-app/src/utils/media.ts", "webpack://everchess-app/src/components/cpsl-col/cpsl-col.tsx", "webpack://everchess-app/src/components/cpsl-col/cpsl-col.scss"], "sourcesContent": ["export const SIZE_TO_MEDIA: any = {\n  xs: '(min-width: 0px)',\n  sm: '(min-width: 576px)',\n  md: '(min-width: 768px)',\n  lg: '(min-width: 992px)',\n  xl: '(min-width: 1200px)',\n};\n\n// Check if the window matches the media query\n// at the breakpoint passed\n// e.g. matchBreakpoint('sm') => true if screen width exceeds 576px\nexport const matchBreakpoint = (breakpoint: string | undefined) => {\n  if (breakpoint === undefined || breakpoint === '') {\n    return true;\n  }\n  if (typeof window !== 'undefined' && (window as any).matchMedia) {\n    const mediaQuery = SIZE_TO_MEDIA[breakpoint];\n    return window.matchMedia(mediaQuery).matches;\n  }\n  return false;\n};\n", "import { Component, Host, Listen, Prop, forceUpdate, h } from '@stencil/core';\nimport { matchBreakpoint } from '../../utils/media.js';\n\nconst win = typeof (window as any) !== 'undefined' ? (window as any) : undefined;\nconst SUPPORTS_VARS = win && !!(win.CSS && win.CSS.supports && win.CSS.supports('--a: 0'));\nconst BREAKPOINTS = ['', 'xs', 'sm', 'md', 'lg', 'xl'];\n\n@Component({\n  tag: 'cpsl-col',\n  styleUrl: 'cpsl-col.scss',\n  shadow: true,\n})\nexport class CpslCol {\n  @Prop() align?: React.CSSProperties['alignItems'] = 'center';\n\n  @Prop() justify?: React.CSSProperties['justifyContent'] = 'center';\n\n  @Prop() gap?: React.CSSProperties['gap'] = '8px';\n\n  /**\n   * The amount to offset the column, in terms of how many columns it should shift to the end\n   * of the total available.\n   */\n  @Prop() offset?: string;\n\n  /**\n   * The amount to offset the column for xs screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() offsetXs?: string;\n\n  /**\n   * The amount to offset the column for sm screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() offsetSm?: string;\n\n  /**\n   * The amount to offset the column for md screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() offsetMd?: string;\n\n  /**\n   * The amount to offset the column for lg screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() offsetLg?: string;\n\n  /**\n   * The amount to offset the column for xl screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() offsetXl?: string;\n\n  /**\n   * The amount to pull the column, in terms of how many columns it should shift to the start of\n   * the total available.\n   */\n  @Prop() pull?: string;\n\n  /**\n   * The amount to pull the column for xs screens, in terms of how many columns it should shift\n   * to the start of the total available.\n   */\n  @Prop() pullXs?: string;\n  /**\n   * The amount to pull the column for sm screens, in terms of how many columns it should shift\n   * to the start of the total available.\n   */\n  @Prop() pullSm?: string;\n  /**\n   * The amount to pull the column for md screens, in terms of how many columns it should shift\n   * to the start of the total available.\n   */\n  @Prop() pullMd?: string;\n  /**\n   * The amount to pull the column for lg screens, in terms of how many columns it should shift\n   * to the start of the total available.\n   */\n  @Prop() pullLg?: string;\n  /**\n   * The amount to pull the column for xl screens, in terms of how many columns it should shift\n   * to the start of the total available.\n   */\n  @Prop() pullXl?: string;\n\n  /**\n   * The amount to push the column, in terms of how many columns it should shift to the end\n   * of the total available.\n   */\n  @Prop() push?: string;\n\n  /**\n   * The amount to push the column for xs screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() pushXs?: string;\n\n  /**\n   * The amount to push the column for sm screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() pushSm?: string;\n\n  /**\n   * The amount to push the column for md screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() pushMd?: string;\n\n  /**\n   * The amount to push the column for lg screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() pushLg?: string;\n\n  /**\n   * The amount to push the column for xl screens, in terms of how many columns it should shift\n   * to the end of the total available.\n   */\n  @Prop() pushXl?: string;\n\n  /**\n   * The size of the column, in terms of how many columns it should take up out of the total\n   * available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() size?: string;\n\n  /**\n   * The size of the column for xs screens, in terms of how many columns it should take up out\n   * of the total available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() sizeXs?: string;\n\n  /**\n   * The size of the column for sm screens, in terms of how many columns it should take up out\n   * of the total available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() sizeSm?: string;\n\n  /**\n   * The size of the column for md screens, in terms of how many columns it should take up out\n   * of the total available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() sizeMd?: string;\n\n  /**\n   * The size of the column for lg screens, in terms of how many columns it should take up out\n   * of the total available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() sizeLg?: string;\n\n  /**\n   * The size of the column for xl screens, in terms of how many columns it should take up out\n   * of the total available. If `\"auto\"` is passed, the column will be the size of its content.\n   */\n  @Prop() sizeXl?: string;\n\n  @Listen('resize', { target: 'window' })\n  onResize() {\n    forceUpdate(this);\n  }\n\n  // Loop through all of the breakpoints to see if the media query\n  // matches and grab the column value from the relevant prop if so\n  private getColumns(property: string) {\n    let matched;\n\n    for (const breakpoint of BREAKPOINTS) {\n      const matches = matchBreakpoint(breakpoint);\n\n      // Grab the value of the property, if it exists and our\n      // media query matches we return the value\n      const columns = (this as any)[property + breakpoint.charAt(0).toUpperCase() + breakpoint.slice(1)];\n\n      if (matches && columns !== undefined) {\n        matched = columns;\n      }\n    }\n\n    // Return the last matched columns since the breakpoints\n    // increase in size and we want to return the largest match\n    return matched;\n  }\n\n  private calculateSize() {\n    const columns = this.getColumns('size');\n\n    // If size wasn't set for any breakpoint\n    // or if the user set the size without a value\n    // it means we need to stick with the default and return\n    // e.g. <ion-col size-md>\n    if (!columns || columns === '') {\n      return;\n    }\n\n    // If the size is set to auto then don't calculate a size\n    const colSize =\n      columns === 'auto'\n        ? 'auto'\n        : // If CSS supports variables we should use the grid columns var\n          SUPPORTS_VARS\n          ? `calc(calc(${columns} / var(--ion-grid-columns, 12)) * 100%)`\n          : // Convert the columns to a percentage by dividing by the total number\n            // of columns (12) and then multiplying by 100\n            (columns / 12) * 100 + '%';\n\n    return {\n      'flex': `0 0 ${colSize}`,\n      'width': `${colSize}`,\n      'max-width': `${colSize}`,\n    };\n  }\n\n  // Called by push, pull, and offset since they use the same calculations\n  private calculatePosition(property: string, modifier: string) {\n    const columns = this.getColumns(property);\n\n    if (!columns) {\n      return;\n    }\n\n    // If the number of columns passed are greater than 0 and less than\n    // 12 we can position the column, else default to auto\n    const amount = SUPPORTS_VARS\n      ? // If CSS supports variables we should use the grid columns var\n        `calc(calc(${columns} / var(--ion-grid-columns, 12)) * 100%)`\n      : // Convert the columns to a percentage by dividing by the total number\n        // of columns (12) and then multiplying by 100\n        columns > 0 && columns < 12\n        ? (columns / 12) * 100 + '%'\n        : 'auto';\n\n    return {\n      [modifier]: amount,\n    };\n  }\n\n  private calculateOffset(isRTL: boolean) {\n    return this.calculatePosition('offset', isRTL ? 'margin-right' : 'margin-left');\n  }\n\n  private calculatePull(isRTL: boolean) {\n    return this.calculatePosition('pull', isRTL ? 'left' : 'right');\n  }\n\n  private calculatePush(isRTL: boolean) {\n    return this.calculatePosition('push', isRTL ? 'right' : 'left');\n  }\n\n  render() {\n    const isRTL = document.dir === 'rtl';\n    return (\n      <Host\n        style={{\n          ...this.calculateOffset(isRTL),\n          ...this.calculatePull(isRTL),\n          ...this.calculatePush(isRTL),\n          ...this.calculateSize(),\n        }}\n      >\n        <slot></slot>\n      </Host>\n    );\n  }\n}\n", "@import './cpsl-col.vars';\n\n// Column\n// --------------------------------------------------\n\n:host {\n  /**\n   * @prop --cpsl-grid-columns: The number of total Columns in the Grid\n   * @prop --cpsl-grid-column-padding: Padding for the Column\n   * @prop --cpsl-grid-column-padding-xs: Padding for the Column on xs screens and up\n   * @prop --cpsl-grid-column-padding-sm: Padding for the Column on sm screens and up\n   * @prop --cpsl-grid-column-padding-md: Padding for the Column on md screens and up\n   * @prop --cpsl-grid-column-padding-lg: Padding for the Column on lg screens and up\n   * @prop --cpsl-grid-column-padding-xl: Padding for the Column on xl screens and up\n   */\n  @include make-breakpoint-padding($grid-column-paddings);\n  @include margin(0);\n\n  box-sizing: border-box;\n\n  position: relative;\n\n  flex-basis: 0;\n  flex-grow: 1;\n\n  width: 100%;\n  max-width: 100%;\n  min-height: 1px; // Prevent columns from collapsing when empty\n}\n"], "names": ["SIZE_TO_MEDIA", "xs", "sm", "md", "lg", "xl", "matchBreakpoint", "breakpoint", "undefined", "window", "matchMedia", "mediaQuery", "matches", "win", "SUPPORTS_VARS", "CSS", "supports", "BREAKPOINTS", "CpslCol", "forceUpdate", "this", "property", "matched", "_i", "_BREAKPOINTS", "length", "columns", "char<PERSON>t", "toUpperCase", "slice", "key", "value", "getColumns", "colSize", "concat", "modifier", "amount", "_defineProperty", "isRTL", "calculatePosition", "document", "dir", "h", "Host", "style", "Object", "assign", "calculateOffset", "calculatePull", "calculatePush", "calculateSize"], "sourceRoot": ""}