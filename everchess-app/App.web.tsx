// This is the entry point for the Web PWA.

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ParaWalletProvider } from './src/components/ParaWalletProvider';
import { ParaTestComponent } from './src/components/ParaTestComponent';
import { paraConfig, validateParaConfig } from './src/config/para.config';

// Simple Test Component (without Para hooks)
const SimpleTestComponent: React.FC = () => {
  const [usePara, setUsePara] = useState(false);

  const handleTogglePara = () => {
    setUsePara(!usePara);
  };

  if (usePara) {
    return (
      <ParaWalletProvider>
        <ParaTestComponent />
      </ParaWalletProvider>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Everchess</Text>
      <Text style={styles.subtitle}>Ancient Made Modern</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: Testing Mode (No Para SDK)
        </Text>
      </View>

      <TouchableOpacity style={styles.button} onPress={handleTogglePara}>
        <Text style={styles.buttonText}>
          Enable Para Wallet
        </Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>🏰 3D Chess Experience</Text>
        <Text style={styles.infoText}>🔗 Web3 Integration</Text>
        <Text style={styles.infoText}>🎮 Cross-Platform Gaming</Text>
      </View>
    </View>
  );
};

// Global error handler to catch all unhandled errors
window.addEventListener('error', (event) => {
  console.error('🔥 Global Error:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error,
    stack: event.error?.stack,
  });
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🔥 Unhandled Promise Rejection:', {
    reason: event.reason,
    promise: event.promise,
  });
});



export default function App() {
  console.log('🔍 App starting...');

  return (
    <View style={styles.appContainer}>
      <SimpleTestComponent />
    </View>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#5856D6',
    marginBottom: 40,
    fontStyle: 'italic',
  },
  statusContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#1e1e1e',
    borderRadius: 8,
    minWidth: 250,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 5,
  },
  button: {
    backgroundColor: '#5856D6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 30,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    alignItems: 'center',
  },
  infoText: {
    color: '#888',
    fontSize: 16,
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
  },
});
