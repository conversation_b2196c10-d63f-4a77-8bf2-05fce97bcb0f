// This is the entry point for the Web PWA.

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ParaProvider, ParaWalletButton } from './src/components/ParaWalletIntegration';

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🔥 App Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorBoundaryContainer}>
          <View style={styles.errorCard}>
            <Text style={styles.errorTitle}>Something went wrong</Text>
            <Text style={styles.errorMessage}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </Text>
            <Text
              style={styles.retryButton}
              onPress={() => this.setState({ hasError: false, error: null })}
            >
              Try Again
            </Text>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

// Global error handler to catch all unhandled errors
window.addEventListener('error', (event) => {
  console.error('🔥 Global Error:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error,
    stack: event.error?.stack,
  });
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🔥 Unhandled Promise Rejection:', {
    reason: event.reason,
    promise: event.promise,
  });
});

// Main App Component
const App: React.FC = () => {
  // Try web SDK variables first, fallback to Expo variables
  const apiKey = process.env.REACT_APP_PARA_API_KEY || process.env.EXPO_PUBLIC_PARA_API_KEY;
  const projectId = process.env.REACT_APP_PARA_PROJECT_ID || process.env.EXPO_PUBLIC_PARA_PROJECT_ID;
  const environment = process.env.REACT_APP_PARA_ENVIRONMENT || process.env.EXPO_PUBLIC_PARA_ENV || 'beta';

  console.log('🔍 App starting with environment:', {
    NODE_ENV: process.env.NODE_ENV,
    REACT_APP_PARA_API_KEY: process.env.REACT_APP_PARA_API_KEY ? 'SET' : 'MISSING',
    REACT_APP_PARA_PROJECT_ID: process.env.REACT_APP_PARA_PROJECT_ID ? 'SET' : 'MISSING',
    REACT_APP_PARA_ENVIRONMENT: process.env.REACT_APP_PARA_ENVIRONMENT || 'DEFAULT(beta)',
    EXPO_PUBLIC_PARA_API_KEY: process.env.EXPO_PUBLIC_PARA_API_KEY ? 'SET' : 'MISSING',
    FINAL_API_KEY: apiKey ? 'SET' : 'MISSING',
    FINAL_PROJECT_ID: projectId ? 'SET' : 'MISSING',
    FINAL_ENVIRONMENT: environment,
  });

  if (!apiKey) {
    return (
      <View style={styles.configErrorContainer}>
        <View style={styles.configErrorCard}>
          <Text style={styles.configErrorTitle}>Configuration Required</Text>
          <Text style={styles.configErrorMessage}>
            Para API Key is missing. Please set REACT_APP_PARA_API_KEY or EXPO_PUBLIC_PARA_API_KEY in your .env file.
          </Text>
          <View style={styles.envDebugContainer}>
            <Text style={styles.envDebugTitle}>Current environment variables:</Text>
            <Text style={styles.envDebugText}>
              {JSON.stringify({
                NODE_ENV: process.env.NODE_ENV,
                REACT_APP_PARA_API_KEY: process.env.REACT_APP_PARA_API_KEY ? 'Set' : 'Missing',
                REACT_APP_PARA_PROJECT_ID: process.env.REACT_APP_PARA_PROJECT_ID ? 'Set' : 'Missing',
                EXPO_PUBLIC_PARA_API_KEY: process.env.EXPO_PUBLIC_PARA_API_KEY ? 'Set' : 'Missing',
                FINAL_API_KEY: apiKey ? 'Set' : 'Missing',
                FINAL_PROJECT_ID: projectId ? 'Set' : 'Missing',
              }, null, 2)}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <ErrorBoundary>
      <ParaProvider apiKey={apiKey} projectId={projectId}>
        <View style={styles.appContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Everchess App</Text>
            <Text style={styles.subtitle}>Para Wallet Integration Demo</Text>
          </View>

          <View style={styles.main}>
            <View style={styles.walletSection}>
              <Text style={styles.sectionTitle}>Para Wallet Connection</Text>
              <ParaWalletButton />
            </View>

            <View style={styles.statusSection}>
              <Text style={styles.sectionTitle}>App Status</Text>
              <View style={styles.statusGrid}>
                <View style={styles.statusCard}>
                  <Text style={styles.statusCardTitle}>✅ Environment</Text>
                  <Text style={styles.statusCardText}>Variables loaded correctly</Text>
                </View>
                <View style={styles.statusCard}>
                  <Text style={styles.statusCardTitle}>✅ Webpack</Text>
                  <Text style={styles.statusCardText}>Custom config working</Text>
                </View>
                <View style={styles.statusCard}>
                  <Text style={styles.statusCardTitle}>✅ React Query</Text>
                  <Text style={styles.statusCardText}>Context properly configured</Text>
                </View>
                <View style={styles.statusCard}>
                  <Text style={styles.statusCardTitle}>✅ Para SDK</Text>
                  <Text style={styles.statusCardText}>Integration ready</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ParaProvider>
    </ErrorBoundary>
  );
};

export default App;

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#f9fafb',
    minHeight: '100vh',
  },
  header: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingVertical: 24,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  main: {
    flex: 1,
    maxWidth: 800,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 32,
  },
  walletSection: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 24,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statusSection: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  statusCard: {
    backgroundColor: '#f0fdf4',
    borderWidth: 1,
    borderColor: '#bbf7d0',
    borderRadius: 8,
    padding: 16,
    flex: 1,
    minWidth: 150,
  },
  statusCardTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 4,
  },
  statusCardText: {
    fontSize: 14,
    color: '#16a34a',
  },
  // Error Boundary Styles
  errorBoundaryContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    padding: 20,
  },
  errorCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 32,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#dc2626',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 24,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
  },
  // Configuration Error Styles
  configErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fffbeb',
    padding: 20,
  },
  configErrorCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 32,
    maxWidth: 500,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  configErrorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#d97706',
    marginBottom: 16,
    textAlign: 'center',
  },
  configErrorMessage: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 24,
    textAlign: 'center',
  },
  envDebugContainer: {
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
    padding: 16,
  },
  envDebugTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  envDebugText: {
    fontSize: 12,
    color: '#374151',
    fontFamily: 'monospace',
  },
});
