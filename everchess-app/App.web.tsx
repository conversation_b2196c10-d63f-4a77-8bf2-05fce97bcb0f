// This is the entry point for the Web PWA.

import { Provider } from 'react-redux';
import { QueryClientProvider } from '@tanstack/react-query';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { ParaProvider, ParaModal, Environment as ParaWebEnvironment } from '@getpara/react-sdk';

import { store } from '@/store/store';
import { queryClient } from '@/services/queryClient';
import AppNavigator from '@/navigation/AppNavigator';
import { AuthProvider } from '@/context/AuthContext';

// Centralized component for all the providers and the main navigator
const AppContent = () => (
  <AuthProvider>
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <NavigationContainer>
          <AppNavigator />
          <StatusBar style="light" />
        </NavigationContainer>
      </QueryClientProvider>
    </Provider>
  </AuthProvider>
);

export default function App() {
  const paraApiKey = process.env.EXPO_PUBLIC_PARA_API_KEY;

  if (!paraApiKey) {
    console.error('Para API Key for web is missing. Web auth will not work.');
    return <AppContent />;
  }

  return (
    <ParaProvider
      paraClientConfig={{
        apiKey: paraApiKey,
        env: ParaWebEnvironment.BETA,
      }}
    >
      <AppContent />
      <ParaModal />
    </ParaProvider>
  );
}
