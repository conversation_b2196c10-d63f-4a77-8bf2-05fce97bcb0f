// This is the entry point for the Web PWA.

import { Provider } from 'react-redux';
import { QueryClientProvider } from '@tanstack/react-query';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { ParaProvider, ParaModal, Environment as ParaWebEnvironment } from '@getpara/react-sdk';

import { store } from '@/store/store';
import { queryClient } from '@/services/queryClient';
import AppNavigator from '@/navigation/AppNavigator';
import { AuthProvider } from '@/context/AuthContext';
import { paraConfig, validateParaConfig, ParaEnvironment } from '@/config/para.config';

// Centralized component for all the providers and the main navigator
const AppContent = () => (
  <AuthProvider>
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <NavigationContainer>
          <AppNavigator />
          <StatusBar style="light" />
        </NavigationContainer>
      </QueryClientProvider>
    </Provider>
  </AuthProvider>
);

export default function App() {
  // Validate Para configuration
  if (!validateParaConfig(paraConfig)) {
    console.error('Para configuration is invalid. Web auth will not work.');
    return <AppContent />;
  }

  // Map our environment enum to Para's environment enum
  const paraEnvironment = paraConfig.environment === ParaEnvironment.PRODUCTION
    ? ParaWebEnvironment.PRODUCTION
    : ParaWebEnvironment.BETA;

  return (
    <ParaProvider
      paraClientConfig={{
        apiKey: paraConfig.apiKey,
        env: paraEnvironment,
      }}
    >
      <AppContent />
      <ParaModal />
    </ParaProvider>
  );
}
