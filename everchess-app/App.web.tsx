// This is the entry point for the Web PWA.

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ParaProvider, ParaModal, Environment as ParaWebEnvironment, useModal, useAccount } from '@getpara/react-sdk';
import { paraConfig, validateParaConfig, ParaEnvironment } from '@/config/para.config';

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🔥 Error Boundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🔥 Error Boundary - Full error details:', {
      error: error.message,
      stack: error.stack,
      errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Something went wrong: {this.state.error?.message || 'Unknown error'}
          </Text>
          <Text style={styles.errorText}>
            Check the console for more details.
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

// Create a QueryClient instance for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Para Test Component
const ParaTestComponent: React.FC = () => {
  console.log('🔍 ParaTestComponent rendering...');

  const { openModal } = useModal();
  const { data: account, status } = useAccount();

  console.log('🔍 Para hooks data:', { account, status });

  const handleLogin = () => {
    console.log('🔍 Opening Para modal...');
    openModal();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Everchess</Text>
      <Text style={styles.subtitle}>Ancient Made Modern</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {status || 'loading...'}
        </Text>
        {account?.isConnected && (
          <Text style={styles.statusText}>
            Connected: {account.isConnected ? 'Yes' : 'No'}
          </Text>
        )}
      </View>

      <TouchableOpacity style={styles.button} onPress={handleLogin}>
        <Text style={styles.buttonText}>
          {account?.isConnected ? 'Manage Account' : 'Connect Wallet'}
        </Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>🏰 3D Chess Experience</Text>
        <Text style={styles.infoText}>🔗 Web3 Integration</Text>
        <Text style={styles.infoText}>🎮 Cross-Platform Gaming</Text>
      </View>
    </View>
  );
};

// Main App Content
const AppContent: React.FC = () => {
  return (
    <View style={styles.appContainer}>
      <ParaTestComponent />
    </View>
  );
};

export default function App() {
  console.log('🔍 App starting - paraConfig:', paraConfig);

  // Validate Para configuration
  if (!validateParaConfig(paraConfig)) {
    console.error('🔥 Para configuration validation failed');
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Para API Key is missing. Please set EXPO_PUBLIC_PARA_API_KEY in your .env file.
        </Text>
      </View>
    );
  }

  // Map our environment enum to Para's environment enum
  const paraEnvironment = paraConfig.environment === ParaEnvironment.PRODUCTION
    ? ParaWebEnvironment.PRODUCTION
    : ParaWebEnvironment.BETA;

  console.log('🔍 Para environment:', paraEnvironment);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ParaProvider
          paraClientConfig={{
            apiKey: paraConfig.apiKey,
            env: paraEnvironment,
          }}
        >
          <AppContent />
          <ParaModal />
        </ParaProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#5856D6',
    marginBottom: 40,
    fontStyle: 'italic',
  },
  statusContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#1e1e1e',
    borderRadius: 8,
    minWidth: 250,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 5,
  },
  button: {
    backgroundColor: '#5856D6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 30,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    alignItems: 'center',
  },
  infoText: {
    color: '#888',
    fontSize: 16,
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
  },
});
