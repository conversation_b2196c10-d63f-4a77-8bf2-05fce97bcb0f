module.exports = {
  presets: [
    ['@babel/preset-env', {
      targets: {
        browsers: ['last 2 versions']
      },
      modules: false
    }],
    ['@babel/preset-react', {
      runtime: 'automatic'
    }],
    ['@babel/preset-typescript', {
      allowNamespaces: true
    }]
  ],
  plugins: [
    '@babel/plugin-transform-class-properties',
    '@babel/plugin-transform-runtime',
    '@babel/plugin-proposal-export-namespace-from',
    ['module-resolver', {
      root: ['./src'],
      alias: {
        '@': './src',
      }
    }]
  ]
};
