module.exports = {
  presets: [
    ['@babel/preset-env', { 
      targets: { 
        browsers: ['last 2 versions'],
        node: 'current'
      },
      modules: false,
      useBuiltIns: 'entry',
      corejs: 3
    }],
    ['@babel/preset-react', { 
      runtime: 'automatic',
      development: process.env.NODE_ENV === 'development'
    }],
    ['@babel/preset-typescript', {
      allowNamespaces: true,
      allowDeclareFields: true
    }]
  ],
  plugins: [
    'react-native-web/babel',
    '@babel/plugin-proposal-class-properties',
    '@babel/plugin-proposal-object-rest-spread',
    '@babel/plugin-transform-runtime',
    '@babel/plugin-syntax-dynamic-import',
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator'
  ],
  env: {
    development: {
      plugins: []
    }
  }
};
