#!/usr/bin/env node

/**
 * Test script to verify Para Wallet + Webpack build configuration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Testing Para Wallet + Expo PWA Build Configuration...\n');

// Test 1: Check if Babel configs exist
console.log('1. Checking Babel configurations...');
const babelConfigs = [
  'babel.config.js',
  'babel.config.web.js'
];

babelConfigs.forEach(config => {
  if (fs.existsSync(config)) {
    console.log(`   ✅ ${config} exists`);
  } else {
    console.log(`   ❌ ${config} missing`);
  }
});

// Test 2: Check webpack config
console.log('\n2. Checking webpack configuration...');
if (fs.existsSync('webpack.config.js')) {
  console.log('   ✅ webpack.config.js exists');
  
  // Check if it references the web babel config
  const webpackContent = fs.readFileSync('webpack.config.js', 'utf8');
  if (webpackContent.includes('babel.config.web.js')) {
    console.log('   ✅ webpack.config.js references web-specific Babel config');
  } else {
    console.log('   ⚠️  webpack.config.js may not be using web-specific Babel config');
  }
} else {
  console.log('   ❌ webpack.config.js missing');
}

// Test 3: Check Para dependencies
console.log('\n3. Checking Para Wallet dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const paraDeps = [
  '@getpara/react-sdk',
  '@getpara/react-native-wallet',
  '@getpara/web-sdk'
];

paraDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`   ✅ ${dep} installed`);
  } else {
    console.log(`   ❌ ${dep} missing`);
  }
});

// Test 4: Try a dry run build
console.log('\n4. Testing webpack build (dry run)...');
try {
  execSync('npx webpack --config webpack.config.js --mode development --dry-run', { 
    stdio: 'pipe',
    timeout: 30000 
  });
  console.log('   ✅ Webpack configuration is valid');
} catch (error) {
  console.log('   ❌ Webpack configuration has issues:');
  console.log('   ', error.message.split('\n')[0]);
}

console.log('\n🎯 Build test complete!');
console.log('\nNext steps:');
console.log('1. Run: npm run build:web');
console.log('2. Check for any remaining Babel/JSX errors');
console.log('3. Test Para authentication in the built app');
