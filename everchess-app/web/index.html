<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <title>Everchess</title>
    <style>
      #root {
        display: flex;
        height: 100vh;
        width: 100vw;
      }
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }

      /* Para Modal Styling Fixes - Multiple Selectors */
      [data-para-modal],
      .para-modal,
      #para-modal,
      div[id*="para"],
      div[class*="para"],
      div[data-testid*="para"],
      iframe[src*="para"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
      }

      /* Hide any potential Para overlay or backdrop */
      div[style*="position: fixed"],
      div[style*="z-index"],
      div[style*="backdrop"] {
        background-color: transparent !important;
      }

      /* Force hide any modal-like elements that might be Para */
      div[style*="position: fixed"][style*="top: 0"][style*="left: 0"] {
        display: none !important;
      }

      /* Alternative: If we want to style instead of hide */
      /*
      [data-para-modal],
      .para-modal,
      #para-modal {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      [data-para-modal] > div,
      .para-modal > div,
      #para-modal > div {
        background-color: #2b2b2b !important;
        border-radius: 16px !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
      }
      */
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
