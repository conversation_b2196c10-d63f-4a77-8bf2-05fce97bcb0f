<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <title>Everchess</title>
    <style>
      #root {
        display: flex;
        height: 100vh;
        width: 100vw;
      }
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }

      /* Para Modal Styling Fixes */
      [data-para-modal] {
        z-index: 9999 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      [data-para-modal] > div {
        background-color: #2b2b2b !important;
        border-radius: 16px !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
      }

      /* Ensure Para modal content is visible */
      [data-para-modal] * {
        color: #ffffff !important;
      }

      [data-para-modal] input {
        background-color: #4a4a4a !important;
        border: 1px solid #5a5a5a !important;
        border-radius: 8px !important;
        padding: 12px !important;
        color: #ffffff !important;
      }

      [data-para-modal] button {
        background-color: #4a4a4a !important;
        border: 1px solid #5a5a5a !important;
        border-radius: 8px !important;
        padding: 12px !important;
        color: #ffffff !important;
        cursor: pointer !important;
      }

      [data-para-modal] button:hover {
        background-color: #5a5a5a !important;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
