diff --git a/node_modules/@getpara/core-components/dist/esm/index-91db3414.js b/node_modules/@getpara/core-components/dist/esm/index-91db3414.js
index 49c2d0e..939eaf0 100644
--- a/node_modules/@getpara/core-components/dist/esm/index-91db3414.js
+++ b/node_modules/@getpara/core-components/dist/esm/index-91db3414.js
@@ -91,11 +91,7 @@ var loadModule = (cmpMeta, hostRef, hmrVersionId) => {
           }
       }
   return import(
-    /* @vite-ignore */
-    /* webpackInclude: /\.entry\.js$/ */
-    /* webpackExclude: /\.system\.entry\.js$/ */
-    /* webpackMode: "lazy" */
-    `./${bundleId}.entry.js${""}`
+        `./${bundleId}.entry.js${""}` 
   ).then((importedModule) => {
     {
       cmpModules.set(bundleId, importedModule);
