{"expo": {"name": "<PERSON><PERSON>", "slug": "everchess", "version": "1.0.0", "scheme": "everchess", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#121212"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.everchess.app", "associatedDomains": ["webcredentials:app.beta.usecapsule.com?mode=developer", "webcredentials:app.usecapsule.com"], "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"package": "com.everchess.app", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png", "bundler": "webpack"}, "extra": {"eas": {"projectId": "db6de3da-2176-454f-806e-c5093a131dd7"}}, "plugins": ["./plugins/withAndroidResolutionStrategy.js", "expo-font"]}}