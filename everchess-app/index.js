// Ensure the shim is imported before anything else - matching official example
import "@getpara/react-native-wallet/dist/shim";
import { registerRootComponent } from 'expo';

import App from './App.native';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
