// This is the entry point for Native platforms.

import { useEffect } from 'react';
import { Provider } from 'react-redux';
import { QueryClientProvider } from '@tanstack/react-query';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';

import { store } from '@/store/store';
import { queryClient } from '@/services/queryClient';
import AppNavigator from '@/navigation/AppNavigator';
import { AuthProvider } from '@/context/AuthContext';
import { init as initParaService } from '@/services/paraService';

// Centralized component for all the providers and the main navigator
const AppContent = () => {
  useEffect(() => {
    // Initialize Para client on app startup
    initParaService();
  }, []);

  return (
    <AuthProvider>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <NavigationContainer>
            <AppNavigator />
            <StatusBar style="light" />
          </NavigationContainer>
        </QueryClientProvider>
      </Provider>
    </AuthProvider>
  );
};

export default function App() {
  return <AppContent />;
}
