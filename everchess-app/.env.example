# Para Wallet Configuration
# Get your API key from: https://dashboard.getpara.com

# Required: Para API Key for authentication
EXPO_PUBLIC_PARA_API_KEY=your_para_api_key_here

# Optional: Para Project ID (for native apps)
EXPO_PUBLIC_PARA_PROJECT_ID=your_para_project_id_here

# Environment: beta or production
EXPO_PUBLIC_PARA_ENV=beta

# Development Configuration
NODE_ENV=development

# Expo Configuration
EXPO_PUBLIC_APP_NAME=Everchess
EXPO_PUBLIC_APP_VERSION=1.0.0

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual Para credentials
# 3. Never commit the .env file to version control
