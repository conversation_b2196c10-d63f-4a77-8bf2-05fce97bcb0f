// Minimal App.web.tsx for testing Para Wallet integration

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ParaProvider, ParaModal, Environment as ParaWebEnvironment, useModal, useAccount } from '@getpara/react-sdk';

// Simple Para Test Component
const ParaTestComponent: React.FC = () => {
  const { openModal } = useModal();
  const { data: account, status } = useAccount();

  const handleLogin = () => {
    openModal();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Para Wallet Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {status}
        </Text>
        {account?.isConnected && (
          <Text style={styles.statusText}>
            Connected: {account.isConnected ? 'Yes' : 'No'}
          </Text>
        )}
      </View>

      <TouchableOpacity style={styles.button} onPress={handleLogin}>
        <Text style={styles.buttonText}>
          {account?.isConnected ? 'Manage Account' : 'Connect Wallet'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

// Main App Component
const AppContent: React.FC = () => {
  return (
    <View style={styles.appContainer}>
      <ParaTestComponent />
    </View>
  );
};

// Root App with Para Provider
export default function App() {
  const paraApiKey = process.env.EXPO_PUBLIC_PARA_API_KEY;

  if (!paraApiKey) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Para API Key is missing. Please set EXPO_PUBLIC_PARA_API_KEY in your .env file.
        </Text>
      </View>
    );
  }

  return (
    <ParaProvider
      paraClientConfig={{
        apiKey: paraApiKey,
        env: ParaWebEnvironment.BETA,
      }}
    >
      <AppContent />
      <ParaModal />
    </ParaProvider>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: '#121212',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 30,
  },
  statusContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#1e1e1e',
    borderRadius: 8,
    minWidth: 200,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 16,
    marginBottom: 5,
  },
  button: {
    backgroundColor: '#5856D6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#121212',
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
  },
});
