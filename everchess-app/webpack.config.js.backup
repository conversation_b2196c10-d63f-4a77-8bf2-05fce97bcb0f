const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');

module.exports = {
  entry: './index.web.js',
  mode: 'development',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js',
    publicPath: '/',
  },
  module: {
    rules: [
      // Handle React Native Web imports
      {
        test: /@?react-(native|navigation|native-?stack|navigation-?stack|native-?gesture-?handler|native-?reanimated|native-?screens|native-?safe-?area-?context|native-?vector-?icons|native-?webview|native-?webview-?web).*\.[jt]sx?$/,
        use: [
          {
            loader: 'babel-loader',
            options: {
              presets: ['babel-preset-expo'],
              plugins: ['react-native-web'],
            },
          },
        ],
      },
      // Handle TypeScript and JavaScript files
      {
        test: /\.[jt]sx?$/,
        exclude: /node_modules\/(?!(@react-navigation|react-native|@react-native|@react-three|three|expo|@expo|@babel|@getpara|@gorhom))/,
        use: [
          {
            loader: 'babel-loader',
            options: {
              configFile: false,
              presets: [
                ['babel-preset-expo', { jsxRuntime: 'automatic' }],
                '@babel/preset-typescript',
              ],
              plugins: [
                ['@babel/plugin-transform-private-methods', { 'loose': false }],
                ['@babel/plugin-transform-private-property-in-object', { 'loose': false }],
                ['@babel/plugin-transform-class-properties', { 'loose': false }],
                'react-native-web',
              ],
            },
          },
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg|ttf|otf|woff|woff2)$/,
        use: [
          {
            loader: 'file-loader',
            options: {
              name: '[name].[ext]',
              outputPath: 'assets/',
            },
          },
        ],
      },
    ],
  },
  resolve: {
    extensions: [
      '.web.js',
      '.web.ts',
      '.web.tsx',
      '.js',
      '.jsx',
      '.ts',
      '.tsx',
      '.json',
      '.mjs',
      '.cjs',
    ],
    extensionAlias: {
      '.js': ['.js', '.jsx', '.ts', '.tsx'],
      '.mjs': ['.mjs', '.mts'],
      '.cjs': ['.cjs', '.cts'],
    },
    alias: {
      // React Native Web
      'react-native$': 'react-native-web',
      'react-native-web': 'react-native-web',
      'react-native-svg': 'react-native-svg-web',
      'react-native-reanimated': 'react-native-reanimated/lib/reanimated2',
      'react-native-gesture-handler': '@gorhom/bottom-sheet',
      '@react-native-community/async-storage': '@react-native-async-storage/async-storage',
      
      // Polyfills
      'crypto': 'crypto-browserify',
      'stream': 'stream-browserify',
      'util': 'util',
      'buffer': 'buffer',
      'process': 'process/browser',
      'path': 'path-browserify',
      'zlib': 'browserify-zlib',
      'http': 'stream-http',
      'https': 'https-browserify',
      'url': 'url',
      'os': 'os-browserify/browser',
      'assert': 'assert',
      'constants': 'constants-browserify',
      'querystring': 'querystring-es3',
      '_stream_duplex': 'readable-stream/duplex',
      '_stream_passthrough': 'readable-stream/passthrough',
      '_stream_readable': 'readable-stream/readable',
      '_stream_transform': 'readable-stream/transform',
      '_stream_writable': 'readable-stream/writable',
      'readable-stream': 'readable-stream',
      'vm': 'vm-browserify',
      'fs': false,
      'tls': false,
      'net': false,
      'dns': false,
      'child_process': false,
    },
    fallback: {
      'crypto': require.resolve('crypto-browserify'),
      'stream': require.resolve('stream-browserify'),
      'util': require.resolve('util/'),
      'buffer': require.resolve('buffer/'),
      'process': require.resolve('process/browser'),
    },
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
      React: 'react',
    }),
    new webpack.EnvironmentPlugin({
      NODE_ENV: 'production',
      DEBUG: false,
      PLATFORM: 'web',
    }),
    new webpack.DefinePlugin({
      __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production'),
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process.env.NODE_DEBUG': JSON.stringify(process.env.NODE_DEBUG || ''),
    }),
    new NodePolyfillPlugin({
      excludeAliases: ['console'],
    }),
    new HtmlWebpackPlugin({
      template: path.resolve(__dirname, 'web/index.html'),
    }),
  ],
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist'),
    },
    compress: true,
    port: 8080,
    historyApiFallback: true,
    hot: true,
    experiments: {
      asyncWebAssembly: true,
      syncWebAssembly: true,
      topLevelAwait: true,
    },
    stats: {
      errorDetails: true,
      children: true,
    },
    devtool: 'source-map',
    infrastructureLogging: {
      level: 'error',
    },
  },
};
