{"compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "esnext", "allowJs": true, "resolveJsonModule": true, "moduleResolution": "bundler", "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "module": "esnext", "lib": ["esnext", "dom"], "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "incremental": true, "declaration": false, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitThis": true, "exactOptionalPropertyTypes": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", ".expo", "dist", "web-build", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "extends": "expo/tsconfig.base"}