<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#f7931e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#00d4aa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with gradient border -->
  <rect width="120" height="120" rx="16" fill="url(#gradient)"/>
  <rect x="4" y="4" width="112" height="112" rx="12" fill="#000000"/>
  
  <!-- <PERSON> piece -->
  <g transform="translate(20, 15)">
    <!-- <PERSON> head outline -->
    <path d="M15 25 C15 20, 20 15, 25 15 C30 12, 35 12, 40 15 C45 15, 50 18, 52 22 C54 25, 54 28, 52 32 C50 35, 45 38, 40 40 L40 50 L15 50 Z" 
          fill="none" stroke="white" stroke-width="3" stroke-linejoin="round"/>
    
    <!-- Knight mane -->
    <path d="M15 25 C12 22, 10 20, 8 18 C6 16, 5 14, 6 12 C7 10, 10 9, 13 10 C16 11, 18 13, 20 16" 
          fill="none" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- Knight eye -->
    <circle cx="35" cy="25" r="2" fill="white"/>
    
    <!-- Knight nostril -->
    <path d="M42 28 C43 29, 43 30, 42 31" fill="none" stroke="white" stroke-width="2" stroke-linecap="round"/>
    
    <!-- Base/Crown -->
    <rect x="15" y="50" width="25" height="15" fill="white"/>
    
    <!-- Crown points -->
    <polygon points="15,50 20,45 25,50 30,45 35,50 40,45 40,50" fill="white"/>
  </g>
</svg>
