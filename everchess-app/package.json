{"name": "everchess-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "webpack serve --config webpack.config.js --mode development", "build:web": "webpack --config webpack.config.js --mode production", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src ./App.tsx", "format": "prettier --write ./src ./App.tsx", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.1.0", "@expo-google-fonts/sora": "^0.4.1", "@expo/metro-runtime": "5.0.4", "@getpara/react-native-wallet": "^1.18.0", "@getpara/react-sdk": "^1.18.0", "@getpara/solana-web3.js-v1-integration": "^1.18.0", "@getpara/web-sdk": "^1.18.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@react-navigation/stack": "^7.4.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.8.2", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/web3.js": "^1.98.2", "@tanstack/react-query": "^5.80.7", "expo": "~53.0.11", "expo-constants": "~17.1.6", "expo-crypto": "^14.1.5", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-gl": "~15.1.6", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "node-forge": "^1.3.1", "node-libs-react-native": "^1.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "^2.26.0", "react-native-get-random-values": "^1.11.0", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-keychain": "^10.0.0", "react-native-modpow": "^1.1.0", "react-native-passkey": "^3.1.0", "react-native-quick-base64": "^2.2.0", "react-native-quick-crypto": "^0.7.14", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-webview": "13.13.5", "react-native-webview-crypto": "^0.0.26", "react-redux": "^9.2.0", "text-encoding": "^0.7.0", "three": "^0.177.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.27.6", "@expo/webpack-config": "^19.0.1", "@gorhom/bottom-sheet": "^5.1.6", "@tsconfig/react-native": "^3.0.6", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "assert": "^2.1.0", "babel-loader": "^9.2.1", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-expo": "^13.2.1", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^6.11.0", "dotenv": "^16.5.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "https-browserify": "^1.0.0", "jest": "~29.7.0", "metro-react-native-babel-preset": "^0.77.0", "node-polyfill-webpack-plugin": "^4.1.0", "os-browserify": "^0.3.0", "patch-package": "^8.0.0", "path-browserify": "^1.0.1", "prettier": "^3.5.3", "process": "^0.11.10", "querystring-es3": "^0.2.1", "react-native-reanimated": "^3.18.0", "react-native-svg-transformer": "^1.5.1", "react-native-svg-web": "^1.0.9", "react-native-web": "^0.20.0", "readable-stream": "^4.7.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^3.3.4", "ts-jest": "^29.4.0", "typescript": "~5.8.3", "url": "^0.11.4", "url-loader": "^4.1.1", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "webpack-pwa-manifest": "^4.3.0", "workbox-webpack-plugin": "^7.3.0"}, "private": true, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-inappbrowser-reborn", "@getpara/react-native-wallet", "@getpara/react-sdk", "@getpara/solana-web3.js-v1-integration", "@getpara/web-sdk", "@react-three/drei", "@react-three/fiber", "@solana/wallet-adapter-base", "@solana/wallet-adapter-react", "@solana/web3.js", "node-forge", "node-libs-react-native", "react-native-modpow", "react-native-webview-crypto", "readable-stream", "text-encoding", "three"]}}}}