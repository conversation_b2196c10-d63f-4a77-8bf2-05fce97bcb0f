const { withProjectBuildGradle } = require('@expo/config-plugins');

const withAndroidResolutionStrategy = (config) => {
  return withProjectBuildGradle(config, (config) => {
    const buildGradle = config.modResults.contents;

    const strategy = `
// Force a stable version of androidx.browser to resolve build conflict
allprojects {
    configurations.all {
        resolutionStrategy {
            force 'androidx.browser:browser:1.8.0'
        }
    }
}
    `;

    // Add the new allprojects block to the end of the file.
    // This is safe because <PERSON><PERSON><PERSON> merges allprojects blocks.
    if (!buildGradle.includes("force 'androidx.browser:browser:1.8.0'")) {
        config.modResults.contents = buildGradle + strategy;
    }

    return config;
  });
};

module.exports = withAndroidResolutionStrategy;
