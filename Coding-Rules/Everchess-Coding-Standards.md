# Everchess Coding Standards & Rules

## Overview

This document establishes the coding standards, architectural principles, and development guidelines for the Everchess cross-platform chess application. These rules ensure consistency, maintainability, and scalability across the entire codebase.

## Project Structure

### Directory Organization

```
everchess/
├── app/                          # React Native + Expo application
│   ├── src/
│   │   ├── components/           # Reusable UI components
│   │   │   ├── ui/              # Base UI components (buttons, inputs, etc.)
│   │   │   ├── game/            # Chess-specific components
│   │   │   ├── auth/            # Authentication components
│   │   │   └── common/          # Shared components
│   │   ├── screens/             # Screen components
│   │   │   ├── auth/            # Authentication screens
│   │   │   ├── game/            # Game-related screens
│   │   │   ├── profile/         # User profile screens
│   │   │   └── social/          # Social features screens
│   │   ├── navigation/          # Navigation configuration
│   │   ├── services/            # API and external service integrations
│   │   │   ├── api/             # REST API services
│   │   │   ├── websocket/       # Real-time communication
│   │   │   ├── auth/            # Para Wallet SDK integration
│   │   │   └── blockchain/      # Solana blockchain services
│   │   ├── hooks/               # Custom React hooks
│   │   ├── contexts/            # React Context providers
│   │   ├── store/               # Redux Toolkit store configuration
│   │   ├── utils/               # Utility functions
│   │   ├── constants/           # Application constants
│   │   ├── types/               # TypeScript type definitions
│   │   └── assets/              # Static assets
│   ├── assets/                  # Expo assets (images, fonts, etc.)
│   └── web/                     # PWA-specific files
├── server/                      # Node.js backend
│   ├── src/
│   │   ├── controllers/         # Route handlers
│   │   ├── services/            # Business logic
│   │   ├── models/              # Database models
│   │   ├── middleware/          # Express middleware
│   │   ├── routes/              # API route definitions
│   │   ├── utils/               # Server utilities
│   │   ├── config/              # Configuration files
│   │   └── types/               # TypeScript types
│   └── tests/                   # Server tests
├── shared/                      # Shared types and utilities
│   ├── types/                   # Shared TypeScript types
│   └── constants/               # Shared constants
└── docs/                        # Documentation
    ├── api/                     # API documentation
    ├── architecture/            # System architecture docs
    └── deployment/              # Deployment guides
```

## File Naming Conventions

### Components and Screens
- **PascalCase** for React components: `ChessBoard.tsx`, `UserProfile.tsx`
- **PascalCase** for screen components: `GameScreen.tsx`, `ProfileScreen.tsx`
- **PascalCase** for context providers: `AuthContext.tsx`, `GameContext.tsx`

### Services and Utilities
- **camelCase** for service files: `authService.ts`, `gameService.ts`
- **camelCase** for utility files: `dateUtils.ts`, `validationUtils.ts`
- **camelCase** for hook files: `useAuth.ts`, `useGame.ts`

### Assets and Static Files
- **kebab-case** for asset files: `chess-piece-king.png`, `background-texture.jpg`
- **kebab-case** for 3D model files: `knight-piece.glb`, `wooden-board.glb`

### Configuration Files
- **kebab-case** for config files: `eslint.config.js`, `babel.config.js`

## Code Organization Standards

### File Size Guidelines

**Standard Components (UI, Utilities)**: 200-400 lines
- Simple UI components, utility functions, basic hooks
- Focus on single responsibility principle

**Complex Components**: 400-700 lines
- 3D rendering components, complex game logic, state management
- May include multiple related functions but maintain cohesion

**Service Files**: 300-600 lines
- API services, authentication services, blockchain integrations
- Group related functionality while maintaining modularity

**Absolute Maximum**: 800 lines
- Requires documentation explaining why splitting would reduce clarity
- Must be approved during code review process

### Component Structure

```typescript
// 1. Imports (external libraries first, then internal)
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAuth } from '../hooks/useAuth';
import { Button } from '../components/ui/Button';

// 2. Type definitions
interface ComponentProps {
  title: string;
  onPress: () => void;
}

// 3. Component implementation
export const ComponentName: React.FC<ComponentProps> = ({ title, onPress }) => {
  // 4. State and hooks
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  // 5. Effects
  useEffect(() => {
    // Effect logic
  }, []);

  // 6. Event handlers
  const handlePress = () => {
    setIsLoading(true);
    onPress();
  };

  // 7. Render
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <Button onPress={handlePress} loading={isLoading} />
    </View>
  );
};

// 8. Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});
```

## TypeScript Standards

### Type Definitions

```typescript
// Use interfaces for object shapes
interface User {
  id: string;
  username: string;
  email: string;
  rating: number;
}

// Use types for unions and computed types
type GameStatus = 'waiting' | 'active' | 'completed' | 'abandoned';
type UserWithStats = User & {
  gamesPlayed: number;
  winRate: number;
};

// Use enums for constants
enum ChessPiece {
  PAWN = 'pawn',
  ROOK = 'rook',
  KNIGHT = 'knight',
  BISHOP = 'bishop',
  QUEEN = 'queen',
  KING = 'king',
}
```

### Generic Types

```typescript
// API response wrapper
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Service method signatures
interface GameService {
  createGame<T extends GameMode>(mode: T): Promise<ApiResponse<Game>>;
  getGameHistory(userId: string): Promise<ApiResponse<Game[]>>;
}
```

## Authentication Integration (Para Wallet SDK)

### Para Wallet SDK Implementation

```typescript
// services/auth/paraWalletService.ts
import { ParaWallet, ParaWalletConfig } from '@para-wallet/react-native-sdk';

const paraConfig: ParaWalletConfig = {
  projectId: process.env.EXPO_PUBLIC_PARA_PROJECT_ID!,
  appName: 'Everchess',
  appDescription: 'Ancient Made Modern',
  chains: ['solana:mainnet', 'solana:devnet'],
  auth: {
    email: true,
    google: true,
    apple: Platform.OS === 'ios',
    discord: true,
    twitter: false,
  },
  theme: {
    colorMode: 'dark',
    primaryColor: '#5856D6',
  },
};

export class ParaWalletService {
  private paraWallet: ParaWallet;

  constructor() {
    this.paraWallet = new ParaWallet(paraConfig);
  }

  async authenticate(): Promise<AuthResult> {
    // Implementation
  }

  async logout(): Promise<void> {
    // Implementation
  }

  getUser(): User | null {
    // Implementation
  }
}
```

## Security Standards

### Authentication Security
- Always validate JWT tokens on the server side
- Implement token refresh mechanisms
- Use secure storage for sensitive data (Expo SecureStore)
- Never expose private keys or sensitive credentials in client code

### API Security
- Implement rate limiting on all endpoints
- Use HTTPS for all communications
- Validate and sanitize all input data
- Implement proper CORS policies

### Database Security
- Use Row Level Security (RLS) policies in Supabase
- Implement proper indexing for performance
- Use parameterized queries to prevent SQL injection
- Encrypt sensitive data at rest

### Web3 Security
- Validate all blockchain transactions server-side
- Implement proper wallet signature verification
- Use secure random number generation for nonces
- Implement transaction replay protection

## Performance Standards

### React Native Performance
- Use `React.memo` for expensive components
- Implement proper key props for lists
- Use `useMemo` and `useCallback` appropriately
- Optimize image loading and caching

### 3D Rendering Performance
- Limit polygon count for mobile devices (<5000 per chess set)
- Use texture compression (WebP format)
- Implement Level of Detail (LOD) for distant objects
- Use object pooling for frequently created/destroyed objects

### API Performance
- Implement proper caching strategies
- Use pagination for large data sets
- Optimize database queries with proper indexing
- Implement request debouncing for user inputs

## Testing Standards

### Unit Testing
- Minimum 80% code coverage for critical components
- Test all public methods and edge cases
- Use Jest and React Testing Library
- Mock external dependencies

### Integration Testing
- Test API endpoints with real database
- Test WebSocket connections and events
- Test authentication flows
- Test blockchain interactions on testnet

### End-to-End Testing
- Test critical user flows (signup, game play, purchases)
- Test cross-platform compatibility
- Test performance under load
- Test offline functionality

## Documentation Standards

### Code Documentation
- Use JSDoc comments for all public functions
- Include parameter types and return types
- Provide usage examples for complex functions
- Document any side effects or dependencies

### API Documentation
- Use OpenAPI/Swagger for REST API documentation
- Document all endpoints, parameters, and responses
- Include example requests and responses
- Document error codes and handling

### Architecture Documentation
- Maintain up-to-date system architecture diagrams
- Document data flow between components
- Explain design decisions and trade-offs
- Include deployment and scaling considerations

## Error Handling

### Client-Side Error Handling
```typescript
// Custom error classes
export class ApiError extends Error {
  constructor(
    public status: number,
    message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

// Error boundary implementation
export class ErrorBoundary extends React.Component {
  // Implementation with proper error reporting
}
```

### Server-Side Error Handling
```typescript
// Centralized error handling middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log error
  logger.error(err);
  
  // Send appropriate response
  if (err instanceof ValidationError) {
    res.status(400).json({ error: 'Invalid input data' });
  } else if (err instanceof AuthenticationError) {
    res.status(401).json({ error: 'Authentication required' });
  } else {
    res.status(500).json({ error: 'Internal server error' });
  }
};
```

## Git Workflow

### Branch Naming
- `feature/description` for new features
- `bugfix/description` for bug fixes
- `hotfix/description` for critical fixes
- `refactor/description` for code refactoring

### Commit Messages
```
type(scope): description

feat(auth): implement Para Wallet SDK integration
fix(game): resolve chess piece movement validation
docs(api): update authentication endpoint documentation
refactor(ui): optimize component rendering performance
```

### Pull Request Process
1. Create feature branch from `develop`
2. Implement changes with tests
3. Update documentation
4. Create pull request with detailed description
5. Code review and approval required
6. Merge to `develop` branch
7. Deploy to staging for testing
8. Merge to `main` for production deployment

## Code Review Checklist

### Functionality
- [ ] Code meets requirements and specifications
- [ ] All edge cases are handled appropriately
- [ ] Error handling is implemented correctly
- [ ] Performance considerations are addressed

### Code Quality
- [ ] Code follows established patterns and conventions
- [ ] Functions and classes have single responsibilities
- [ ] Code is readable and well-documented
- [ ] No code duplication or unnecessary complexity

### Security
- [ ] Input validation is implemented
- [ ] Authentication and authorization are correct
- [ ] Sensitive data is handled securely
- [ ] No security vulnerabilities introduced

### Testing
- [ ] Unit tests cover new functionality
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance impact assessed

This document serves as the foundation for all development work on the Everchess platform. All team members must familiarize themselves with these standards and follow them consistently throughout the development process.
