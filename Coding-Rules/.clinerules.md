# Everchess v1 - Coding Rules for Everchess on Cline

> **IMPORTANT**: This is the primary rules file for Windsurf to build the Everchess platform. It consolidates all requirements and guidelines from the original reference files into a single comprehensive document organized by topic.

You're an amazing expert genius senior software engineer dev on the following Tech Stack for the Everchess Platform: React Native, Expo SDK 49+, React Three Fiber, Node.js, Express 4.x, Socket.IO, Supabase, Para Wallet SDK (unified Web2/Web3 authentication), Solana, AWS (EC2, S3, CloudFront), Sentry, and AWS CloudWatch.

## Project Overview
- **Type:** Next-Gen Immersive Chess Platform
- **Description:** Everchess is a next-generation chess platform combining traditional gameplay with modern gaming features, social interactions, Web3 capabilities, and immersive 3D experiences. It serves as a Progressive Web App (PWA) and native mobile app, integrating NFT chess sets, staking, player progression, and competitive gameplay.
- **Primary Goal:** Build a secure, scalable, and engaging chess platform that seamlessly combines Web2 and Web3 technologies to create a unique player experience across devices.

## Core Technical Architecture

### Tech Stack Requirements
- **Frontend**:
  - **Primary**: React Native + Expo SDK 49+ (for cross-platform iOS, Android, and PWA development)
  - **3D Rendering**: React Three Fiber (for immersive chess rendering and animations)
  - **State Management**: Redux Toolkit + React Query for global state and server state caching
  - **Navigation**: React Navigation 6 with type-safe navigation and deep linking
  - **Styling**: StyleSheet API with responsive design and React Native Reanimated 3 for animations
  - **Storage**: Expo SecureStore for sensitive data, AsyncStorage for app preferences

- **Backend**:
  - **Server**: Node.js + Express 4.x (for API endpoints and game logic)
  - **Real-time**: Socket.IO (for game state updates and chat)
  - **Database**: Supabase (PostgreSQL) with real-time subscriptions
  - **Authentication**: Combination of traditional auth and Web3 wallet connections

- **Web3 Integration**:
  - **Blockchain**: Solana (for fast, low-cost NFT transactions)
  - **Authentication**: Para Wallet SDK for unified Web2/Web3 authentication (email, social, wallet)
  - **Wallet Integration**: React Native-compatible Solana wallet connections

- **DevOps & Monitoring**:
  - **Hosting**: AWS (EC2, S3, CloudFront)
  - **Error Tracking**: Sentry
  - **Performance Monitoring**: AWS CloudWatch

### Project Structure Rules
- **Directory Organization**:
  - `/app`: React Native application code
    - `/screens`: Screen components organized by feature
    - `/components`: Reusable UI components
    - `/hooks`: Custom React hooks
    - `/utils`: Utility functions and helpers
    - `/constants`: App-wide constants and configuration
    - `/navigation`: Navigation-related configuration
    - `/services`: API clients and service integrations
    - `/assets`: Static assets (images, fonts, etc.)
  
  - `/server`: Backend application code
    - `/routes`: Express route definitions
    - `/controllers`: Business logic handlers
    - `/models`: Data models and schemas
    - `/middleware`: Express middleware
    - `/socket`: Socket.IO event handlers
    - `/utils`: Server utility functions
    - `/config`: Server configuration

  - `/assets`: 3D models and NFT assets
    - `/models`: 3D chess pieces and boards
    - `/textures`: Textures for 3D models
    - `/thumbnails`: Preview images for NFTs
    - `/animations`: Animation files for in-game effects

- **File Naming Conventions**:
  - Use PascalCase for component files: `ChessBoard.tsx`
  - Use camelCase for utility/helper files: `authHelpers.ts`
  - Use kebab-case for assets: `knight-piece.glb`
  - Include type in file name for context: `UserContext.tsx`, `AuthTypes.ts`

## Coding Standards & Best Practices

### General Code Style
- Use TypeScript for all new code to ensure type safety
- Follow ESLint and Prettier configurations for consistent formatting
- Maintain 2-space indentation across all files
- Keep files under 300-400 lines (split larger files into logical modules)
- Use meaningful, descriptive variable and function names
- Include JSDoc comments for public functions and components
- Maintain a clean git history with semantic commit messages

### Language-Specific Guidelines

#### JavaScript/TypeScript
- Use ES6+ features (arrow functions, destructuring, template literals)
- Prefer async/await over callbacks or raw promises
- Utilize TypeScript interfaces for prop definitions and API responses
- Avoid any type when possible, use proper typing
- Use type guards for runtime type checking when necessary
- Implement dependency injection for better testability
- Manage side effects in dedicated locations (useEffect, thunks, etc.)

#### React Native/React
- Use functional components with hooks
- Create custom hooks to encapsulate and reuse complex logic
- Implement proper error boundaries for fault tolerance
- Optimize re-renders with useMemo, useCallback, and React.memo
- Use the Platform API for platform-specific implementations
- Implement accessibility features (proper labels, focus management)
- Apply responsive design principles for cross-device compatibility

#### SQL (Supabase/PostgreSQL)
- Use parameterized queries to prevent SQL injection
- Apply proper indexing strategies for frequently queried columns
- Implement Row Level Security (RLS) policies for all tables
- Use PostgreSQL features like JSONB for flexible data structures
- Follow consistent naming conventions (snake_case for tables/columns)
- Implement database transactions for related operations
- Use migrations for all schema changes

#### Express.js
- Organize routes using Express.Router for modularity
- Implement proper middleware order (parsers, auth, routes, error handlers)
- Use async/await with try/catch blocks for route handlers
- Create a centralized error handling middleware
- Apply request validation for all endpoints
- Implement proper logging for requests and errors
- Use appropriate HTTP status codes and consistent response formats

### 3D Rendering Rules (Three Fiber)
- Optimize 3D assets for performance (reduce polygons, compress textures)
- Implement Level of Detail (LOD) techniques for different device capabilities
- Use lazy loading for non-critical assets
- Minimize draw calls and optimize shaders
- Implement proper memory management to prevent leaks
- Provide fallback rendering for lower-end devices
- Ensure smooth animations (target 60fps on modern devices)

## Security Guidelines

### Web2 Security Requirements
- Enforce HTTPS for all API calls with proper TLS configuration
- Implement JWT authentication with short-lived access tokens
- Use secure cookies with HttpOnly, Secure, and SameSite attributes
- Apply rate limiting for authentication and sensitive endpoints
- Validate and sanitize all user inputs (server and client side)
- Implement Content Security Policy (CSP) headers
- Use Helmet middleware for secure HTTP headers
- Regularly update dependencies and scan for vulnerabilities
- Implement appropriate CORS policies for API access

### Web3 Security Requirements
- Use Para Wallet SDK for secure, unified Web2/Web3 authentication
- Require transaction signing for all blockchain actions through Para Wallet
- Implement proper error handling for blockchain operations with React Native compatibility
- Follow Solana best practices for NFT implementation and management
- Implement retry logic with exponential backoff for blockchain transactions
- Encrypt sensitive off-chain data before storage using Expo SecureStore
- Provide clear transaction information to users before signing
- Never store private keys or sensitive wallet data in the React Native app
- Use Para Wallet's secure authentication flow for all Web3 interactions

### Data Protection
- Encrypt sensitive data at rest and in transit
- Implement proper access control for user data
- Use parameterized queries for all database operations
- Apply database connection pooling and timeout handling
- Implement secure password policies and storage
- Use multi-factor authentication for sensitive operations
- Create regular database backups with secure storage

## Error Handling and Logging

### Global Error Strategy
- Implement a hierarchical error handling system that categorizes errors by severity and type
- Create custom error classes for domain-specific errors (GameLogicError, AuthError, BlockchainError)
- Use centralized error middleware in Express to handle uncaught exceptions
- Implement graceful degradation for non-critical feature failures
- Provide user-friendly error messages while logging detailed error information

### Frontend Error Handling
- Implement React error boundaries around critical components
- Create fallback UI states for component failures
- Handle network errors with clear user feedback and retry options
- Manage async errors with try/catch patterns in useEffect and event handlers
- Implement global error state for app-wide error notifications

### Backend Error Handling
- Use try/catch with async/await for all asynchronous operations
- Implement proper logging levels (debug, info, warn, error)
- Structure error responses consistently (status, code, message, details)
- Log errors with transaction IDs for request tracing
- Implement circuit breakers for external service calls

### Blockchain Error Handling
- Create specific handling for "incomplete envelope: unexpected EOF" Solana errors
- Implement exponential backoff for retry logic
- Handle network interruptions gracefully with informative user messages
- Monitor wallet connection status and reconnect when needed
- Implement transaction confirmation with proper timeout handling

### Logging Strategy
- Use structured logging (JSON format) for machine parsing
- Include contextual information (user ID, request ID, component)
- Implement log rotation and retention policies
- Use Sentry for error aggregation and real-time alerting
- Create different logging levels for development and production

## Performance Optimization

### Frontend Performance
- Implement code splitting and lazy loading for non-critical components
- Optimize bundle size through tree shaking and dependency management
- Use image optimization techniques (WebP format, appropriate sizing)
- Implement efficient list rendering with FlatList/virtualization
- Cache API responses for frequently accessed data
- Optimize font loading and display
- Implement skeleton screens for loading states

### Backend Performance
- Use connection pooling for database connections
- Implement query optimization and proper indexing
- Apply caching strategies for frequently accessed data
- Optimize Socket.IO connections with appropriate room management
- Implement horizontal scaling for stateless services
- Use message queues for asynchronous processing
- Monitor and optimize API response times
- Implement database query timeout handling
- Use streaming responses for large data sets
- Apply database-level optimization (proper indexing, query planning)
- Implement server-side pagination for all list endpoints
- Use batching for multiple related API calls
- Optimize WebSocket frame size and message frequency
- Implement proper connection handling for mobile network transitions

### 3D Performance
- Compress 3D assets (textures, models) appropriately
- Implement asset preloading and caching strategies
- Optimize lighting and material complexity
- Use instancing for repeated objects
- Implement frustum culling and occlusion techniques
- Adjust rendering quality based on device capabilities
- Monitor and maintain consistent frame rates
- Use texture atlasing for related materials
- Implement progressive loading for complex 3D scenes
- Apply geometry simplification for distant objects
- Optimize shader complexity for mobile GPUs
- Use baked lighting where possible to reduce real-time calculations
- Implement object pooling for frequently created/destroyed elements
- Apply WebGL best practices for memory management

## Real-time Communication Guidelines

### Socket.IO Implementation
- Organize Socket.IO events by feature domain (game, chat, social)
- Implement room-based communications for scalability
- Use namespaces to separate concerns (gameplay vs. chat)
- Implement proper error handling for socket connections
- Apply rate limiting to prevent spam and DoS attacks
- Add reconnection logic with exponential backoff
- Use binary transmission for efficiency when appropriate
- Implement proper authentication and authorization for socket connections
- Monitor socket connection health and performance

### Game State Synchronization
- Use reliable ordered messaging for critical game state updates
- Implement optimistic updates with rollback for better UX
- Create a conflict resolution strategy for state divergence
- Apply delta updates to minimize bandwidth usage
- Use server authority for game logic validation
- Implement proper timeout handling for disconnected players
- Create a state recovery mechanism for reconnected clients

### Chat System
- Implement moderation systems for chat content
- Apply message rate limiting to prevent spam
- Create separate channels for different contexts (game, global, private)
- Support rich content in messages where appropriate
- Implement proper message storage and retrieval
- Create offline message delivery for disconnected users
- Add typing indicators for better user experience

## Database Design Guidelines

### Schema Design
- Create normalized database schemas to reduce redundancy
- Use appropriate data types for columns (e.g., UUID, timestamp with time zone)
- Implement referential integrity with foreign key constraints
- Apply proper indexing strategies for query patterns
- Use materialized views for complex aggregate queries
- Implement JSONB for semi-structured data where appropriate
- Design with future scalability in mind (sharding considerations)

### Supabase-Specific Guidelines
- Leverage Supabase's real-time capabilities for live updates
- Implement row-level security (RLS) policies for all tables
- Use Supabase functions for complex database operations
- Apply Postgres extensions as needed (e.g., pgvector for similarity)
- Implement proper user management with Supabase Auth
- Use Supabase storage for file management with proper policies
- Create database functions for complex operations

## CSS and Styling Guidelines

### Styling Architecture
- Use StyleSheet.create() for all React Native styles
- Organize styles by component with consistent naming
- Create theme variables for colors, spacing, typography
- Implement responsive design using flexbox and dynamic units
- Create a unified design system with shared components
- Support both light and dark mode with theme toggling
- Implement platform-specific styling where necessary

### Animation Standards
- Use React Native's Animated API for performance
- Create consistent animation durations and easing functions
- Implement skeleton screens for loading states
- Optimize animations for battery efficiency on mobile
- Use hardware acceleration where appropriate
- Implement gesture-based interactions with proper feedback
- Create accessible animations with reduced motion support

## Feature Implementation Guidelines

### Chess Gameplay
- Implement standard chess rules with proper move validation
- Support multiple time controls (Bullet, Blitz, Rapid, Classical)
- Create various game modes (Casual, Ranked, Wagers, Tournaments)
- Implement a robust anti-cheating system
- Support game history and replay functionality
- Provide move analysis and learning tools
- Ensure gameplay works consistently online and offline

### NFT Chess Sets
- Follow the specified metadata structure for chess sets
- Organize assets by theme and ID in AWS S3
- Implement asset loading with proper fallback mechanisms
- Support previewing and showcasing collectible sets
- Ensure proper rendering across different device capabilities
- Implement marketplace functionality for trading sets
- Support staking mechanics for sets

### Social Features
- Create a robust friend system with relationship management
- Implement real-time chat with appropriate moderation tools
- Develop leaderboards with various ranking metrics
- Support club/team creation and management
- Implement tournament creation and participation
- Create spectator mode for watching games
- Support social sharing of games and achievements

### Player Progression
- Develop an XP/leveling system with meaningful rewards
- Implement daily and weekly missions with varied objectives
- Create a battlepass system with free and premium tiers
- Track player statistics and history
- Implement achievements and badges
- Create seasonal events and competitions
- Support player profiles with customization options

## Testing Requirements

### Unit Testing
- Write unit tests for critical game logic and utilities
- Maintain >80% test coverage for core business logic
- Use Jest for JavaScript/TypeScript testing
- Mock external services and APIs in tests
- Test edge cases and error scenarios thoroughly
- Implement continuous integration with automated testing

### Integration Testing
- Test API endpoints with simulated clients
- Verify database interactions and data integrity
- Test real-time communication with Socket.IO
- Validate authentication flows and security measures
- Test cross-component interactions
- Verify Web3 integrations with test networks

### UI and UX Testing
- Test responsive design across different screen sizes
- Verify accessibility compliance (WCAG 2.1 AA)
- Test touch interactions for mobile devices
- Validate proper loading states and error handling
- Test offline functionality and state recovery
- Verify visual consistency across platforms
- Conduct usability testing with representative users

## Deployment and DevOps

### Deployment Strategy
- Implement continuous integration/deployment pipeline
- Use environment-specific configurations
- Create staging and production environments
- Implement blue-green deployment for zero-downtime updates
- Configure proper logging and monitoring
- Implement automated backup strategies
- Create disaster recovery plans

### AWS Configuration
- Configure EC2 instances with auto-scaling
- Set up S3 buckets with appropriate permissions and CORS
- Configure CloudFront for global content distribution
- Implement proper IAM roles and access controls
- Set up CloudWatch for monitoring and alerts
- Configure proper network security groups
- Implement database backup strategies

## Scaling Strategy

### Technical Scaling
- Design for horizontal scaling of stateless services
- Implement database sharding for future growth
- Use caching layers (Redis) for frequently accessed data
- Optimize real-time communication for large player bases
- Implement proper load balancing strategies
- Design efficient data models for high-volume operations
- Plan for geographic distribution as user base grows

### Feature Scaling
- Design modular systems that can be extended
- Create feature flags for gradual rollouts
- Implement analytics to track feature usage
- Design APIs with versioning to support evolution
- Create documentation for future development
- Plan for internationalization and localization
- Establish community feedback mechanisms

## Para Wallet SDK & Solana Integration Guidelines

- Use Para Wallet SDK for unified Web2/Web3 authentication in React Native
- Implement `@solana/web3.js` for blockchain interactions through Para Wallet
- Create robust error handling for RPC calls with React Native compatibility
- Use mock implementations for development and testing
- Implement exponential backoff for failed transactions
- Optimize transaction fees for Solana network efficiency
- Provide clear transaction feedback to users through React Native UI
- Follow Solana best practices for NFT creation and management
- Integrate Para Wallet's authentication flow:
  - Email and social login for Web2 users
  - Wallet connection for Web3 users
  - Unified user experience across authentication methods
- Use Expo SecureStore for secure token and key management

## NFT Metadata Structure

```json
{
  "name": "Everchess [Theme] Set #[Number]",
  "symbol": "ECHESS",
  "description": "Limited edition [Theme]-themed 3D chess set for Everchess",
  "image": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/thumbnail.png",
  "animation_url": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/preview.glb",
  "attributes": [
    {"trait_type": "Theme", "value": "[Theme]"},
    {"trait_type": "Rarity", "value": "[Rarity]"},
    {"trait_type": "Edition", "value": "[Number]/[Total]"}
  ],
  "properties": {
    "files": [
      {
        "type": "image/png",
        "uri": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/thumbnail.png"
      },
      {
        "type": "model/gltf-binary",
        "uri": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/preview.glb"
      }
    ],
    "pieces": {
      "player": {
        "pawn": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-pawn.glb",
        "rook": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-rook.glb",
        "knight": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-knight.glb",
        "bishop": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-bishop.glb",
        "queen": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-queen.glb",
        "king": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/player-king.glb"
      },
      "opponent": {
        "pawn": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-pawn.glb",
        "rook": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-rook.glb",
        "knight": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-knight.glb",
        "bishop": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-bishop.glb",
        "queen": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-queen.glb",
        "king": "https://everchess-assets.s3.amazonaws.com/nfts/[theme]-[id]/opponent-king.glb"
      }
    },
    "theme": {
      "name": "[Theme]",
      "description": "[Theme Description]",
      "designer": "[Designer Name]",
      "materials": ["[Material 1]", "[Material 2]", "[Material 3]"]
    }
  }
}
```

## Security Checklist

### Critical Security Priorities

- **Wallet Connection Security**
  - Domain whitelisting for DApp connections
  - Transaction simulation before signing
  - Visual contract verification
  
- **USDC Transaction Security**
  - Transfer confirmations with dual approval for large amounts
  - Session hardening with timeouts
  - Secondary channel verification
  
- **Fair Play Enforcement**
  - Real-time move validation
  - Behavioral analysis for anti-cheating
  - Secure wager match protections
  
- **Account Security**
  - Multi-factor authentication for sensitive operations
  - Session security with device fingerprinting
  - Anomaly detection for suspicious activities

### General Security Requirements

- Store secrets in environment variables, never in code
- Implement proper authentication and authorization
- Encrypt data at rest and in transit
- Apply proper API security (rate limiting, timeouts)
- Implement security monitoring and logging
- Regularly scan dependencies for vulnerabilities
- Conduct security testing (SAST, DAST)
- Document security controls and procedures

## UI/UX Guidelines

- Follow a consistent design system across the application
- Implement responsive design for various screen sizes
- Create intuitive navigation patterns
- Provide clear feedback for user actions
- Implement proper loading states and transitions
- Ensure accessibility compliance (WCAG 2.1 AA)
- Optimize for touch interactions on mobile
- Create smooth animations for enhanced experience
- Support light and dark themes
- Implement proper form validation and error handling
- Design for both novice and experienced chess players
- Create consistent layout and typography across screens
- Implement haptic feedback for important interactions on mobile
- Design with international audiences in mind (i18n support)
- Create visual hierarchy to guide user attention
- Apply consistent spacing and alignment principles
- Implement proper focus states for keyboard navigation
- Use appropriate contrast ratios for text readability
- Design with offline states in mind for intermittent connectivity

## Documentation Requirements

### Official Documentation References

All development must follow patterns and best practices from official documentation. Reference these resources when implementing features:

**Core Technology Stack:**
- **React Native**: [https://reactnative.dev/docs/getting-started](https://reactnative.dev/docs/getting-started)
- **Expo**: [https://docs.expo.dev/](https://docs.expo.dev/)
- **React Three Fiber**: [https://r3f.docs.pmnd.rs/](https://r3f.docs.pmnd.rs/)
- **Redux Toolkit**: [https://redux-toolkit.js.org/introduction/getting-started](https://redux-toolkit.js.org/introduction/getting-started)
- **Socket.IO Client**: [https://socket.io/docs/v4/client-api/](https://socket.io/docs/v4/client-api/)
- **React Navigation**: [https://reactnavigation.org/docs/getting-started](https://reactnavigation.org/docs/getting-started)
- **Three.js**: [https://threejs.org/docs/](https://threejs.org/docs/)
- **SWR**: [https://swr.vercel.app/docs/getting-started](https://swr.vercel.app/docs/getting-started)

**Backend:**
- **Node.js**: [https://nodejs.org/en/docs/](https://nodejs.org/en/docs/)
- **Express**: [https://expressjs.com/](https://expressjs.com/)
- **Socket.IO Server**: [https://socket.io/docs/v4/server-api/](https://socket.io/docs/v4/server-api/)
- **TypeScript**: [https://www.typescriptlang.org/docs/](https://www.typescriptlang.org/docs/)
- **Zod**: [https://zod.dev/](https://zod.dev/)

**Database & Storage:**
- **Supabase**: [https://supabase.com/docs](https://supabase.com/docs)
- **AWS S3**: [https://docs.aws.amazon.com/s3/](https://docs.aws.amazon.com/s3/)
- **Redis**: [https://redis.io/documentation](https://redis.io/documentation)

**Web3:**
- **Solana Web3.js**: [https://solana-labs.github.io/solana-web3.js/](https://solana-labs.github.io/solana-web3.js/)
- **Para Wallet SDK**: [https://docs.getpara.com/](https://docs.getpara.com/)
- **Solana Documentation**: [https://docs.solana.com/](https://docs.solana.com/)

**DevOps & Deployment:**
- **AWS Services**: [https://docs.aws.amazon.com/](https://docs.aws.amazon.com/)
- **Docker**: [https://docs.docker.com/](https://docs.docker.com/)
- **TestFlight**: [https://developer.apple.com/testflight/](https://developer.apple.com/testflight/)
- **Google Play Console**: [https://developer.android.com/distribute/console](https://developer.android.com/distribute/console)
- **App Store Guidelines**: [https://developer.apple.com/app-store/review/guidelines/](https://developer.apple.com/app-store/review/guidelines/)

### Code Documentation
- Use JSDoc comments for all public functions and components
- Document complex algorithms with explanations and references
- Include parameter and return type documentation
- Add examples for non-trivial function usage
- Maintain up-to-date README files for major components
- Document known limitations and edge cases
- Create architecture diagrams for complex systems

### API Documentation
- Generate OpenAPI/Swagger documentation for all endpoints
- Document request/response formats with examples
- Include error codes and their meanings
- Document rate limits and authentication requirements
- Create integration examples for key endpoints
- Maintain changelog for API versioning

### User Documentation
- Create user guides for complex features
- Implement contextual help within the application
- Design intuitive onboarding flows for new users
- Document NFT and wallet functionality for non-technical users
- Create FAQs for common questions and issues
- Provide troubleshooting guides for known issues

## Platform-Specific Optimizations

### Progressive Web App (PWA)
- Implement service workers for offline functionality
- Create a web manifest for installability
- Optimize first meaningful paint and time to interactive
- Implement IndexedDB for offline data storage
- Apply resource hints (preload, prefetch) for critical assets
- Create responsive designs that adapt to all screen sizes
- Optimize for web vitals metrics (LCP, FID, CLS)

### Mobile App (React Native)
- Optimize app binary size through proper bundling
- Implement deep linking for improved navigation
- Use appropriate native modules for platform integration
- Optimize startup time with proper initialization
- Implement app state handling for background/foreground transitions
- Create platform-specific UI elements where necessary
- Optimize for battery usage and memory consumption
- Support push notifications for engagement

### Input & Interaction Pattern Differences
- Design drag-and-drop interactions for chess piece movement
- Implement touch controls for mobile with appropriate hit areas
- Support mouse and keyboard for desktop PWA users
- Create adaptive UI that responds to input method
- Implement game controller support where applicable
- Design for different screen orientations on mobile
- Provide keyboard shortcuts for power users

## Version Control & Deployment

### Git Workflow
- Use semantic versioning for releases
- Implement feature branches with descriptive names
- Create detailed pull request templates
- Apply conventional commit message format
- Implement pre-commit hooks for linting and formatting
- Use gitignore files to exclude sensitive information
- Maintain a clean commit history with focused changes

### CI/CD Pipeline
- Automate testing for all pull requests
- Implement staging environments for pre-production testing
- Use deployment previews for UI changes
- Apply automated security scanning in the pipeline
- Implement canary deployments for risk mitigation
- Create rollback mechanisms for failed deployments
- Automate dependency updates with security validation