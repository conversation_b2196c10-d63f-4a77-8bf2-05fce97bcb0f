{"buildFiles": ["/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/new-examples-hub/mobile/with-flutter/android/app/.cxx/Debug/265z3k5o/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/GitHub/new-examples-hub/mobile/with-flutter/android/app/.cxx/Debug/265z3k5o/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}