{"expo": {"name": "para-expo", "slug": "para-expo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "newArchEnabled": true, "scheme": "para-expo", "ios": {"supportsTablet": true, "bundleIdentifier": "com.getpara.example", "associatedDomains": ["webcredentials:app.beta.usecapsule.com?mode=developer", "webcredentials:app.usecapsule.com"], "appleTeamId": "FF9U73RS48"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.getpara.example"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}