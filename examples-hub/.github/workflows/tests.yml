name: Tests

on:
  pull_request:
    branches: [main]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Node.js settings
  NODE_OPTIONS: "--max-old-space-size=4096"

  # API keys (placeholders for CI)
  PARA_API_KEY: test-build-key
  VITE_PARA_API_KEY: test-build-key-vite
  NEXT_PUBLIC_PARA_API_KEY: test-build-key-next
  ENCRYPTION_KEY: test-encryption-key
  NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID: test-wc-id

  # Web3/blockchain configs
  REACT_APP_NETWORK: "testnet"
  NEXT_PUBLIC_NETWORK: "testnet"
  VITE_NETWORK: "testnet"
  NEXT_PUBLIC_INFURA_ID: "test-infura-id"
  REACT_APP_INFURA_ID: "test-infura-id"
  VITE_INFURA_ID: "test-infura-id"
  NEXT_PUBLIC_ALCHEMY_KEY: "test-alchemy-key"
  REACT_APP_ALCHEMY_KEY: "test-alchemy-key"
  VITE_ALCHEMY_KEY: "test-alchemy-key"

  # EVM chain settings
  NEXT_PUBLIC_DEFAULT_CHAIN: "sepolia"
  REACT_APP_DEFAULT_CHAIN: "sepolia"
  VITE_DEFAULT_CHAIN: "sepolia"

  # Auth providers
  NEXT_PUBLIC_AUTH_PROVIDERS: "email,wallet"
  VITE_AUTH_PROVIDERS: "email,wallet"
  REACT_APP_AUTH_PROVIDERS: "email,wallet"

  # Solana configs
  NEXT_PUBLIC_SOLANA_NETWORK: "devnet"
  REACT_APP_SOLANA_NETWORK: "devnet"
  VITE_SOLANA_NETWORK: "devnet"
  NEXT_PUBLIC_SOLANA_RPC_URL: "https://api.devnet.solana.com"
  REACT_APP_SOLANA_RPC_URL: "https://api.devnet.solana.com"
  VITE_SOLANA_RPC_URL: "https://api.devnet.solana.com"

  # Cosmos configs
  NEXT_PUBLIC_COSMOS_CHAIN_ID: "theta-testnet-001"
  REACT_APP_COSMOS_CHAIN_ID: "theta-testnet-001"
  VITE_COSMOS_CHAIN_ID: "theta-testnet-001"

  # IPFS/Arweave settings
  NEXT_PUBLIC_IPFS_GATEWAY: "https://ipfs.io/ipfs/"
  REACT_APP_IPFS_GATEWAY: "https://ipfs.io/ipfs/"
  VITE_IPFS_GATEWAY: "https://ipfs.io/ipfs/"

jobs:
  example-hub-e2e-tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout monorepo
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"

      - name: Install packages
        run: yarn install

      - name: Install Playwright Browsers
        run: npx playwright install chromium

      - name: Run Examples Hub E2Es
        shell: bash
        run: xvfb-run yarn e2e-examples-hub-script

      - name: Upload Playwright Report
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 1

  build-web-react-nextjs:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "web/with-react-nextjs"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          PROJECTS=()
          for DIR in "$PROJECT_DIR"/*; do
            if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
              PROJECTS+=("$DIR")
            fi
          done
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              yarn build
              BUILD_EXIT_CODE=$?
              rm -rf node_modules .next dist build out
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-web-react-vite:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "web/with-react-vite"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          PROJECTS=()
          for DIR in "$PROJECT_DIR"/*; do
            if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
              PROJECTS+=("$DIR")
            fi
          done
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              yarn build
              BUILD_EXIT_CODE=$?
              rm -rf node_modules .next dist build out
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-web-vue-vite:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "web/with-vue-vite"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          PROJECTS=()
          for DIR in "$PROJECT_DIR"/*; do
            if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
              PROJECTS+=("$DIR")
            fi
          done
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              yarn build
              BUILD_EXIT_CODE=$?
              rm -rf node_modules .next dist build out
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-web-svelte-vite:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "web/with-svelte-vite"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          PROJECTS=()
          for DIR in "$PROJECT_DIR"/*; do
            if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
              PROJECTS+=("$DIR")
            fi
          done
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              yarn build
              BUILD_EXIT_CODE=$?
              rm -rf node_modules .next dist build out
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-server-node:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "server/with-node"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          PROJECTS=()
          for DIR in "$PROJECT_DIR"/*; do
            if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
              PROJECTS+=("$DIR")
            fi
          done
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              yarn build
              BUILD_EXIT_CODE=$?
              rm -rf node_modules dist build
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi
  build-server-deno:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "server/with-deno"
      NODE_OPTIONS: "--max-old-space-size=4096"
    steps:
      - uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v2
        id: deno-setup
        with:
          deno-version: "2.2.11"
          cache: true

      - name: Debug Deno
        run: |
          echo "Deno version: $(deno --version)"
          echo "Deno binary path: $(which deno)"
          echo "Release channel: ${{ steps.deno-setup.outputs.release-channel }}"
          echo "Installed version: ${{ steps.deno-setup.outputs.deno-version }}"

      - name: Build projects
        timeout-minutes: 15 #
        run: |
          set +e
          # Check if the directory itself has a deno.json file first (for single project)
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/deno.json" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            # Look for multiple projects
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/deno.json" ]; then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              # Create dist directory if it doesn't exist
              mkdir -p dist
              chmod 755 dist
              
              # Create empty .env file if it doesn't exist
              touch .env
              
              # Cache dependencies first - with retries
              echo "Caching dependencies..."
              if [ -f "deno.json" ] && grep -q '"imports":' deno.json; then
                for ATTEMPT in {1..3}; do
                  echo "Dependency cache attempt $ATTEMPT..."
                  deno cache --reload server.ts && break || sleep 5
                done
              fi
              
              if [ -f "deno.json" ] && grep -q '"tasks":' deno.json && grep -q '"build"' deno.json; then
                # Try to build with retries
                for ATTEMPT in {1..3}; do
                  echo "Build attempt $ATTEMPT..."
                  deno task build && break || {
                    echo "Build failed, retrying after clearing cache..."
                    rm -rf "$HOME/.cache/deno/npm" || true
                    sleep 5
                  }
                done
              else
                echo "No build task found, checking main entry point..."
                MAIN_FILE=$(find . -maxdepth 1 -name "*.ts" -o -name "main.ts" -o -name "index.ts" -o -name "server.ts" | head -n 1)
                if [ -n "$MAIN_FILE" ]; then
                  echo "Checking $MAIN_FILE"
                  deno check "$MAIN_FILE"
                else
                  echo "No main file found to check, treating as success"
                fi
              fi
              BUILD_EXIT_CODE=$?
              # Clean up Deno cache if needed
              if [ $BUILD_EXIT_CODE -ne 0 ] && [ -d "$HOME/.cache/deno" ]; then
                echo "Build failed, cleaning up cache..."
                rm -rf "$HOME/.cache/deno/npm"
              fi
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-server-bun:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "server/with-bun"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      - name: Build projects
        run: |
          set +e
          # Check if the directory itself has required files first (for single project)
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/package.json" ] && [ -f "$PROJECT_DIR/bun.lockb" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            # Look for multiple projects
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/package.json" ] && ([ -f "$DIR/bun.lockb" ] || grep -q '"bun"' "$DIR/package.json"); then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              echo "Installing dependencies..."
              bun install
              
              # Run build task if present
              if grep -q '"build"' package.json; then
                bun run build
              else
                echo "No build script found, checking TypeScript files..."
                MAIN_FILE=$(find . -maxdepth 1 -name "*.ts" -o -name "main.ts" -o -name "index.ts" -o -name "server.ts" | head -n 1)
                if [ -n "$MAIN_FILE" ]; then
                  echo "Checking $MAIN_FILE with TypeScript"
                  if [ -f "tsconfig.json" ]; then
                    bunx --bun tsc --noEmit
                  else
                    echo "No tsconfig.json found, trying to check with Bun directly"
                    bun build --compile "$MAIN_FILE" --outfile /dev/null || true
                  fi
                else
                  echo "No main file found to check, treating as success"
                fi
              fi
              BUILD_EXIT_CODE=$?
              
              # Clean up after build
              echo "Cleaning up build artifacts..."
              rm -rf node_modules dist build .bun
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-specialized-electronjs:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "specialized/with-electronjs"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libx11-dev libxkbfile-dev libsecret-1-dev libgtk-3-dev libnss3 libatk1.0-0 libatk-bridge2.0-0 libgdk-pixbuf2.0-0 libgtk-3-0 libgbm-dev libxss1
      - name: Build projects
        run: |
          set +e
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/package.json" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              
              # Use make command for comprehensive testing if available
              if grep -q '"make"' package.json; then
                echo "Running electron-forge make for comprehensive build..."
                # Run package as a fallback in CI (since make may require more resources)
                yarn package
                MAKE_EXIT_CODE=$?
              elif grep -q '"package"' package.json; then
                echo "Running electron-forge package..."
                yarn package
                MAKE_EXIT_CODE=$?
              elif grep -q '"build"' package.json; then
                echo "Running build script..."
                yarn build
                MAKE_EXIT_CODE=$?
              else
                echo "No build/package/make script found, checking if we can compile..."
                if [ -f "tsconfig.json" ]; then
                  echo "Checking TypeScript compilation..."
                  npx tsc --noEmit
                  MAKE_EXIT_CODE=$?
                else
                  echo "No build method found, treating as success"
                  MAKE_EXIT_CODE=0
                fi
              fi
              
              # Clean up after build
              rm -rf node_modules out dist
              exit $MAKE_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-specialized-jupiter-dex:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "specialized/with-jupiter-dex-api"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/package.json" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              
              # Try to build the project
              if grep -q '"build"' package.json; then
                echo "Running build script..."
                yarn build
                BUILD_EXIT_CODE=$?
              elif [ -f "tsconfig.json" ]; then
                echo "No build script found, checking TypeScript compilation..."
                npx tsc --noEmit
                BUILD_EXIT_CODE=$?
              else
                echo "No build method found, treating as success"
                BUILD_EXIT_CODE=0
              fi
              
              # Clean up after build
              rm -rf node_modules dist build
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-specialized-telegram-web-app:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "specialized/with-telegram-web-app"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/package.json" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              yarn install
              
              # Try to build the project
              if grep -q '"build"' package.json; then
                echo "Running build script..."
                yarn build
                BUILD_EXIT_CODE=$?
              elif [ -f "tsconfig.json" ]; then
                echo "No build script found, checking TypeScript compilation..."
                npx tsc --noEmit
                BUILD_EXIT_CODE=$?
              else
                echo "No build method found, treating as success"
                BUILD_EXIT_CODE=0
              fi
              
              # Clean up after build
              rm -rf node_modules dist build
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi

  build-mobile-react-native:
    runs-on: ubuntu-latest
    env:
      PROJECT_DIR: "mobile/with-react-native"
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.19.0"
      - name: Build projects
        run: |
          set +e
          if [ -d "$PROJECT_DIR" ] && [ -f "$PROJECT_DIR/package.json" ]; then
            PROJECTS=("$PROJECT_DIR")
          else
            PROJECTS=()
            for DIR in "$PROJECT_DIR"/*; do
              if [ -d "$DIR" ] && [ -f "$DIR/package.json" ]; then
                PROJECTS+=("$DIR")
              fi
            done
          fi
          echo "Found $(echo ${#PROJECTS[@]}) projects to build:"
          printf "  - %s\n" "${PROJECTS[@]}"
          touch success_projects.txt
          touch failed_projects.txt
          for PROJECT in "${PROJECTS[@]}"; do
            echo ""
            echo "========================================================"
            echo "Building $PROJECT"
            echo "========================================================"
            (
              cd "$GITHUB_WORKSPACE/$PROJECT"
              # Skip pod install in CI environment
              yarn install --ignore-scripts
              
              # Build the project
              yarn build
              BUILD_EXIT_CODE=$?
              
              # Clean up after build
              rm -rf node_modules
              exit $BUILD_EXIT_CODE
            )
            if [ $? -eq 0 ]; then
              echo "✅ Successfully built $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/success_projects.txt
            else
              echo "❌ Failed to build $PROJECT"
              echo "$PROJECT" >> $GITHUB_WORKSPACE/failed_projects.txt
            fi
          done
          SUCCESSFUL=$(cat success_projects.txt | wc -l)
          FAILED=$(cat failed_projects.txt | wc -l)
          echo ""
          echo "========================================================"
          echo "Build Summary for $PROJECT_DIR"
          echo "========================================================"
          echo "Total projects: ${#PROJECTS[@]}"
          echo "Successful: $SUCCESSFUL"
          echo "Failed: $FAILED"
          if [ "$FAILED" -gt 0 ]; then
            echo "Failed projects:"
            cat failed_projects.txt | sed 's/^/  - /'
            exit 1
          fi
