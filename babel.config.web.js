module.exports = {
  presets: [
    ['@babel/preset-env', { 
      targets: { 
        browsers: ['last 2 versions'],
        node: 'current'
      },
      modules: false,
      useBuiltIns: 'entry',
      corejs: 3
    }],
    ['@babel/preset-react', { 
      runtime: 'automatic',
      development: process.env.NODE_ENV === 'development'
    }],
    ['@babel/preset-typescript', {
      allowNamespaces: true,
      allowDeclareFields: true
    }]
  ],
  plugins: [
    '@babel/plugin-proposal-class-properties',
    '@babel/plugin-proposal-object-rest-spread',
    '@babel/plugin-transform-runtime',
    ['@babel/plugin-transform-react-jsx', {
      runtime: 'automatic'
    }],
    // Para Wallet SDK compatibility
    '@babel/plugin-syntax-dynamic-import',
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator'
  ],
  env: {
    development: {
      plugins: ['react-refresh/babel']
    }
  }
};
